msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-03-13 09:02+0100\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: ko_KR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgctxt "prime_tower_mode description"
msgid "<html>How to generate the prime tower:<ul><li><b>Normal:</b> create a bucket in which secondary materials are primed</li><li><b>Interleaved:</b> create a prime tower as sparse as possible. This will save time and filament, but is only possible if the used materials adhere to each other</li></ul></html>"
msgstr "<html>프라임 타워 생성 방법:<ul><li><b>일반:</b> 보조 재료가 프라이밍되는 버킷을 생성합니다.</li><li><b>중간 삽입:</b> 프라임 타워를 최대한 희박하게 생성합니다. 시간과 필라멘트를 절약하지만, 사용된 재료가 서로 밀착되는 경우에만 가능합니다.</li></ul></html>"

msgctxt "brim_inside_margin description"
msgid "A brim around a model may touch an other model where you don't want it. This removes all brim within this distance from brimless models."
msgstr "모델 주위의 브림은 원하지 않는 다른 모델에 닿을 수 있습니다. 이 설정은 브림이 없는 모델에서 이 거리 내의 모든 브림을 제거합니다."

msgctxt "ironing_inset description"
msgid "A distance to keep from the edges of the model. Ironing all the way to the edge of the mesh may result in a jagged edge on your print."
msgstr "모델 브림에서 떨어서포트 않는 거리. 메쉬 브림까지 다림질하면 출력물의 브림가 고르지 않을 수 있습니다."

msgctxt "material_no_load_move_factor description"
msgid "A factor indicating how much the filament gets compressed between the feeder and the nozzle chamber, used to determine how far to move the material for a filament switch."
msgstr "피더와 노즐 챔버 사이에 필라멘트가 압축되는 양을 나타내는 요소, 필라멘트 전환을 위해 재료를 움직이는 범위를 결정하는 데 사용됨."

msgctxt "roofing_angles description"
msgid "A list of integer line directions to use when the top surface skin layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "상단 표면 스킨 층이 선 또는 지그재그 패턴을 사용할 때 사용할 정수선 방향 리스트. 리스트의 요소는 레이어가 진행됨에 따라 순차적으로 사용되며 리스트의 끝에 도달하면 처음부터 다시 시작됩니다. 리스트 항목은 쉼표로 구분되며 전체 리스트은 대괄호로 묶여 있습니다. 기본값은 전통적인 기본 각도 (45도 및 135도)를 사용하는 빈 리스트입니다."

msgctxt "skin_angles description"
msgid "A list of integer line directions to use when the top/bottom layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "상단/하단 레이어가 선 또는 지그재그 패턴을 사용할 때 사용할 방향 리스트입니다. 리스트의 요소는 레이어가 진행됨에 따라 순차적으로 사용되며 리스트의 끝에 도달하면 처음부터 다시 시작됩니다. 리스트 항목은 쉼표로 구분되며 전체 리스트는 대괄호 안에 들어 있습니다. 기본값은 빈 목록이고, 기본 각도(45도 및 135도)를 사용합니다."

msgctxt "support_infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angle 0 degrees."
msgstr "사용할 정수 선 방향 리스트입니다. 리스트의 요소는 레이어가 진행됨에 따라 순차적으로 사용되며 리스트의 끝에 도달하면 처음부터 다시 시작됩니다. 리스트 항목은 쉼표로 구분되며 전체 리스트는 대괄호 안에 들어 있습니다. 기본값은 빈 목록입니다. 즉 기본 각도인 0도를 사용합니다."

msgctxt "support_bottom_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "사용할 정수 선 방향 리스트입니다. 리스트의 요소는 레이어가 진행됨에 따라 순차적으로 사용되며 리스트의 끝에 도달하면 처음부터 다시 시작됩니다. 리스트 항목은 쉼표로 구분되며 전체 리스트는 대괄호 안에 들어 있습니다. 기본값은 빈 목록입니다. 즉 기본 각도인 0도를 사용합니다(인터페이스가 상당히 두껍거나 90도라면 45도와 135도를 번갈아 사용)."

msgctxt "support_interface_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "사용할 정수 선 방향 리스트입니다. 리스트의 요소는 레이어가 진행됨에 따라 순차적으로 사용되며 리스트의 끝에 도달하면 처음부터 다시 시작됩니다. 리스트 항목은 쉼표로 구분되며 전체 리스트는 대괄호 안에 들어 있습니다. 기본값은 빈 목록입니다. 즉 기본 각도인 0도를 사용합니다(인터페이스가 상당히 두껍거나 90도라면 45도와 135도를 번갈아 사용)."

msgctxt "support_roof_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "사용할 정수 선 방향 리스트입니다. 리스트의 요소는 레이어가 진행됨에 따라 순차적으로 사용되며 리스트의 끝에 도달하면 처음부터 다시 시작됩니다. 리스트 항목은 쉼표로 구분되며 전체 리스트는 대괄호 안에 들어 있습니다. 기본값은 빈 목록입니다. 즉 기본 각도인 0도를 사용합니다(인터페이스가 상당히 두껍거나 90도라면 45도와 135도를 번갈아 사용)."

msgctxt "infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees for the lines and zig zag patterns and 45 degrees for all other patterns)."
msgstr "사용할 라인 방향 리스트. 리스트의 요소는 레이어가 진행됨에 따라 순차적으로 사용되며 리스트의 끝에 도달하면 처음부터 다시 시작됩니다. 리스트 항목은 쉼표로 구분되며 전체 리스트은 대괄호 안에 들어 있습니다. 기본값은 기본 각도 (선 및 지그재그 패턴의 경우 45 및 135도, 다른 모든 패턴의 경우 45도)를 사용하는 빈 리스트입니다."

msgctxt "nozzle_disallowed_areas description"
msgid "A list of polygons with areas the nozzle is not allowed to enter."
msgstr "노즐이 위치할 수 없는 구역의 목록입니다."

msgctxt "machine_disallowed_areas description"
msgid "A list of polygons with areas the print head is not allowed to enter."
msgstr "프린팅 헤드가 위치할 수 없는 구역의 목록입니다."

msgctxt "support_tree_branch_reach_limit description"
msgid "A recomendation to how far branches can move from the points they support. Branches can violate this value to reach their destination (buildplate or a flat part of the model). Lowering this value will make the support more sturdy, but increase the amount of branches (and because of that material usage/print time) "
msgstr "브랜치가 서포트하는 지점에서 뻗어 나가는 거리에 대한 권장 사항입니다. 브랜치는 목적지(빌드 플레이트 또는 모델의 평평한 부분)에 도달하기 위해 이 값을 초과할 수 있습니다. 이 값을 낮추면 서포트가 더 견고해지지만, 브랜치의 양이 늘어나므로 재료 사용량/프린트 시간이 늘어납니다."

msgctxt "extruder_prime_pos_abs label"
msgid "Absolute Extruder Prime Position"
msgstr "독립 익스트루더 프라임 포지션"

msgctxt "adaptive_layer_height_variation label"
msgid "Adaptive Layers Maximum Variation"
msgstr "어댑티브 레이어 최대 변화"

msgctxt "adaptive_layer_height_threshold label"
msgid "Adaptive Layers Topography Size"
msgstr "어댑티브 레이어 지형 크기"

msgctxt "adaptive_layer_height_variation_step label"
msgid "Adaptive Layers Variation Step Size"
msgstr "어댑티브 레이어 변화 단계 크기"

msgctxt "adaptive_layer_height_enabled description"
msgid "Adaptive layers computes the layer heights depending on the shape of the model."
msgstr "어댑티브 레이어는 모델의 모양에 따라 레이어의 높이를 계산합니다."

msgctxt "infill_wall_line_count description"
msgid "Add extra walls around the infill area. Such walls can make top/bottom skin lines sag down less which means you need less top/bottom skin layers for the same quality at the cost of some extra material.\nThis feature can combine with the Connect Infill Polygons to connect all the infill into a single extrusion path without the need for travels or retractions if configured right."
msgstr "내부채움 영역 주변에 여분의 벽을 추가합니다. 이러한 벽은 상단/하단 스킨 라인이 늘어지는 것을 줄여줄 수 있습니다. 일부 여분 재료를 사용해도 같은 품질을 유지하는 데 필요한 필요한 상단/하단 스킨 층이 감소한다는 의미입니다."
"이 기능을 올바르게 구성하는 경우 내부채움 다각형 연결과 함께 사용해 이동 또는 리트랙션없이 모든 내부채움을 단일 돌출 경로에 연결할 수 있습니다."

msgctxt "platform_adhesion description"
msgid "Adhesion"
msgstr "부착"

msgctxt "material_adhesion_tendency label"
msgid "Adhesion Tendency"
msgstr "점착 성항"

msgctxt "skin_overlap description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines, as a percentage of the line widths of the skin lines and the innermost wall. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any percentage over 50% may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "벽과 스킨-센터라인(종점) 사이의 겹침 양을 스킨 라인과 가장 안쪽 벽의 라인 폭 비율로 조정하십시오. 약간의 겹침으로 벽이 스킨에 확실하게 연결될 수 있습니다. 동일한 스킨 및 벽 라인-폭을 고려할 때 비율이 50%가 넘는다면, 그 지점에서 스킨-익스트루더의 노즐 위치가 이미 벽 중앙을 지나 도달할 수 있기 때문에 이미 스킨이 벽을 지나치고 있을 수 있습니다."

msgctxt "skin_overlap_mm description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any value over half the width of the wall may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "벽과 스킨-센터라인(종점) 사이의 겹침 양을 조정하십시오. 약간의 겹침으로 벽이 스킨에 확실하게 연결될 수 있습니다. 동일한 스킨 및 벽 라인-폭을 고려할 때 값이 벽 폭의 절반을 넘는다면, 그 지점에서 스킨-익스트루더의 노즐 위치가 이미 벽 중앙을 지나 도달할 수 있기 때문에 이미 스킨이 벽을 지나치고 있을 수 있습니다."

msgctxt "infill_sparse_density description"
msgid "Adjusts the density of infill of the print."
msgstr "출력물의 내부채움을 조절합니다."

msgctxt "support_interface_density description"
msgid "Adjusts the density of the roofs and floors of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "서포트의 지붕 및 바닥 밀도를 조정합니다. 값이 높을수록 오버행에서 좋지만 서포트를 제거하기가 더 어렵습니다."

msgctxt "support_tree_top_rate description"
msgid "Adjusts the density of the support structure used to generate the tips of the branches. A higher value results in better overhangs, but the supports are harder to remove. Use Support Roof for very high values or ensure support density is similarly high at the top."
msgstr "브랜치의 팁을 생성하는 데 사용되는 서포트 구조의 밀도를 조정합니다. 값이 클수록 오버행은 개선되지만 서포트를 제거하기가 어려워집니다. 매우 큰 값을 위해서는 서포트 지붕을 사용하거나 상단의 서포트 밀도가 비슷하게 높은지 확인합니다."

msgctxt "support_infill_rate description"
msgid "Adjusts the density of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "서포트 구조의 밀도를 조정합니다. 값이 높을수록 오버행이 좋아 서포트만 서포트를 제거하기가 더 어렵습니다."

msgctxt "material_diameter description"
msgid "Adjusts the diameter of the filament used. Match this value with the diameter of the used filament."
msgstr "사용 된 필라멘트의 직경을 조정합니다. 이 값을 사용될 필라멘트의 직경과 일치시킵니다."

msgctxt "support_type description"
msgid "Adjusts the placement of the support structures. The placement can be set to touching build plate or everywhere. When set to everywhere the support structures will also be printed on the model."
msgstr "서포트 구조의 배치를 조정합니다. 배치는 빌드 플레이트 또는 모든 곳을 터치하도록 설정할 수 있습니다. 모든 곳에 설정하면 모델에 서포트 구조가 프린팅됩니다."

msgctxt "prime_tower_wipe_enabled description"
msgid "After printing the prime tower with one nozzle, wipe the oozed material from the other nozzle off on the prime tower."
msgstr "하나의 노즐로 프라임 타워를 프린팅 한 후, 다른 타워의 이물질을 프라임 타워에서 닦아냅니다."

msgctxt "retraction_hop_after_extruder_switch description"
msgid "After the machine switched from one extruder to the other, the build plate is lowered to create clearance between the nozzle and the print. This prevents the nozzle from leaving oozed material on the outside of a print."
msgstr "기기가 하나의 익스트루더에서 다른 익스트루더로 전환 된 후, 빌드 플레이트가 내려가 노즐과 출력물 사이에 간격이 생깁니다. 이렇게 하면 프린트 물 바깥쪽에서 노즐로 부터 필라멘트가 흐르는 것을 방지합니다."

msgctxt "retraction_combing option all"
msgid "All"
msgstr "모두"

msgctxt "print_sequence option all_at_once"
msgid "All at Once"
msgstr "모두 한꺼번에"

msgctxt "resolution description"
msgid "All settings that influence the resolution of the print. These settings have a large impact on the quality (and print time)"
msgstr "출력물의 해상도에 영향을 미치는 모든 설정. 이러한 설정은 품질 (및 프린팅 시간)에 큰 영향을 미칩니다."

msgctxt "user_defined_print_order_enabled description"
msgid "Allows you to order the object list to manually set the print sequence. First object from the list will be printed first."
msgstr "객체 목록의 순서를 지정하여 출력 순서를 수동으로 설정할 수 있도록 허용합니다. 목록의 첫 번째 객체가 먼저 출력됩니다."

msgctxt "alternate_extra_perimeter label"
msgid "Alternate Extra Wall"
msgstr "대체 여분 벽"

msgctxt "alternate_carve_order label"
msgid "Alternate Mesh Removal"
msgstr "대체 메쉬 제거"

msgctxt "material_alternate_walls label"
msgid "Alternate Wall Directions"
msgstr "벽 방향 대체"

msgctxt "material_alternate_walls description"
msgid "Alternate wall directions every other layer and inset. Useful for materials that can build up stress, like for metal printing."
msgstr "다른 레이어마다 벽 방향을 대체하고 삽입합니다. 금속 프린팅의 경우와 같이 응력을 증강시킬 수 있는 재료에 유용."

msgctxt "machine_buildplate_type option aluminum"
msgid "Aluminum"
msgstr "알루미늄"

msgctxt "machine_always_write_active_tool label"
msgid "Always Write Active Tool"
msgstr "항상 활성 도구 쓰기"

msgctxt "travel_retract_before_outer_wall description"
msgid "Always retract when moving to start an outer wall."
msgstr "외벽을 프린팅하기 위해 이동할 때 항상 리트렉션합니다."

msgctxt "xy_offset description"
msgid "Amount of offset applied to all polygons in each layer. Positive values can compensate for too big holes; negative values can compensate for too small holes."
msgstr "각 레이어의 모든 다각형에 적용된 오프셋의 양입니다. 양수 값은 아주 큰 구멍을 보완 할 수 있습니다. 음수 값은 아주 작은 구멍을 보완 할 수 있습니다."

msgctxt "xy_offset_layer_0 description"
msgid "Amount of offset applied to all polygons in the first layer. A negative value can compensate for squishing of the first layer known as \"elephant's foot\"."
msgstr "첫 번째 레이어의 모든 다각형에 적용된 오프셋의 양입니다. 음수 값은 \"elephant's foot\"이라고 알려진 현상을 보완 할 수 있습니다."

msgctxt "support_offset description"
msgid "Amount of offset applied to all support polygons in each layer. Positive values can smooth out the support areas and result in more sturdy support."
msgstr "각 레이어의 모든 서포트 다각형에 적용된 오프셋의 양입니다. 양수 값을 사용하면 서포트 영역이 원활 해지며 보다 견고한 서포트가 됩니다."

msgctxt "support_bottom_offset description"
msgid "Amount of offset applied to the floors of the support."
msgstr "서포트 바닥에 적용되는 오프셋 양."

msgctxt "support_roof_offset description"
msgid "Amount of offset applied to the roofs of the support."
msgstr "서포트 지붕에 적용되는 오프셋 양."

msgctxt "support_interface_offset description"
msgid "Amount of offset applied to the support interface polygons."
msgstr "서포트 인터페이스 영역에 적용되는 오프셋 양."

msgctxt "wipe_retraction_amount description"
msgid "Amount to retract the filament so it does not ooze during the wipe sequence."
msgstr "필라멘트를 리트렉션하는 양으로 와이프 순서 동안 새어 나오지 않습니다."

msgctxt "sub_div_rad_add description"
msgid "An addition to the radius from the center of each cube to check for the boundary of the model, as to decide whether this cube should be subdivided. Larger values lead to a thicker shell of small cubes near the boundary of the model."
msgstr "모델의 경계를 확인하기 위해 각 큐브의 중심에서 반경을 더하여 이 큐브를 세분화할지 여부를 결정합니다. 값이 클수록 모델의 경계 근처에 작은 큐브가 더 두껍게 나옵니다."

msgctxt "anti_overhang_mesh label"
msgid "Anti Overhang Mesh"
msgstr "안티 오버행 메쉬"

msgctxt "material_anti_ooze_retracted_position label"
msgid "Anti-ooze Retracted Position"
msgstr "흐름 방지 리트랙션 위치"

msgctxt "material_anti_ooze_retraction_speed label"
msgid "Anti-ooze Retraction Speed"
msgstr "흐름 방지 리트랙션 속도"

msgctxt "machine_use_extruder_offset_to_offset_coords description"
msgid "Apply the extruder offset to the coordinate system. Affects all extruders."
msgstr "익스트루더 오프셋을 좌표계에 적용하십시오. 모든 익스트루더에 적용됩니다."

msgctxt "interlocking_enable description"
msgid "At the locations where models touch, generate an interlocking beam structure. This improves the adhesion between models, especially models printed in different materials."
msgstr "모델이 닿는 위치에 연동 빔 구조를 생성합니다. 이렇게 하면 모델, 특히 다른 재료로 프린트된 모델 간의 접착력이 개선됩니다."

msgctxt "travel_avoid_other_parts label"
msgid "Avoid Printed Parts When Traveling"
msgstr "움직일 때 프린팅한 부분을 피하기"

msgctxt "travel_avoid_supports label"
msgid "Avoid Supports When Traveling"
msgstr "이동하는 경우 지지대 피함"

msgctxt "z_seam_position option back"
msgid "Back"
msgstr "뒤로"

msgctxt "z_seam_position option backleft"
msgid "Back Left"
msgstr "후면 왼쪽"

msgctxt "z_seam_position option backright"
msgid "Back Right"
msgstr "후면 오른쪽"

msgctxt "machine_gcode_flavor option BFB"
msgid "Bits from Bytes"
msgstr "Bits from Bytes"

msgctxt "magic_mesh_surface_mode option both"
msgid "Both"
msgstr "모두"

msgctxt "support_interface_priority option nothing"
msgid "Both overlap"
msgstr "둘 다 겹침"

msgctxt "bottom_layers label"
msgid "Bottom Layers"
msgstr "하단 레이어"

msgctxt "top_bottom_pattern_0 label"
msgid "Bottom Pattern Initial Layer"
msgstr "하단 패턴 초기 레이어"

msgctxt "bottom_skin_expand_distance label"
msgid "Bottom Skin Expand Distance"
msgstr "밑면 스킨 확장 거리"

msgctxt "bottom_skin_preshrink label"
msgid "Bottom Skin Removal Width"
msgstr "밑면 스킨 제거 폭"

msgctxt "bottom_thickness label"
msgid "Bottom Thickness"
msgstr "바닥 두께"

msgctxt "support_tree_top_rate label"
msgid "Branch Density"
msgstr "브랜치 밀도"

msgctxt "support_tree_branch_diameter label"
msgid "Branch Diameter"
msgstr "브랜치 직경"

msgctxt "support_tree_branch_diameter_angle label"
msgid "Branch Diameter Angle"
msgstr "브랜치 직경 각도"

msgctxt "material_break_preparation_retracted_position label"
msgid "Break Preparation Retracted Position"
msgstr "파단 준비 리트랙션 위치"

msgctxt "material_break_preparation_speed label"
msgid "Break Preparation Retraction Speed"
msgstr "파단 준비 리트랙션 속도"

msgctxt "material_break_preparation_temperature label"
msgid "Break Preparation Temperature"
msgstr "준비 온도 파단"

msgctxt "material_break_retracted_position label"
msgid "Break Retracted Position"
msgstr "파단 리트랙션 위치"

msgctxt "material_break_speed label"
msgid "Break Retraction Speed"
msgstr "파단 리트랙션 속도"

msgctxt "material_break_temperature label"
msgid "Break Temperature"
msgstr "파단 온도"

msgctxt "support_skip_some_zags label"
msgid "Break Up Support In Chunks"
msgstr "Chunk에서 서포트 중단"

msgctxt "bridge_fan_speed label"
msgid "Bridge Fan Speed"
msgstr "브릿지 팬 속도"

msgctxt "bridge_enable_more_layers label"
msgid "Bridge Has Multiple Layers"
msgstr "여러 개의 레이어가있는 브릿지"

msgctxt "bridge_skin_density_2 label"
msgid "Bridge Second Skin Density"
msgstr "브리지 두 번째 스킨 밀도"

msgctxt "bridge_fan_speed_2 label"
msgid "Bridge Second Skin Fan Speed"
msgstr "브릿지 두번째 스킨 팬 속도"

msgctxt "bridge_skin_material_flow_2 label"
msgid "Bridge Second Skin Flow"
msgstr "브리지 두 번째 스킨 압출량"

msgctxt "bridge_skin_speed_2 label"
msgid "Bridge Second Skin Speed"
msgstr "브릿지 두번째 스킨 속도"

msgctxt "bridge_skin_density label"
msgid "Bridge Skin Density"
msgstr "브릿지 스킨 밀도"

msgctxt "bridge_skin_material_flow label"
msgid "Bridge Skin Flow"
msgstr "브리지 스킨 압출량"

msgctxt "bridge_skin_speed label"
msgid "Bridge Skin Speed"
msgstr "브릿지 스킨 속도"

msgctxt "bridge_skin_support_threshold label"
msgid "Bridge Skin Support Threshold"
msgstr "브릿지 스킨 서포트 임계값"

msgctxt "bridge_sparse_infill_max_density label"
msgid "Bridge Sparse Infill Max Density"
msgstr "브리지의 희박한 내부채움 최대 밀도"

msgctxt "bridge_skin_density_3 label"
msgid "Bridge Third Skin Density"
msgstr "브릿지 세번째 스킨 밀도"

msgctxt "bridge_fan_speed_3 label"
msgid "Bridge Third Skin Fan Speed"
msgstr "브릿지 세번째 스킨 팬 속도"

msgctxt "bridge_skin_material_flow_3 label"
msgid "Bridge Third Skin Flow"
msgstr "브리지 세 번째 스킨 압출량"

msgctxt "bridge_skin_speed_3 label"
msgid "Bridge Third Skin Speed"
msgstr "브릿지 세번째 스킨 속도"

msgctxt "bridge_wall_coast label"
msgid "Bridge Wall Coasting"
msgstr "브릿지 벽 코스팅(Coasting)"

msgctxt "bridge_wall_material_flow label"
msgid "Bridge Wall Flow"
msgstr "브리지 벽 압출량"

msgctxt "bridge_wall_speed label"
msgid "Bridge Wall Speed"
msgstr "브릿지 벽 속도"

msgctxt "adhesion_type option brim"
msgid "Brim"
msgstr "브림"

msgctxt "brim_inside_margin label"
msgid "Brim Avoid Margin"
msgstr "브림의 여백 회피"

msgctxt "brim_gap label"
msgid "Brim Distance"
msgstr "브림 거리"

msgctxt "brim_line_count label"
msgid "Brim Line Count"
msgstr "브림 선 수"

msgctxt "brim_location label"
msgid "Brim Location"
msgstr "브림 위치"

msgctxt "brim_replaces_support label"
msgid "Brim Replaces Support"
msgstr "브림이 서포트를 대체"

msgctxt "brim_width label"
msgid "Brim Width"
msgstr "브림 너비"

msgctxt "platform_adhesion label"
msgid "Build Plate Adhesion"
msgstr "빌드 플레이트 부착"

msgctxt "adhesion_extruder_nr label"
msgid "Build Plate Adhesion Extruder"
msgstr "빌드 플레이트 고정 익스트루더"

msgctxt "adhesion_type label"
msgid "Build Plate Adhesion Type"
msgstr "빌드 플레이트 고정 유형"

msgctxt "machine_buildplate_type label"
msgid "Build Plate Material"
msgstr "빌드 플레이트 재질"

msgctxt "machine_shape label"
msgid "Build Plate Shape"
msgstr "빌드 플레이트 모양"

msgctxt "material_bed_temperature label"
msgid "Build Plate Temperature"
msgstr "빌드 플레이트 온도"

msgctxt "material_bed_temperature_layer_0 label"
msgid "Build Plate Temperature Initial Layer"
msgstr "초기 레이어의 빌드 플레이트 온도"

msgctxt "build_volume_temperature label"
msgid "Build Volume Temperature"
msgstr "빌드 볼륨 온도"

msgctxt "bv_temp_anomaly_limit label"
msgid "Build Volume temperature Limit"
msgstr "빌드 볼륨 온도 제한"

msgctxt "bv_temp_warn_limit label"
msgid "Build Volume temperature Warning"
msgstr "빌드 볼륨 온도 경고"

msgctxt "prime_tower_brim_enable description"
msgid "By enabling this setting, your prime-tower will get a brim, even if the model doesn't. If you want a sturdier base for a high tower, you can increase the base height."
msgstr "이 설정을 활성화하면 모델에 브림이 없더라도 프라임 타워에는 브림이 생성됩니다. 높은 타워의 튼튼한 베이스가 필요하다면 베이스 높이를 늘릴 수 있습니다."

msgctxt "center_object label"
msgid "Center Object"
msgstr "가운데 객체"

msgctxt "conical_overhang_enabled description"
msgid "Change the geometry of the printed model such that minimal support is required. Steep overhangs will become shallow overhangs. Overhanging areas will drop down to become more vertical."
msgstr "최소한의 서포트가 필요하도록 프린팅 된 모델의 형상을 변경합니다. 가파른 오버행은 얕은 오버행이됩니다. 오버행 영역이 더 수직으로 떨어집니다."

msgctxt "support_structure description"
msgid "Chooses between the techniques available to generate support. \"Normal\" support creates a support structure directly below the overhanging parts and drops those areas straight down. \"Tree\" support creates branches towards the overhanging areas that support the model on the tips of those branches, and allows the branches to crawl around the model to support it from the build plate as much as possible."
msgstr "서포트를 생성하는 데 사용할 수 있는 기술 중 하나를 선택합니다. '표준' 서포트는 오버행(경사면) 파트 바로 아래에 서포트 구조물을 생성하고 해당 영역을 바로 아래로 떨어뜨립니다. '트리' 서포트는 모델을 지지하는 브랜치 끝에서 오버행(경사면) 영역을 향해 브랜치를 만들고 빌드 플레이트에서 모델을 지지할 수 있도록 모델을 브랜치로 최대한 감쌉니다."

msgctxt "coasting_speed label"
msgid "Coasting Speed"
msgstr "코스팅(Coasting) 속도"

msgctxt "coasting_volume label"
msgid "Coasting Volume"
msgstr "코스팅(Coasting) 양"

msgctxt "coasting_enable description"
msgid "Coasting replaces the last part of an extrusion path with a travel path. The oozed material is used to print the last piece of the extrusion path in order to reduce stringing."
msgstr "코스팅(Coasting)은 압출 경로의 마지막 부분을 이동 경로로 바꿉니다. 누출된 재료는 스트링을 줄이기 위해 압출 경로의 마지막 부분을 프린팅하는 데 사용됩니다."

msgctxt "retraction_combing label"
msgid "Combing Mode"
msgstr "Combing 모드"

msgctxt "retraction_combing description"
msgid "Combing keeps the nozzle within already printed areas when traveling. This results in slightly longer travel moves but reduces the need for retractions. If combing is off, the material will retract and the nozzle moves in a straight line to the next point. It is also possible to avoid combing over top/bottom skin areas or to only comb within the infill."
msgstr "Combing은 이동할 때 이미 인쇄 된 영역 내에 노즐을 유지합니다. 이로 인해 이동이 약간 더 길어 지지만 리트렉션의 필요성은 줄어듭니다. Combing이 꺼져 있으면 재료가 후퇴하고 노즐이 직선으로 다음 점으로 이동합니다. 또한 상단/하단 스킨 영역을 Combing하거나 내부채움 내에서만 빗질하는 것을 피할 수 있습니다."

msgctxt "command_line_settings label"
msgid "Command Line Settings"
msgstr "커맨드 라인 설정"

msgctxt "infill_pattern option concentric"
msgid "Concentric"
msgstr "동심원"

msgctxt "ironing_pattern option concentric"
msgid "Concentric"
msgstr "동심원"

msgctxt "roofing_pattern option concentric"
msgid "Concentric"
msgstr "동심원의"

msgctxt "support_bottom_pattern option concentric"
msgid "Concentric"
msgstr "동심원의"

msgctxt "support_interface_pattern option concentric"
msgid "Concentric"
msgstr "동심원의"

msgctxt "support_pattern option concentric"
msgid "Concentric"
msgstr "동심원의"

msgctxt "support_roof_pattern option concentric"
msgid "Concentric"
msgstr "동심원의"

msgctxt "top_bottom_pattern option concentric"
msgid "Concentric"
msgstr "동심원 형태"

msgctxt "top_bottom_pattern_0 option concentric"
msgid "Concentric"
msgstr "동심원의"

msgctxt "support_conical_angle label"
msgid "Conical Support Angle"
msgstr "원추서포트 각"

msgctxt "support_conical_min_width label"
msgid "Conical Support Minimum Width"
msgstr "원뿔형 서포트 최소 너비"

msgctxt "zig_zaggify_infill label"
msgid "Connect Infill Lines"
msgstr "내부채움 선 연결"

msgctxt "connect_infill_polygons label"
msgid "Connect Infill Polygons"
msgstr "내부채움 다각형 연결"

msgctxt "zig_zaggify_support label"
msgid "Connect Support Lines"
msgstr "서포트 선 연결"

msgctxt "support_connect_zigzags label"
msgid "Connect Support ZigZags"
msgstr "ZigZags 서포트 연결"

msgctxt "connect_skin_polygons label"
msgid "Connect Top/Bottom Polygons"
msgstr "상단/하단 다각형 연결"

msgctxt "connect_infill_polygons description"
msgid "Connect infill paths where they run next to each other. For infill patterns which consist of several closed polygons, enabling this setting greatly reduces the travel time."
msgstr "스킨 경로가 나란히 이어지는 내부채움 경로를 연결합니다. 여러 개의 폐다각형으로 구성되는 내부채움 패턴의 경우 이 설정을 사용하면 이동 시간이 크게 감소합니다."

msgctxt "support_connect_zigzags description"
msgid "Connect the ZigZags. This will increase the strength of the zig zag support structure."
msgstr "지그재그를 연결하십시오. 이것은 지그재그 서포트 구조의 강도를 증가시킵니다."

msgctxt "zig_zaggify_support description"
msgid "Connect the ends of the support lines together. Enabling this setting can make your support more sturdy and reduce underextrusion, but it will cost more material."
msgstr "서포트의 끝을 서로 연결하십시오. 이 설정을 사용하면 서포트가 보다 견고해지지만 더 많은 재료가 소모됩니다."

msgctxt "zig_zaggify_infill description"
msgid "Connect the ends where the infill pattern meets the inner wall using a line which follows the shape of the inner wall. Enabling this setting can make the infill adhere to the walls better and reduce the effects of infill on the quality of vertical surfaces. Disabling this setting reduces the amount of material used."
msgstr "내벽의 형태를 따라가는 선을 사용하여 내부채움 패턴과 내벽이 만나는 끝을 연결합니다. 이 설정을 사용하면 내부채움이 벽에 더 잘 붙게되어 내부채움이 수직면의 품질에 미치는 영향을 줄일 수 있습니다. 이 설정을 해제하면 사용되는 재료의 양이 줄어듭니다."

msgctxt "connect_skin_polygons description"
msgid "Connect top/bottom skin paths where they run next to each other. For the concentric pattern enabling this setting greatly reduces the travel time, but because the connections can happen midway over infill this feature can reduce the top surface quality."
msgstr "스킨 경로가 나란히 이어지는 상단/하단 스킨 경로를 연결합니다. 동심원 패턴의 경우 이 설정을 사용하면 이동 시간이 크게 감소하지만, 내부채움의 중간에 연결될 수 있기 때문에 이 기능은 상단 표면 품질을 저하시킬 수 있습니다."

msgctxt "z_seam_corner description"
msgid "Control whether corners on the model outline influence the position of the seam. None means that corners have no influence on the seam position. Hide Seam makes the seam more likely to occur on an inside corner. Expose Seam makes the seam more likely to occur on an outside corner. Hide or Expose Seam makes the seam more likely to occur at an inside or outside corner. Smart Hiding allows both inside and outside corners, but chooses inside corners more frequently, if appropriate."
msgstr "모델 외곽선의 모서리가 이음선의 위치에 영향을 주는지 여부를 제어합니다. 이것은 모서리가 이음선 위치에 영향을 미치지 않는다는 것을 의미하지 않습니다. 이음선 숨김은 이음선이 안쪽 모서리에서 발생할 가능성을 높입니다. 이음선 노출은 이음선이 외부 모서리에서 발생할 가능성을 높입니다. 이음선 숨김 또는 노출은 이음선이 내부나 외부 모서리에서 발생할 가능성을 높입니다. 스마트 숨김은 내외부 모서리 모두 가능하지만, 적절하다면 내부 모서리를 더욱 빈번하게 선택합니다."

msgctxt "infill_multiplier description"
msgid "Convert each infill line to this many lines. The extra lines do not cross over each other, but avoid each other. This makes the infill stiffer, but increases print time and material usage."
msgstr "각 내부채움 선을 여러 개의 선으로 변환합니다. 추가되는 선은 다른 선을 교차하지 않고, 다른 선을 피해 변환됩니다. 내부채움을 빽빽하게 만들지만, 인쇄 및 재료 사용이 증가합니다."

msgctxt "machine_nozzle_cool_down_speed label"
msgid "Cool Down Speed"
msgstr "냉각 속도"

msgctxt "cooling description"
msgid "Cooling"
msgstr "냉각"

msgctxt "cooling label"
msgid "Cooling"
msgstr "냉각"

msgctxt "infill_pattern option cross"
msgid "Cross"
msgstr "십자형"

msgctxt "support_pattern option cross"
msgid "Cross"
msgstr "십자"

msgctxt "infill_pattern option cross_3d"
msgid "Cross 3D"
msgstr "십자형 3D"

msgctxt "cross_infill_pocket_size label"
msgid "Cross 3D Pocket Size"
msgstr "크로스 3D 포켓 크기"

msgctxt "cross_support_density_image label"
msgid "Cross Fill Density Image for Support"
msgstr "지지대에 대한 교차 충진 밀도 이미지"

msgctxt "cross_infill_density_image label"
msgid "Cross Infill Density Image"
msgstr "교차 충진 밀도 이미지"

msgctxt "material_crystallinity label"
msgid "Crystalline Material"
msgstr "결정형 소재"

msgctxt "infill_pattern option cubic"
msgid "Cubic"
msgstr "입방체"

msgctxt "infill_pattern option cubicsubdiv"
msgid "Cubic Subdivision"
msgstr "입방체 세분"

msgctxt "sub_div_rad_add label"
msgid "Cubic Subdivision Shell"
msgstr "입방 세분 쉘"

msgctxt "cutting_mesh label"
msgid "Cutting Mesh"
msgstr "커팅 메쉬"

msgctxt "material_flow_temp_graph description"
msgid "Data linking material flow (in mm3 per second) to temperature (degrees Celsius)."
msgstr "재료 공급 데이터 (mm3 / 초) - 온도 (섭씨)."

msgctxt "machine_acceleration label"
msgid "Default Acceleration"
msgstr "기본 가속도"

msgctxt "default_material_bed_temperature label"
msgid "Default Build Plate Temperature"
msgstr "기본 빌드 플레이트 온도"

msgctxt "machine_max_jerk_e label"
msgid "Default Filament Jerk"
msgstr "기본 필라멘트 Jerk"

msgctxt "default_material_print_temperature label"
msgid "Default Printing Temperature"
msgstr "기본 프린팅 온도"

msgctxt "machine_max_jerk_xy label"
msgid "Default X-Y Jerk"
msgstr "기본 X-Y Jerk"

msgctxt "machine_max_jerk_z label"
msgid "Default Z Jerk"
msgstr "기본 Z Jerk"

msgctxt "machine_max_jerk_xy description"
msgid "Default jerk for movement in the horizontal plane."
msgstr "수평면에서의 이동을 위한 기본 Jerk."

msgctxt "machine_max_jerk_z description"
msgid "Default jerk for the motor of the Z-direction."
msgstr "Z 방향 모터의 기본 Jerk."

msgctxt "machine_max_jerk_e description"
msgid "Default jerk for the motor of the filament."
msgstr "필라멘트를 구동하는 모터의 기본 Jerk."

msgctxt "bridge_settings_enabled description"
msgid "Detect bridges and modify print speed, flow and fan settings while bridges are printed."
msgstr "브릿지가 출력되는 중에 브리지를 감지하고 인쇄 속도, 흐름 및 팬 설정을 수정합니다."

msgctxt "inset_direction description"
msgid "Determines the order in which walls are printed. Printing outer walls earlier helps with dimensional accuracy, as faults from inner walls cannot propagate to the outside. However printing them later allows them to stack better when overhangs are printed. When there is an uneven amount of total innner walls, the 'center last line' is always printed last."
msgstr "벽이 프린팅되는 순서를 정의합니다. 외벽을 먼저 프린팅하면 내벽의 오류가 외부로 전파될 수 없으므로 치수 정확도가 향상됩니다. 그러나 나중에 프린팅하면 오버행(경사면)이 프린트팅 될 때 더 잘 쌓일 수 있습니다. 전체 내벽의 총량이 불균일할 경우, '가운데 마지막 선'이 항상 마지막에 인쇄됩니다."

msgctxt "infill_mesh_order description"
msgid "Determines the priority of this mesh when considering multiple overlapping infill meshes. Areas where multiple infill meshes overlap will take on the settings of the mesh with the highest rank. An infill mesh with a higher rank will modify the infill of infill meshes with lower rank and normal meshes."
msgstr "여러 내부채움 매쉬 오버랩을 고려할 때 메쉬의 우선 순위를 결정합니다. 여러 내부채움 메쉬가 오버랩하는 영역은 최고 랭크의 메쉬 설정에 착수하게 됩니다. 높은 내부채움 메쉬는 낮은 내부채움 메쉬와 표준 메쉬의 내부채움을 수정합니다."

msgctxt "lightning_infill_support_angle description"
msgid "Determines when a lightning infill layer has to support anything above it. Measured in the angle given the thickness of a layer."
msgstr "라이트닝 내부채움 레이어가 그 위에 있는 것을 서포트해야 할 부분을 결정합니다. 레이어 두께가 주어진 각도로 측정됩니다."

msgctxt "lightning_infill_overhang_angle description"
msgid "Determines when a lightning infill layer has to support the model above it. Measured in the angle given the thickness."
msgstr "라이트닝 내부채움 레이어가 레이어 위에 있는 모델을 서포트해야 할 부분을 결정합니다. 두께가 주어진 각도로 측정됩니다."

msgctxt "material_diameter label"
msgid "Diameter"
msgstr "직경"

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model label"
msgid "Diameter Increase To Model"
msgstr "모델에 대한 직경 증가"

msgctxt "support_tree_bp_diameter description"
msgid "Diameter every branch tries to achieve when reaching the buildplate. Improves bed adhesion."
msgstr "모든 브랜치가 빌드 플레이트에 도달할 때 달성하려고 하는 직경입니다. 이에 따라 베드 접착력이 개선됩니다."

msgctxt "adhesion_type description"
msgid "Different options that help to improve both priming your extrusion and adhesion to the build plate. Brim adds a single layer flat area around the base of your model to prevent warping. Raft adds a thick grid with a roof below the model. Skirt is a line printed around the model, but not connected to the model."
msgstr "빌드 플레이트에 대한 접착력을 향상시키는 데 도움이되는 다양한 옵션. 브림은 뒤틀림을 방지하기 위해 모델 바닥 주위에 단층 평면 영역을 추가합니다. 래프트는 모델 아래에 지붕이있는 두꺼운 격자를 추가합니다. 스커트는 모델 주변에 프린팅 된 선이지만 모델에는 연결되어 있지 않습니다."

msgctxt "machine_disallowed_areas label"
msgid "Disallowed Areas"
msgstr "허용되지 않는 지역"

msgctxt "infill_line_distance description"
msgid "Distance between the printed infill lines. This setting is calculated by the infill density and the infill line width."
msgstr "프린팅 된 내부채움 선 사이의 거리. 이 설정은 내부채움 밀도 및 내부채움 선 너비로 계산됩니다."

msgctxt "support_initial_layer_line_distance description"
msgid "Distance between the printed initial layer support structure lines. This setting is calculated by the support density."
msgstr "인쇄된 초기 레이어 서포트 구조 선 사이의 거리. 이 설정은 서포트 밀도로 계산됩니다."

msgctxt "support_bottom_line_distance description"
msgid "Distance between the printed support floor lines. This setting is calculated by the Support Floor Density, but can be adjusted separately."
msgstr "프린팅 된 서포트 플로어 사이의 거리. 이 설정은 서포트 바닥 밀도로 계산되지만 별도로 조정할 수 있습니다."

msgctxt "support_roof_line_distance description"
msgid "Distance between the printed support roof lines. This setting is calculated by the Support Roof Density, but can be adjusted separately."
msgstr "프린팅 된 지붕 루프 사이의 거리. 이 설정은 서포트 지붕 밀도에 의해 계산되지만 별도로 조정할 수 있습니다."

msgctxt "support_line_distance description"
msgid "Distance between the printed support structure lines. This setting is calculated by the support density."
msgstr "프린팅 된 서포트 구조 선 사이의 거리. 이 설정은 서포트 밀도로 계산됩니다."

msgctxt "support_bottom_distance description"
msgid "Distance from the print to the bottom of the support. Note that this is rounded up to the next layer height."
msgstr "프린트에서 서포트 바닥까지의 거리입니다. 이는 다음 레이어 높이로 반올림됩니다."

msgctxt "support_top_distance description"
msgid "Distance from the top of the support to the print."
msgstr "서포트 상단에서 프린팅까지의 거리."

msgctxt "support_z_distance description"
msgid "Distance from the top/bottom of the support structure to the print. This gap provides clearance to remove the supports after the model is printed. The topmost support layer below the model might be a fraction of regular layers."
msgstr "서포트 구조물의 상단/하단에서 프린트까지의 거리입니다. 이 공간은 모델이 인쇄된 후 서포트를 제거할 수 있도록 여유를 제공합니다. 모델 아래 최상위 서포트 레이어는 정규 레이어의 일부일 수 있습니다."

msgctxt "infill_wipe_dist description"
msgid "Distance of a travel move inserted after every infill line, to make the infill stick to the walls better. This option is similar to infill overlap, but without extrusion and only on one end of the infill line."
msgstr "각 내부채움 라인 다음에 삽입 된 이동 거리. 내부채움 스틱을 벽에 더 잘 붙게 합니다. 이 옵션은 내부채움 겹침과 유사하지만 압출이 없고 충전 선의 한쪽 끝에서만 사용됩니다."

msgctxt "wall_0_wipe_dist description"
msgid "Distance of a travel move inserted after the outer wall, to hide the Z seam better."
msgstr "Z 층의 경계면을 더 잘 가리기 위해 바깥쪽 벽 뒤에 삽입되어 이동한 거리."

msgctxt "draft_shield_dist description"
msgid "Distance of the draft shield from the print, in the X/Y directions."
msgstr "X/Y 방향으로 프린트와 드래프트 쉴드까지의 거리입니다."

msgctxt "ooze_shield_dist description"
msgid "Distance of the ooze shield from the print, in the X/Y directions."
msgstr "X/Y 방향으로 출력물에서 Ooze 쉴드까지의 거리."

msgctxt "support_xy_distance_overhang description"
msgid "Distance of the support structure from the overhang in the X/Y directions."
msgstr "X/Y 방향에서 오버행으로부터 서포트까지의 거리."

msgctxt "support_xy_distance description"
msgid "Distance of the support structure from the print in the X/Y directions."
msgstr "X/Y 방향에서 출력물로과 서포트까지의 거리."

msgctxt "meshfix_fluid_motion_shift_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr "경로를 평활화하기 위해 거리 지점이 이동됩니다"

msgctxt "meshfix_fluid_motion_small_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr "경로를 평활화하기 위해 거리 지점이 이동됩니다"

msgctxt "min_infill_area description"
msgid "Don't generate areas of infill smaller than this (use skin instead)."
msgstr "이보다 작은 내부채움 영역을 생성하지 마십시오 (대신 스킨 사용)."

msgctxt "draft_shield_height label"
msgid "Draft Shield Height"
msgstr "드래프트 쉴드 높이"

msgctxt "draft_shield_height_limitation label"
msgid "Draft Shield Limitation"
msgstr "드래프트 쉴드 제한"

msgctxt "draft_shield_dist label"
msgid "Draft Shield X/Y Distance"
msgstr "드래프트 쉴드 X/Y 거리"

msgctxt "support_mesh_drop_down label"
msgid "Drop Down Support Mesh"
msgstr "드롭 다운 서포트 메쉬"

msgctxt "dual label"
msgid "Dual Extrusion"
msgstr "이중 압출"

msgctxt "machine_shape option elliptic"
msgid "Elliptic"
msgstr "타원"

msgctxt "acceleration_enabled label"
msgid "Enable Acceleration Control"
msgstr "가속 제어 활성화"

msgctxt "bridge_settings_enabled label"
msgid "Enable Bridge Settings"
msgstr "브릿지 설정 사용"

msgctxt "coasting_enable label"
msgid "Enable Coasting"
msgstr "코스팅(Coasting) 사용"

msgctxt "support_conical_enabled label"
msgid "Enable Conical Support"
msgstr "원추형 서포트 사용"

msgctxt "draft_shield_enabled label"
msgid "Enable Draft Shield"
msgstr "드래프트 쉴드 사용"

msgctxt "meshfix_fluid_motion_enabled label"
msgid "Enable Fluid Motion"
msgstr "플루이드 모션 활성화"

msgctxt "ironing_enabled label"
msgid "Enable Ironing"
msgstr "다림질 사용"

msgctxt "jerk_enabled label"
msgid "Enable Jerk Control"
msgstr "Jerk 컨트롤 사용"

msgctxt "machine_nozzle_temp_enabled label"
msgid "Enable Nozzle Temperature Control"
msgstr "노즐 온도 조절 활성화"

msgctxt "ooze_shield_enabled label"
msgid "Enable Ooze Shield"
msgstr "Ooze 쉴드 사용"

msgctxt "prime_blob_enable label"
msgid "Enable Prime Blob"
msgstr "프라임 블롭 활성화"

msgctxt "prime_tower_enable label"
msgid "Enable Prime Tower"
msgstr "프라임 타워 사용"

msgctxt "cool_fan_enabled label"
msgid "Enable Print Cooling"
msgstr "프린팅 냉각 사용"

msgctxt "ppr_enable label"
msgid "Enable Print Process Reporting"
msgstr "출력 과정 보고 활성화"

msgctxt "retraction_enable label"
msgid "Enable Retraction"
msgstr "리트렉션 활성화"

msgctxt "support_brim_enable label"
msgid "Enable Support Brim"
msgstr "서포트 브림 사용"

msgctxt "support_bottom_enable label"
msgid "Enable Support Floor"
msgstr "서포트 바닥 사용"

msgctxt "support_interface_enable label"
msgid "Enable Support Interface"
msgstr "서포트 인터페이스 사용"

msgctxt "support_roof_enable label"
msgid "Enable Support Roof"
msgstr "서포트 지붕 사용"

msgctxt "acceleration_travel_enabled label"
msgid "Enable Travel Acceleration"
msgstr "이동 가속 활성화"

msgctxt "jerk_travel_enabled label"
msgid "Enable Travel Jerk"
msgstr "이동 저크 활성화"

msgctxt "ooze_shield_enabled description"
msgid "Enable exterior ooze shield. This will create a shell around the model which is likely to wipe a second nozzle if it's at the same height as the first nozzle."
msgstr "Ooze 쉴드를 활성화. 이렇게하면 첫 번째 노즐과 동일한 높이에 두 번째 노즐을 닦을 가능성이 있는 모델 주위에 쉘이 생깁니다."

msgctxt "ppr_enable description"
msgid "Enable print process reporting for setting threshold values for possible fault detection."
msgstr "발생할 수 있는 오류를 감지하도록 임곗값 설정을 위한 출력 과정 보고를 활성화합니다."

msgctxt "small_skin_on_surface description"
msgid "Enable small (up to 'Small Top/Bottom Width') regions on the topmost skinned layer (exposed to air) to be filled with walls instead of the default pattern."
msgstr "맨 위 스킨 레이어(공기에 노출됨)의 작은(최대 '작은 상단/하단 너비') 영역을 기본 패턴 대신 벽으로 채울 수 있습니다."

msgctxt "jerk_enabled description"
msgid "Enables adjusting the jerk of print head when the velocity in the X or Y axis changes. Increasing the jerk can reduce printing time at the cost of print quality."
msgstr "X 또는 Y 축의 속도가 변경 될 때 프린트 헤드의 속도를 조정할 수 있습니다. Jerk를 높이면 프린팅 품질을 저하시키면서 프린팅 시간을 줄일 수 있습니다."

msgctxt "acceleration_enabled description"
msgid "Enables adjusting the print head acceleration. Increasing the accelerations can reduce printing time at the cost of print quality."
msgstr "프린트 헤드 가속도를 활성화 합니다. 가속도를 높이면 프린팅 품질을 저하시키지만 프린팅 시간을 줄일 수 있습니다."

msgctxt "cool_fan_enabled description"
msgid "Enables the print cooling fans while printing. The fans improve print quality on layers with short layer times and bridging / overhangs."
msgstr "프린팅 중에 프린팅 냉각 팬을 활성화합니다. 팬은 짧은 레이어 시간 및 브리징 / 오버행으로 레이어의 프린팅 품질을 향상시킵니다."

msgctxt "machine_end_gcode label"
msgid "End G-code"
msgstr "End GCode"

msgctxt "material_end_of_filament_purge_length label"
msgid "End of Filament Purge Length"
msgstr "필라멘트 끝의 퍼지 길이"

msgctxt "material_end_of_filament_purge_speed label"
msgid "End of Filament Purge Speed"
msgstr "필라멘트 끝의 퍼지 속도"

msgctxt "brim_replaces_support description"
msgid "Enforce brim to be printed around the model even if that space would otherwise be occupied by support. This replaces some regions of the first layer of support by brim regions."
msgstr "서포트가 차지할 공간이더라도 모델 주변에 브림이 인쇄되도록 합니다. 이렇게 하면 서포트의 첫 번째 레이어 영역 일부가 브림 영역으로 대체됩니다."

msgctxt "brim_location option everywhere"
msgid "Everywhere"
msgstr "모두"

msgctxt "support_type option everywhere"
msgid "Everywhere"
msgstr "어디에나"

msgctxt "slicing_tolerance option exclusive"
msgid "Exclusive"
msgstr "배타적"

msgctxt "experimental label"
msgid "Experimental"
msgstr "실험적인"

msgctxt "z_seam_corner option z_seam_corner_outer"
msgid "Expose Seam"
msgstr "솔기 노출"

msgctxt "meshfix_extensive_stitching label"
msgid "Extensive Stitching"
msgstr "광범위한 스티칭"

msgctxt "meshfix_extensive_stitching description"
msgid "Extensive stitching tries to stitch up open holes in the mesh by closing the hole with touching polygons. This option can introduce a lot of processing time."
msgstr "광범위한 스티칭은 다각형을 만지면서 구멍을 닫음으로써 메쉬의 열린 구멍을 꿰매려합니다. 이 옵션은 많은 처리 시간을 초래할 수 있습니다."

msgctxt "infill_wall_line_count label"
msgid "Extra Infill Wall Count"
msgstr "여분의 내부채움 벽 수"

msgctxt "skin_outline_count label"
msgid "Extra Skin Wall Count"
msgstr "여분의 스킨 벽 수"

msgctxt "switch_extruder_extra_prime_amount description"
msgid "Extra material to prime after nozzle switching."
msgstr "노즐 스위치 후 프라이밍하는 추가 소재의 양입니다."

msgctxt "extruder_prime_pos_x label"
msgid "Extruder Prime X Position"
msgstr "익스트루더 프라임 X 위치"

msgctxt "extruder_prime_pos_y label"
msgid "Extruder Prime Y Position"
msgstr "익스트루더 프라임 Y 위치"

msgctxt "extruder_prime_pos_z label"
msgid "Extruder Prime Z Position"
msgstr "익스트루더 프라임 Z 포지션"

msgctxt "machine_extruders_share_heater label"
msgid "Extruders Share Heater"
msgstr "압출기의 히터 공유"

msgctxt "machine_extruders_share_nozzle label"
msgid "Extruders Share Nozzle"
msgstr "익스트루더의 노즐 공유"

msgctxt "material_extrusion_cool_down_speed label"
msgid "Extrusion Cool Down Speed Modifier"
msgstr "압출 냉각 속도 조절기"

msgctxt "speed_equalize_flow_width_factor description"
msgid "Extrusion width based correction factor on the speed. At 0% the movement speed is kept constant at the Print Speed. At 100% the movement speed is adjusted so that the flow (in mm³/s) is kept constant, i.e. lines half the normal Line Width are printed twice as fast and lines twice as wide are printed half as fast. A value larger than 100% can help to compensate for the higher pressure required to extrude wide lines."
msgstr "속도에 대한 압출 너비 기준 보정 계수. 0%에서는 이동 속도가 프린팅 속도로 일정하게 유지됩니다. 100%에서는 흐름(단위: mm³/s)이 일정하게 유지되도록 이동 속도가 조정됩니다. 즉 일반적인 선 너비의 절반인 선은 두 배로 빠르게 프린팅되고 너비가 두 배인 선은 절반 속도로 프린팅됩니다. 100%보다 큰 값은 넓은 선을 압출하기 위해 요구되는 더 높은 압력을 보정하는 데 도움이 될 수 있습니다."

msgctxt "cool_fan_speed label"
msgid "Fan Speed"
msgstr "팬 속도"

msgctxt "support_fan_enable label"
msgid "Fan Speed Override"
msgstr "팬 속도 무시"

msgctxt "small_feature_max_length description"
msgid "Feature outlines that are shorter than this length will be printed using Small Feature Speed."
msgstr "이 수치보다 길이가 짧은 피처 윤곽은 소형 피처 속도 기능을 이용해 프린트합니다."

msgctxt "experimental description"
msgid "Features that haven't completely been fleshed out yet."
msgstr "아직 구체화되지 않은 기능들."

msgctxt "machine_feeder_wheel_diameter label"
msgid "Feeder Wheel Diameter"
msgstr "피더 휠 지름"

msgctxt "material_final_print_temperature label"
msgid "Final Printing Temperature"
msgstr "최종 프린팅 온도"

msgctxt "machine_firmware_retract label"
msgid "Firmware Retraction"
msgstr "펌웨어 리트렉션"

msgctxt "support_extruder_nr_layer_0 label"
msgid "First Layer Support Extruder"
msgstr "첫 번째 레이어 서포트 익스트루더"

msgctxt "material_flow label"
msgid "Flow"
msgstr "공급량"

msgctxt "speed_equalize_flow_width_factor label"
msgid "Flow Equalization Ratio"
msgstr "흐름 균일화 비율"

msgctxt "flow_anomaly_limit label"
msgid "Flow Limit"
msgstr "유량 제한"

msgctxt "flow_rate_extrusion_offset_factor label"
msgid "Flow Rate Compensation Factor"
msgstr "압출 속도 보상 배율"

msgctxt "flow_rate_max_extrusion_offset label"
msgid "Flow Rate Compensation Max Extrusion Offset"
msgstr "압출 속도 보상 최대 압출 오프셋"

msgctxt "material_flow_temp_graph label"
msgid "Flow Temperature Graph"
msgstr "재료 공급 온도 그래프"

msgctxt "flow_warn_limit label"
msgid "Flow Warning"
msgstr "유량 경고"

msgctxt "material_flow_layer_0 description"
msgid "Flow compensation for the first layer: the amount of material extruded on the initial layer is multiplied by this value."
msgstr "첫번째 레이어에 대한 압출량 보상: 압출 된 재료의 양에 이 값을 곱합니다."

msgctxt "skin_material_flow_layer_0 description"
msgid "Flow compensation on bottom lines of the first layer"
msgstr "첫 번째 레이어 하단 라인의 압출 보상"

msgctxt "infill_material_flow description"
msgid "Flow compensation on infill lines."
msgstr "내부채움 라인의 압출 보상입니다."

msgctxt "support_interface_material_flow description"
msgid "Flow compensation on lines of support roof or floor."
msgstr "지지대 지붕 또는 바닥 라인의 압출 보상입니다."

msgctxt "roofing_material_flow description"
msgid "Flow compensation on lines of the areas at the top of the print."
msgstr "프린트 상단 부분 라인의 압출 보상입니다."

msgctxt "prime_tower_flow description"
msgid "Flow compensation on prime tower lines."
msgstr "프라임 타워 라인의 압출 보상입니다."

msgctxt "skirt_brim_material_flow description"
msgid "Flow compensation on skirt or brim lines."
msgstr "스커트 또는 브림 라인의 압출 보상입니다."

msgctxt "support_bottom_material_flow description"
msgid "Flow compensation on support floor lines."
msgstr "지지대 바닥 라인의 압출 보상입니다."

msgctxt "support_roof_material_flow description"
msgid "Flow compensation on support roof lines."
msgstr "지지대 지붕 라인의 압출 보상입니다."

msgctxt "support_material_flow description"
msgid "Flow compensation on support structure lines."
msgstr "지지대 구조 라인의 압출 보상입니다."

msgctxt "wall_0_material_flow_layer_0 description"
msgid "Flow compensation on the outermost wall line of the first layer."
msgstr "첫 번째 레이어의 가장 외측 벽 라인의 압출 보상입니다."

msgctxt "wall_0_material_flow description"
msgid "Flow compensation on the outermost wall line."
msgstr "가장 외측 벽 라인의 압출 보상입니다."

msgctxt "wall_0_material_flow_roofing description"
msgid "Flow compensation on the top surface outermost wall line."
msgstr "상면 가장 바깥쪽 벽 라인의 유량 보정"

msgctxt "wall_x_material_flow_roofing description"
msgid "Flow compensation on top surface wall lines for all wall lines except the outermost one."
msgstr "가장 바깥쪽 라인을 제외한 모든 벽 라인에 대한 상면 벽 라인의 유량 보정"

msgctxt "skin_material_flow description"
msgid "Flow compensation on top/bottom lines."
msgstr "상단/하단 라인의 압출 보상입니다."

msgctxt "wall_x_material_flow_layer_0 description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one, but only for the first layer"
msgstr "가장 외측 벽을 제외한 모든 벽 라인의 압출 보상입니다(단, 첫 번째 레이어에 한정)."

msgctxt "wall_x_material_flow description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one."
msgstr "가장 외측 벽을 제외한 모든 벽 라인의 압출 보상입니다."

msgctxt "wall_material_flow description"
msgid "Flow compensation on wall lines."
msgstr "벽 라인의 압출 보상입니다."

msgctxt "material_flow description"
msgid "Flow compensation: the amount of material extruded is multiplied by this value."
msgstr "압출량 보상: 압출 된 재료의 양에 이 값을 곱합니다."

msgctxt "meshfix_fluid_motion_angle label"
msgid "Fluid Motion Angle"
msgstr "플루이드 모션 각도"

msgctxt "meshfix_fluid_motion_shift_distance label"
msgid "Fluid Motion Shift Distance"
msgstr "플루이드 모션 이동 거리"

msgctxt "meshfix_fluid_motion_small_distance label"
msgid "Fluid Motion Small Distance"
msgstr "플루이드 모션 가까운 거리"

msgctxt "material_flush_purge_length label"
msgid "Flush Purge Length"
msgstr "수평 퍼지 길이"

msgctxt "material_flush_purge_speed label"
msgid "Flush Purge Speed"
msgstr "수평 퍼지 속도"

msgctxt "min_wall_line_width description"
msgid "For thin structures around once or twice the nozzle size, the line widths need to be altered to adhere to the thickness of the model. This setting controls the minimum line width allowed for the walls. The minimum line widths inherently also determine the maximum line widths, since we transition from N to N+1 walls at some geometry thickness where the N walls are wide and the N+1 walls are narrow. The widest possible wall line is twice the Minimum Wall Line Width."
msgstr "노즐 크기의 1~2배 정도의 얇은 구조물의 경우 모델의 두께에 맞게 선 너비를 변경해야 합니다. 이 설정은 벽에 허용되는 최소 선 너비를 제어합니다. N개의 벽이 넓고 N+1개의 벽이 좁은 일부 형상 두께에서는 N개의 벽에서 N+1개의 벽으로 전환하기 때문에, 최소 선 너비가 내재적으로 최대 선 너비를 결정합니다. 가장 넓을 가능성이 있는 벽 선은 최소 벽 선 너비의 두 배입니다."

msgctxt "z_seam_position option front"
msgid "Front"
msgstr "전면"

msgctxt "z_seam_position option frontleft"
msgid "Front Left"
msgstr "전면 왼쪽"

msgctxt "z_seam_position option frontright"
msgid "Front Right"
msgstr "전면 오른쪽"

msgctxt "draft_shield_height_limitation option full"
msgid "Full"
msgstr "가득찬"

msgctxt "magic_fuzzy_skin_enabled label"
msgid "Fuzzy Skin"
msgstr "퍼지 스킨"

msgctxt "magic_fuzzy_skin_point_density label"
msgid "Fuzzy Skin Density"
msgstr "퍼지 스킨 밀도"

msgctxt "magic_fuzzy_skin_outside_only label"
msgid "Fuzzy Skin Outside Only"
msgstr "부용 퍼지 스킨"

msgctxt "magic_fuzzy_skin_point_dist label"
msgid "Fuzzy Skin Point Distance"
msgstr "퍼지 스킨 포인트 거리"

msgctxt "magic_fuzzy_skin_thickness label"
msgid "Fuzzy Skin Thickness"
msgstr "퍼지 스킨 두께"

msgctxt "machine_gcode_flavor label"
msgid "G-code Flavor"
msgstr "Gcode 유형"

msgctxt "machine_end_gcode description"
msgid "G-code commands to be executed at the very end - separated by \n."
msgstr "맨 마지막에 실행될 G 코드 명령 "
"."

msgctxt "machine_start_gcode description"
msgid "G-code commands to be executed at the very start - separated by \n."
msgstr "시작과 동시에형실행될 G 코드 명령어 "
"."

msgctxt "material_guid description"
msgid "GUID of the material. This is set automatically."
msgstr "재료의 GUID. 자동으로 설정됩니다."

msgctxt "gantry_height label"
msgid "Gantry Height"
msgstr "갠트리 높이"

msgctxt "interlocking_enable label"
msgid "Generate Interlocking Structure"
msgstr "연동 구조 생성"

msgctxt "support_enable label"
msgid "Generate Support"
msgstr "서포트 생성"

msgctxt "support_brim_enable description"
msgid "Generate a brim within the support infill regions of the first layer. This brim is printed underneath the support, not around it. Enabling this setting increases the adhesion of support to the build plate."
msgstr "첫 번째 레이어의 서포트 내부채움 영역 내에서 브림을 생성합니다. 이 브림은 서포트 주변이 아니라 아래에 인쇄됩니다. 이 설정을 사용하면 빌드 플레이트에 대한 서포트력이 향상됩니다."

msgctxt "support_interface_enable description"
msgid "Generate a dense interface between the model and the support. This will create a skin at the top of the support on which the model is printed and at the bottom of the support, where it rests on the model."
msgstr "모델과 서포트 사이에 조밀 한 인터페이스를 생성합니다. 이렇게 하면 모델이 프린팅 된 서포트 맨 위의 스킨과 모델의 위에있는 서포트 맨 아래에 스킨이 만들어집니다."

msgctxt "support_bottom_enable description"
msgid "Generate a dense slab of material between the bottom of the support and the model. This will create a skin between the model and support."
msgstr "서포트 바닥과 모델 사이에 조밀 한 슬래브를 생성하십시오. 그러면 모델과 지원 사이에 스킨이 만들어집니다."

msgctxt "support_roof_enable description"
msgid "Generate a dense slab of material between the top of support and the model. This will create a skin between the model and support."
msgstr "서포트 상단과 모델 사이에 조밀 한 슬래브를 생성하십시오. 그러면 모델과 서포트 사이에 스킨이 만들어집니다."

msgctxt "support_enable description"
msgid "Generate structures to support parts of the model which have overhangs. Without these structures, such parts would collapse during printing."
msgstr "오버행이 있는 모델 부분을 서포트하는 구조를 생성합니다. 이러한 구조가 없으면 이런 부분이 프린팅 중에 붕괴됩니다."

msgctxt "machine_buildplate_type option glass"
msgid "Glass"
msgstr "유리"

msgctxt "ironing_enabled description"
msgid "Go over the top surface one additional time, but this time extruding very little material. This is meant to melt the plastic on top further, creating a smoother surface. The pressure in the nozzle chamber is kept high so that the creases in the surface are filled with material."
msgstr "상단 표면을 한 번 더 이동하지만 재료를 아주 약간만 압출 성형합니다. 따라서 맨 위의 플라스틱이 녹아 부드러운 표면을 만듭니다. 노즐 챔버 내의 압력이 고압으로 유지되므로 표면상의 주름이 재료로 채워집니다."

msgctxt "gradual_infill_step_height label"
msgid "Gradual Infill Step Height"
msgstr "점진적인 내부채움 단계 높이"

msgctxt "gradual_infill_steps label"
msgid "Gradual Infill Steps"
msgstr "점진적인 내부채움 단계"

msgctxt "gradual_support_infill_step_height label"
msgid "Gradual Support Infill Step Height"
msgstr "점진적 서포트 내부채움 단계 높이"

msgctxt "gradual_support_infill_steps label"
msgid "Gradual Support Infill Steps"
msgstr "점진적 서포트 내부채움 단계"

msgctxt "cool_min_temperature description"
msgid "Gradually reduce to this temperature when printing at reduced speeds because of minimum layer time."
msgstr "최소 레이어 시간 때문에 늦춰진 속도로 프린트하는 경우 점차 이 온도로 낮춥니다."

msgctxt "infill_pattern option grid"
msgid "Grid"
msgstr "그리드"

msgctxt "support_bottom_pattern option grid"
msgid "Grid"
msgstr "그리드"

msgctxt "support_interface_pattern option grid"
msgid "Grid"
msgstr "그리드"

msgctxt "support_pattern option grid"
msgid "Grid"
msgstr "격자"

msgctxt "support_roof_pattern option grid"
msgid "Grid"
msgstr "그리드"

msgctxt "machine_gcode_flavor option Griffin"
msgid "Griffin"
msgstr "Griffin"

msgctxt "group_outer_walls label"
msgid "Group Outer Walls"
msgstr "외벽 그룹화"

msgctxt "infill_pattern option gyroid"
msgid "Gyroid"
msgstr "자이로이드"

msgctxt "support_pattern option gyroid"
msgid "Gyroid"
msgstr "자이로이드"

msgctxt "machine_heated_build_volume label"
msgid "Has Build Volume Temperature Stabilization"
msgstr "빌드 볼륨 온도 안정화"

msgctxt "machine_heated_bed label"
msgid "Has Heated Build Plate"
msgstr "히팅 빌드 플레이트가 있음"

msgctxt "machine_nozzle_heat_up_speed label"
msgid "Heat Up Speed"
msgstr "가열 속도"

msgctxt "machine_heat_zone_length label"
msgid "Heat Zone Length"
msgstr "가열 영역 길이"

msgctxt "draft_shield_height description"
msgid "Height limitation of the draft shield. Above this height no draft shield will be printed."
msgstr "드래프트 쉴드의 높이 제한. 이 높이 이상에서는 드래프트 쉴드가 프린팅되지 않습니다."

msgctxt "z_seam_corner option z_seam_corner_inner"
msgid "Hide Seam"
msgstr "솔기 숨기기"

msgctxt "z_seam_corner option z_seam_corner_any"
msgid "Hide or Expose Seam"
msgstr "솔기 숨기기 또는 노출"

msgctxt "hole_xy_offset label"
msgid "Hole Horizontal Expansion"
msgstr "구멍 수평 확장"

msgctxt "hole_xy_offset_max_diameter label"
msgid "Hole Horizontal Expansion Max Diameter"
msgstr "구멍 수평 확장 최대 직경"

msgctxt "small_hole_max_size description"
msgid "Holes and part outlines with a diameter smaller than this will be printed using Small Feature Speed."
msgstr "이 수치보다 직경이 작은 구멍 및 부품 윤곽은 소형 피처 속도 기능을 이용해 프린트합니다."

msgctxt "xy_offset label"
msgid "Horizontal Expansion"
msgstr "수평 확장"

msgctxt "material_shrinkage_percentage_xy label"
msgid "Horizontal Scaling Factor Shrinkage Compensation"
msgstr "수평 확장 배율 수축 보정"

msgctxt "material_break_preparation_retracted_position description"
msgid "How far the filament can be stretched before it breaks, while heated."
msgstr "가열 시 파단되기 전까지 필라멘트가 늘어날 수 있는 거리입니다."

msgctxt "material_anti_ooze_retracted_position description"
msgid "How far the material needs to be retracted before it stops oozing."
msgstr "흐름이 멈추기 전에 소재가 후퇴해야 하는 거리입니다."

msgctxt "flow_rate_extrusion_offset_factor description"
msgid "How far to move the filament in order to compensate for changes in flow rate, as a percentage of how far the filament would move in one second of extrusion."
msgstr "압출 속도 변화를 보상하기 위해 필라멘트를 이동하는 거리(1초 압출 시 필라멘트가 이동할 수 있는 거리의 백분율)."

msgctxt "material_break_retracted_position description"
msgid "How far to retract the filament in order to break it cleanly."
msgstr "필라멘트가 깔끔하게 파단되기 위해 후퇴해야 하는 거리입니다."

msgctxt "material_break_preparation_speed description"
msgid "How fast the filament needs to be retracted just before breaking it off in a retraction."
msgstr "리트랙션 시 파단되기 직전까지 필라멘트가 후퇴해야 하는 속도입니다."

msgctxt "material_anti_ooze_retraction_speed description"
msgid "How fast the material needs to be retracted during a filament switch to prevent oozing."
msgstr "흐름을 방지하기 위해 필라멘트 스위치 중 소재가 후퇴해야 하는 속도입니다."

msgctxt "material_end_of_filament_purge_speed description"
msgid "How fast to prime the material after replacing an empty spool with a fresh spool of the same material."
msgstr "빈 스풀을 동일한 재료의 새로운 스풀로 교체한 후 재료를 압출하는 속도."

msgctxt "material_flush_purge_speed description"
msgid "How fast to prime the material after switching to a different material."
msgstr "다른 재료로 전환 후 재료를 압출하는 속도."

msgctxt "material_maximum_park_duration description"
msgid "How long the material can be kept out of dry storage safely."
msgstr "재료를 안전하게 건식 보관함에 보관할 수 있는 기간."

msgctxt "machine_steps_per_mm_x description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the X direction."
msgstr "X 방향으로 1 밀리미터를 압출에 필요한 스텝 모터의 스텝 수."

msgctxt "machine_steps_per_mm_y description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Y direction."
msgstr "Y 방향으로 1 밀리미터를 압출에 필요한 스텝 모터의 스텝 수."

msgctxt "machine_steps_per_mm_z description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Z direction."
msgstr "Z 방향으로 1 밀리미터를 압출에 필요한 스텝 모터의 스텝 수."

msgctxt "machine_steps_per_mm_e description"
msgid "How many steps of the stepper motors will result in moving the feeder wheel by one millimeter around its circumference."
msgstr "둘레를 따라 1밀리미터씩 피더 휠을 움직이는 스텝 모터의 스텝 수."

msgctxt "material_end_of_filament_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when replacing an empty spool with a fresh spool of the same material."
msgstr "빈 스풀을 동일한 재료의 새로운 스풀로 교체할 때 (필라멘트를 지나며) 노즐에서 이전 재료를 퍼지하기 위해 사용하는 재료 양."

msgctxt "material_flush_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when switching to a different material."
msgstr "다른 재료로 전환할 때 (필라멘트를 지나며) 노즐에서 이전 재료를 퍼지하기 위해 사용하는 재료 양."

msgctxt "machine_extruders_shared_nozzle_initial_retraction description"
msgid "How much the filament of each extruder is assumed to have been retracted from the shared nozzle tip at the completion of the printer-start gcode script; the value should be equal to or greater than the length of the common part of the nozzle's ducts."
msgstr "프린터 시작 gcode 스크립트가 완료될 때 공유된 노즐 끝에서 각 익스트루더의 필라멘트가 수축된 것으로 가정하는 정도입니다. 이 값은 노즐 덕트의 공통 부분의 길이와 같거나 커야 합니다."

msgctxt "support_interface_priority description"
msgid "How support interface and support will interact when they overlap. Currently only implemented for support roof."
msgstr "서포트 인터페이스와 서포트가 겹칠 때 상호 작용하는 방식으로, 현재 서포트 지붕에만 구현되어 있습니다."

msgctxt "support_tree_min_height_to_model description"
msgid "How tall a branch has to be if it is placed on the model. Prevents small blobs of support. This setting is ignored when a branch is supporting a support roof."
msgstr "모델에 브랜치를 배치할 때 브랜치의 높이를 설정합니다. 이에 따라 서포트의 작은 얼룩이 방지됩니다. 브랜치가 서포트 지붕을 서포트하는 경우 이 설정은 무시됩니다."

msgctxt "bridge_skin_support_threshold description"
msgid "If a skin region is supported for less than this percentage of its area, print it using the bridge settings. Otherwise it is printed using the normal skin settings."
msgstr "스킨 영역이 해당 영역의 비율 미만으로 생성되면 브릿지 설정을 사용하여 인쇄하십시오. 그렇지 않으면 일반 스킨 설정을 사용하여 인쇄됩니다."

msgctxt "meshfix_fluid_motion_angle description"
msgid "If a toolpath-segment deviates more than this angle from the general motion it is smoothed."
msgstr "도구 경로 세그먼트가 일반적인 모션에서 이 각도보다 더 많이 벗어나면 평활화됩니다."

msgctxt "bridge_enable_more_layers description"
msgid "If enabled, the second and third layers above the air are printed using the following settings. Otherwise, those layers are printed using the normal settings."
msgstr "이 옵션을 사용하면 다음 설정을 사용하여 에어 위의 두 번째 및 세 번째 레이어가 인쇄됩니다. 그렇지 않으면 해당 레이어는 일반 설정을 사용하여 인쇄됩니다."

msgctxt "wall_transition_filter_distance description"
msgid "If it would be transitioning back and forth between different numbers of walls in quick succession, don't transition at all. Remove transitions if they are closer together than this distance."
msgstr "서로 다른 수의 벽들 사이를 빠르게 연속적으로 왔다 갔다 하며 전환되는 경우에는 전환하지 마십시오. 이 거리보다 서로 더 가까운 경우에는 전환을 제거하십시오."

msgctxt "raft_base_margin description"
msgid "If the raft base is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "래프트 베이스가 활성화된 경우, 래프트가 주어진 모델 주위의 추가 래프트 영역입니다. 이 여백을 늘리면 재료를 더 사용하고 출력 영역을 적게 차지하면서 더 강한 래프트를 만들 수 있습니다."

msgctxt "raft_margin description"
msgid "If the raft is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "래프트가 활성화 된 경우 래프트가 주어진 모델 주변의 추가 래프트 지역입니다. 이 여백을 늘리면 재료를 더 많이 사용하고 출력물을 적게 차지하면서 더 강력한 래프트가 만들어집니다."

msgctxt "raft_interface_margin description"
msgid "If the raft middle is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "래프트 중간이 활성화된 경우, 래프트가 주어진 모델 주위의 추가 래프트 영역입니다. 이 여백을 늘리면 재료를 더 사용하고 출력 영역을 적게 차지하면서 더 강한 래프트를 만들 수 있습니다."

msgctxt "raft_surface_margin description"
msgid "If the raft top is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "래프트 상단이 활성화된 경우, 래프트가 주어진 모델 주위의 추가 래프트 영역입니다. 이 여백을 늘리면 재료를 더 사용하고 출력 영역을 적게 차지하면서 더 강한 래프트를 만들 수 있습니다."

msgctxt "meshfix_union_all description"
msgid "Ignore the internal geometry arising from overlapping volumes within a mesh and print the volumes as one. This may cause unintended internal cavities to disappear."
msgstr "메쉬 내의 겹치는 볼륨으로 인해 발생하는 내부 지오메트리를 무시하고 볼륨을 하나로 프린팅합니다. 이로 인해 의도하지 않은 내부 공동이 사라질 수 있습니다."

msgctxt "material_bed_temp_prepend label"
msgid "Include Build Plate Temperature"
msgstr "빌드 플레이트 온도 포함"

msgctxt "material_print_temp_prepend label"
msgid "Include Material Temperatures"
msgstr "재료의 온도 포함하기"

msgctxt "slicing_tolerance option inclusive"
msgid "Inclusive"
msgstr "중복"

msgctxt "infill description"
msgid "Infill"
msgstr "내부채움"

msgctxt "infill label"
msgid "Infill"
msgstr "내부채움"

msgctxt "acceleration_infill label"
msgid "Infill Acceleration"
msgstr "내부채움 가속도"

msgctxt "infill_before_walls label"
msgid "Infill Before Walls"
msgstr "벽 앞에 내부채움"

msgctxt "infill_sparse_density label"
msgid "Infill Density"
msgstr "내부채움 밀도"

msgctxt "infill_extruder_nr label"
msgid "Infill Extruder"
msgstr "내부채움 익스트루더"

msgctxt "infill_material_flow label"
msgid "Infill Flow"
msgstr "내부채움 압출량"

msgctxt "jerk_infill label"
msgid "Infill Jerk"
msgstr "Jerk 내부채움"

msgctxt "infill_sparse_thickness label"
msgid "Infill Layer Thickness"
msgstr "내부채움 층 두께"

msgctxt "infill_angles label"
msgid "Infill Line Directions"
msgstr "내부채움 선 방향"

msgctxt "infill_line_distance label"
msgid "Infill Line Distance"
msgstr "내부채움 선간 거리"

msgctxt "infill_multiplier label"
msgid "Infill Line Multiplier"
msgstr "내부채움 선 승수"

msgctxt "infill_line_width label"
msgid "Infill Line Width"
msgstr "내부채움 선 폭"

msgctxt "infill_mesh label"
msgid "Infill Mesh"
msgstr "메쉬 내부채움"

msgctxt "infill_support_angle label"
msgid "Infill Overhang Angle"
msgstr "충진물 오버행 각도"

msgctxt "infill_overlap_mm label"
msgid "Infill Overlap"
msgstr "내부채움 오버랩"

msgctxt "infill_overlap label"
msgid "Infill Overlap Percentage"
msgstr "내부채움 오버랩 비율"

msgctxt "infill_pattern label"
msgid "Infill Pattern"
msgstr "내부채움 패턴"

msgctxt "speed_infill label"
msgid "Infill Speed"
msgstr "내부채움 속도"

msgctxt "infill_support_enabled label"
msgid "Infill Support"
msgstr "충진물 지지대"

msgctxt "infill_enable_travel_optimization label"
msgid "Infill Travel Optimization"
msgstr "내부채움재 이동 최적화"

msgctxt "infill_wipe_dist label"
msgid "Infill Wipe Distance"
msgstr "내부채움 거리"

msgctxt "infill_offset_x label"
msgid "Infill X Offset"
msgstr "내부채움 X 오프셋"

msgctxt "infill_offset_y label"
msgid "Infill Y Offset"
msgstr "내부채움 Y 오프셋"

msgctxt "initial_bottom_layers label"
msgid "Initial Bottom Layers"
msgstr "초기 하단 레이어"

msgctxt "cool_fan_speed_0 label"
msgid "Initial Fan Speed"
msgstr "초기 팬 속도"

msgctxt "acceleration_layer_0 label"
msgid "Initial Layer Acceleration"
msgstr "초기 레이어 가속"

msgctxt "skin_material_flow_layer_0 label"
msgid "Initial Layer Bottom Flow"
msgstr "첫 번째 레이어 하단 압출량"

msgctxt "support_tree_bp_diameter label"
msgid "Initial Layer Diameter"
msgstr "초기 레이어 직경"

msgctxt "material_flow_layer_0 label"
msgid "Initial Layer Flow"
msgstr "첫번째 레이어 압출량"

msgctxt "layer_height_0 label"
msgid "Initial Layer Height"
msgstr "첫번째 레이어 높이"

msgctxt "xy_offset_layer_0 label"
msgid "Initial Layer Horizontal Expansion"
msgstr "첫번째 레이어 수평 확장"

msgctxt "wall_x_material_flow_layer_0 label"
msgid "Initial Layer Inner Wall Flow"
msgstr "첫 번째 레이어 내벽 압출량"

msgctxt "jerk_layer_0 label"
msgid "Initial Layer Jerk"
msgstr "초기 레이어 Jerk"

msgctxt "initial_layer_line_width_factor label"
msgid "Initial Layer Line Width"
msgstr "초기 레이어 라인 폭"

msgctxt "wall_0_material_flow_layer_0 label"
msgid "Initial Layer Outer Wall Flow"
msgstr "첫 번째 레이어 외벽 압출량"

msgctxt "acceleration_print_layer_0 label"
msgid "Initial Layer Print Acceleration"
msgstr "초기 레이어 프린팅 가속"

msgctxt "jerk_print_layer_0 label"
msgid "Initial Layer Print Jerk"
msgstr "초기 레이어 프린팅 Jerk"

msgctxt "speed_print_layer_0 label"
msgid "Initial Layer Print Speed"
msgstr "초기 레이어 프린팅 속도"

msgctxt "speed_layer_0 label"
msgid "Initial Layer Speed"
msgstr "초기 레이어 속도"

msgctxt "support_initial_layer_line_distance label"
msgid "Initial Layer Support Line Distance"
msgstr "초기 레이어 서포트 선 거리"

msgctxt "acceleration_travel_layer_0 label"
msgid "Initial Layer Travel Acceleration"
msgstr "초기 레이어 이동 가속도"

msgctxt "jerk_travel_layer_0 label"
msgid "Initial Layer Travel Jerk"
msgstr "초기 레이어 이동 Jerk"

msgctxt "speed_travel_layer_0 label"
msgid "Initial Layer Travel Speed"
msgstr "초기 레이어 이동 속도"

msgctxt "layer_0_z_overlap label"
msgid "Initial Layer Z Overlap"
msgstr "초기 레이어 Z 겹침"

msgctxt "material_initial_print_temperature label"
msgid "Initial Printing Temperature"
msgstr "초기 프린팅 온도"

msgctxt "acceleration_wall_x label"
msgid "Inner Wall Acceleration"
msgstr "내벽 가속도"

msgctxt "wall_x_extruder_nr label"
msgid "Inner Wall Extruder"
msgstr "내벽 익스트루더"

msgctxt "jerk_wall_x label"
msgid "Inner Wall Jerk"
msgstr "내벽 Jerk"

msgctxt "speed_wall_x label"
msgid "Inner Wall Speed"
msgstr "내벽 속도"

msgctxt "wall_x_material_flow label"
msgid "Inner Wall(s) Flow"
msgstr "내벽 압출량"

msgctxt "wall_line_width_x label"
msgid "Inner Wall(s) Line Width"
msgstr "내부 벽 선 너비"

msgctxt "wall_0_inset description"
msgid "Inset applied to the path of the outer wall. If the outer wall is smaller than the nozzle, and printed after the inner walls, use this offset to get the hole in the nozzle to overlap with the inner walls instead of the outside of the model."
msgstr "외벽의 경로에 삽입이 적용됩니다. 외벽이 노즐보다 작고 내벽 다음에 프린팅 된 경우 이 옵셋을 사용하여 노즐의 구멍이 모델 외부가 아닌 내벽과 겹치도록하십시오."

msgctxt "brim_location option inside"
msgid "Inside Only"
msgstr "내부만"

msgctxt "inset_direction option inside_out"
msgid "Inside To Outside"
msgstr "내부에서 외부로"

msgctxt "support_interface_priority option interface_lines_overwrite_support_area"
msgid "Interface lines preferred"
msgstr "인터페이스 라인 우선"

msgctxt "support_interface_priority option interface_area_overwrite_support_area"
msgid "Interface preferred"
msgstr "인터페이스 우선"

msgctxt "prime_tower_mode option interleaved"
msgid "Interleaved"
msgstr "중간 삽입"

msgctxt "interlocking_beam_layer_count label"
msgid "Interlocking Beam Layer Count"
msgstr "연동 빔 레이어 수"

msgctxt "interlocking_beam_width label"
msgid "Interlocking Beam Width"
msgstr "연동 빔 너비"

msgctxt "interlocking_boundary_avoidance label"
msgid "Interlocking Boundary Avoidance"
msgstr "연동 경계 회피"

msgctxt "interlocking_depth label"
msgid "Interlocking Depth"
msgstr "연동 깊이"

msgctxt "interlocking_orientation label"
msgid "Interlocking Structure Orientation"
msgstr "연동 구조 방향"

msgctxt "ironing_only_highest_layer label"
msgid "Iron Only Highest Layer"
msgstr "최상위 레이어에 다림질"

msgctxt "acceleration_ironing label"
msgid "Ironing Acceleration"
msgstr "다림질 가속"

msgctxt "ironing_flow label"
msgid "Ironing Flow"
msgstr "다림질 압출량"

msgctxt "ironing_inset label"
msgid "Ironing Inset"
msgstr "다림질 삽입"

msgctxt "jerk_ironing label"
msgid "Ironing Jerk"
msgstr "다림질 저크(Jerk)"

msgctxt "ironing_line_spacing label"
msgid "Ironing Line Spacing"
msgstr "다림질 라인 간격"

msgctxt "ironing_pattern label"
msgid "Ironing Pattern"
msgstr "다림질 패턴"

msgctxt "speed_ironing label"
msgid "Ironing Speed"
msgstr "다림질 속도"

msgctxt "machine_center_is_zero label"
msgid "Is Center Origin"
msgstr "센터 원점"

msgctxt "material_is_support_material label"
msgid "Is support material"
msgstr "서포트 재료임"

msgctxt "material_crystallinity description"
msgid "Is this material the type that breaks off cleanly when heated (crystalline), or is it the type that produces long intertwined polymer chains (non-crystalline)?"
msgstr "이 소재는 가열 시 깔끔하게 분리되는 유형(결정형)입니까? 아니면 길게 얽힌 폴리머 체인을 생성하는 유형(비결정형)입니까?"

msgctxt "material_is_support_material description"
msgid "Is this material typically used as a support material during printing."
msgstr "이 재료는 일반적으로 프린팅 중에 서포트 재료로 사용됩니다."

msgctxt "magic_fuzzy_skin_outside_only description"
msgid "Jitter only the parts' outlines and not the parts' holes."
msgstr "부품의 윤곽만 지터하고 부품의 구멍은 지터하지 않습니다."

msgctxt "meshfix_keep_open_polygons label"
msgid "Keep Disconnected Faces"
msgstr "끊긴 면 유지"

msgctxt "layer_height label"
msgid "Layer Height"
msgstr "층 높이"

msgctxt "layer_start_x label"
msgid "Layer Start X"
msgstr "레이어 시작 X"

msgctxt "layer_start_y label"
msgid "Layer Start Y"
msgstr "레이어 시작 Y"

msgctxt "raft_base_thickness description"
msgid "Layer thickness of the base raft layer. This should be a thick layer which sticks firmly to the printer build plate."
msgstr "기본 래프트 레이어의 레이어 두께. 이것은 프린터 빌드 플레이트에 단단히 붙어있는 두꺼운 층이어야합니다."

msgctxt "raft_interface_thickness description"
msgid "Layer thickness of the middle raft layer."
msgstr "중간 래프트 층의 층 두께."

msgctxt "raft_surface_thickness description"
msgid "Layer thickness of the top raft layers."
msgstr "상단 래프트 레이어의 레이어 두께."

msgctxt "support_skip_zag_per_mm description"
msgid "Leave out a connection between support lines once every N millimeter to make the support structure easier to break away."
msgstr "서포트 구조를 쉽게 분리 할 수 있도록 N 밀리미터마다 서포트선 사이를 연결합니다."

msgctxt "z_seam_position option left"
msgid "Left"
msgstr "왼쪽"

msgctxt "cool_lift_head label"
msgid "Lift Head"
msgstr "리프트 헤드"

msgctxt "infill_pattern option lightning"
msgid "Lightning"
msgstr "라이트닝"

msgctxt "lightning_infill_overhang_angle label"
msgid "Lightning Infill Overhang Angle"
msgstr "라이트닝 내부채움 오버행 각도"

msgctxt "lightning_infill_prune_angle label"
msgid "Lightning Infill Prune Angle"
msgstr "라이트닝 내부채움 가지치기 각도"

msgctxt "lightning_infill_straightening_angle label"
msgid "Lightning Infill Straightening Angle"
msgstr "라이트닝 내부채움 정리 각도"

msgctxt "lightning_infill_support_angle label"
msgid "Lightning Infill Support Angle"
msgstr "라이트닝 내부채움 서포트 각도"

msgctxt "support_tree_limit_branch_reach label"
msgid "Limit Branch Reach"
msgstr "브랜치 도달 거리 제한"

msgctxt "support_tree_limit_branch_reach description"
msgid "Limit how far each branch should travel from the point it supports. This can make the support more sturdy, but will increase the amount of branches (and because of that material usage/print time)"
msgstr "각 브랜치가 서포트하는 지점에서 뻗어 나가는 거리를 제한합니다. 이렇게 하면 서포트가 더 견고해질 수 있지만, 브랜치의 양이 늘어나므로 재료 사용량/프린트 시간이 늘어납니다."

msgctxt "bv_temp_warn_limit description"
msgid "Limit on Build Volume Temperature warning for detection."
msgstr "감지 시의 빌드 볼륨 온도 제한 경고입니다."

msgctxt "bv_temp_anomaly_limit description"
msgid "Limit on Build Volume temperature Anomaly for detection."
msgstr "감지 시의 비정상적인 빌드 볼륨 온도 제한입니다. "

msgctxt "print_temp_anomaly_limit description"
msgid "Limit on Print Temperature anomaly for detection."
msgstr "감지 시의 비정상적인 출력 온도 제한입니다."

msgctxt "print_temp_warn_limit description"
msgid "Limit on Print temperature warning for detection."
msgstr "감지 시의 출력 온도 제한 경고입니다."

msgctxt "flow_anomaly_limit description"
msgid "Limit on flow anomaly for detection."
msgstr "감지 시의 비정상적인 유량 제한입니다."

msgctxt "flow_warn_limit description"
msgid "Limit on the flow warning for detection."
msgstr "감지 시의 유량 제한 경고입니다."

msgctxt "cutting_mesh description"
msgid "Limit the volume of this mesh to within other meshes. You can use this to make certain areas of one mesh print with different settings and with a whole different extruder."
msgstr "이 메쉬의 볼륨을 다른 메쉬 내로 제한합니다. 이 기능을 사용하면 다른 설정과 전체 익스트루더로 하나의 메쉬 프린팅 영역을 만들 수 있습니다."

msgctxt "draft_shield_height_limitation option limited"
msgid "Limited"
msgstr "제한된"

msgctxt "line_width label"
msgid "Line Width"
msgstr "선의 두께"

msgctxt "infill_pattern option lines"
msgid "Lines"
msgstr "라인"

msgctxt "roofing_pattern option lines"
msgid "Lines"
msgstr "윤곽"

msgctxt "support_bottom_pattern option lines"
msgid "Lines"
msgstr "라인"

msgctxt "support_interface_pattern option lines"
msgid "Lines"
msgstr "라인"

msgctxt "support_pattern option lines"
msgid "Lines"
msgstr "라인"

msgctxt "support_roof_pattern option lines"
msgid "Lines"
msgstr "라인"

msgctxt "top_bottom_pattern option lines"
msgid "Lines"
msgstr "라인"

msgctxt "top_bottom_pattern_0 option lines"
msgid "Lines"
msgstr "윤곽"

msgctxt "machine_gcode_flavor option MACH3"
msgid "Mach3"
msgstr "Mach3"

msgctxt "machine_settings label"
msgid "Machine"
msgstr "기기"

msgctxt "machine_depth label"
msgid "Machine Depth"
msgstr "기기 깊이"

msgctxt "machine_head_with_fans_polygon label"
msgid "Machine Head & Fan Polygon"
msgstr "머신 헤드 및 팬 폴리곤"

msgctxt "machine_height label"
msgid "Machine Height"
msgstr "기기 높이"

msgctxt "machine_name label"
msgid "Machine Type"
msgstr "기기 유형"

msgctxt "machine_width label"
msgid "Machine Width"
msgstr "기기 너비"

msgctxt "machine_settings description"
msgid "Machine specific settings"
msgstr "기기 세부 설정"

msgctxt "conical_overhang_enabled label"
msgid "Make Overhang Printable"
msgstr "오버행이 프린팅되도록 설정"

msgctxt "multiple_mesh_overlap description"
msgid "Make meshes which are touching each other overlap a bit. This makes them bond together better."
msgstr "서로 닿는 메쉬가 조금 겹치게 만듭니다. 이것은 그들을 더 잘 묶는 것입니다."

msgctxt "support_conical_enabled description"
msgid "Make support areas smaller at the bottom than at the overhang."
msgstr "오버행보다 하단에서 지지대 영역을 작게 만듭니다."

msgctxt "support_mesh_drop_down description"
msgid "Make support everywhere below the support mesh, so that there's no overhang in the support mesh."
msgstr "서포트 메쉬 아래의 모든 부분을 지원하여서 서포트 메쉬에 오버행이 없습니다."

msgctxt "extruder_prime_pos_abs description"
msgid "Make the extruder prime position absolute rather than relative to the last-known location of the head."
msgstr "익스투루더의 위치를 헤드의 마지막으로 알려진 위치에 상대위치가 아닌 절대 위치로 만듭니다."

msgctxt "layer_0_z_overlap description"
msgid "Make the first and second layer of the model overlap in the Z direction to compensate for the filament lost in the airgap. All models above the first model layer will be shifted down by this amount.\nIt may be noted that sometimes the second layer is printed below initial layer because of this setting. This is intended behavior"
msgstr "모델의 첫 번째 및 두 번째 레이어를 Z 방향으로 겹쳐서 에어 갭에서 손실된 필라멘트를 보완합니다. 첫 번째 모델 레이어 위의 모든 모델은 이 값만큼 아래로 이동합니다."
"이 설정으로 인해 두 번째 레이어가 초기 레이어 아래에 출력되는 경우가 있습니다. 이는 의도된 동작입니다."

msgctxt "meshfix description"
msgid "Make the meshes more suited for 3D printing."
msgstr "메쉬를 3D 프린팅에 보다 맞춤화시킵니다."

msgctxt "machine_gcode_flavor option Makerbot"
msgid "Makerbot"
msgstr "Makerbot"

msgctxt "machine_gcode_flavor option RepRap (Marlin/Sprinter)"
msgid "Marlin"
msgstr "Marlin"

msgctxt "machine_gcode_flavor option RepRap (Volumetric)"
msgid "Marlin (Volumetric)"
msgstr "Marlin (부피 측정법)"

msgctxt "material description"
msgid "Material"
msgstr "재료"

msgctxt "material label"
msgid "Material"
msgstr "재료"

msgctxt "material_brand label"
msgid "Material Brand"
msgstr "재료 브랜드"

msgctxt "material_guid label"
msgid "Material GUID"
msgstr "재료 GUID"

msgctxt "material_type label"
msgid "Material Type"
msgstr "재료 유형"

msgctxt "max_extrusion_before_wipe label"
msgid "Material Volume Between Wipes"
msgstr "와이프 사이의 재료 볼륨"

msgctxt "retraction_combing_max_distance label"
msgid "Max Comb Distance With No Retract"
msgstr "수축이 없을 때 최대 빗질 거리"

msgctxt "machine_max_acceleration_x label"
msgid "Maximum Acceleration X"
msgstr "최대 가속도 X"

msgctxt "machine_max_acceleration_y label"
msgid "Maximum Acceleration Y"
msgstr "Y 방향 최대 가속도"

msgctxt "machine_max_acceleration_z label"
msgid "Maximum Acceleration Z"
msgstr "Z 방향 최대 가속도"

msgctxt "support_tree_angle label"
msgid "Maximum Branch Angle"
msgstr "최대 브랜치 각도"

msgctxt "meshfix_maximum_deviation label"
msgid "Maximum Deviation"
msgstr "최대 편차"

msgctxt "meshfix_maximum_extrusion_area_deviation label"
msgid "Maximum Extrusion Area Deviation"
msgstr "최대 압출 영역 편차"

msgctxt "cool_fan_speed_max label"
msgid "Maximum Fan Speed"
msgstr "최대 팬 속도"

msgctxt "machine_max_acceleration_e label"
msgid "Maximum Filament Acceleration"
msgstr "최대 필라멘트 가속도"

msgctxt "conical_overhang_angle label"
msgid "Maximum Model Angle"
msgstr "최대 모델 각도"

msgctxt "conical_overhang_hole_size label"
msgid "Maximum Overhang Hole Area"
msgstr "최대 오버행 홀 영역"

msgctxt "material_maximum_park_duration label"
msgid "Maximum Park Duration"
msgstr "최대 파크 기간"

msgctxt "meshfix_maximum_resolution label"
msgid "Maximum Resolution"
msgstr "최대 해상도"

msgctxt "retraction_count_max label"
msgid "Maximum Retraction Count"
msgstr "최대 리트렉션 수"

msgctxt "max_skin_angle_for_expansion label"
msgid "Maximum Skin Angle for Expansion"
msgstr "확장을 위한 최대 스킨 각"

msgctxt "machine_max_feedrate_e label"
msgid "Maximum Speed E"
msgstr "최대 속도 E"

msgctxt "machine_max_feedrate_x label"
msgid "Maximum Speed X"
msgstr "X 방향 최대 속도"

msgctxt "machine_max_feedrate_y label"
msgid "Maximum Speed Y"
msgstr "Y 방향 최대 속도"

msgctxt "machine_max_feedrate_z label"
msgid "Maximum Speed Z"
msgstr "Z 방향 최대 속도"

msgctxt "support_tower_maximum_supported_diameter label"
msgid "Maximum Tower-Supported Diameter"
msgstr "최대 타워 지지 직경"

msgctxt "meshfix_maximum_travel_resolution label"
msgid "Maximum Travel Resolution"
msgstr "최대 이동 해상도"

msgctxt "machine_max_acceleration_x description"
msgid "Maximum acceleration for the motor of the X-direction"
msgstr "X 방향 모터의 최대 가속도"

msgctxt "machine_max_acceleration_y description"
msgid "Maximum acceleration for the motor of the Y-direction."
msgstr "Y 방향 모터의 최대 가속도."

msgctxt "machine_max_acceleration_z description"
msgid "Maximum acceleration for the motor of the Z-direction."
msgstr "Z 방향 모터의 최대 가속도."

msgctxt "machine_max_acceleration_e description"
msgid "Maximum acceleration for the motor of the filament."
msgstr "필라멘트를 구동하는 모터의 최대 가속도."

msgctxt "bridge_sparse_infill_max_density description"
msgid "Maximum density of infill considered to be sparse. Skin over sparse infill is considered to be unsupported and so may be treated as a bridge skin."
msgstr "희박하다고 여겨지는 내부채움의 최대 밀도 희박한 내부채움의 스킨은 지원되지 않는 것으로 간주되므로 브릿지 스킨으로 취급할 수 있습니다."

msgctxt "support_tower_maximum_supported_diameter description"
msgid "Maximum diameter in the X/Y directions of a small area which is to be supported by a specialized support tower."
msgstr "특수 지지대 타워에 의해서 지지될 작은 영역의 X/Y 방향의 최대 직경입니다."

msgctxt "max_extrusion_before_wipe description"
msgid "Maximum material that can be extruded before another nozzle wipe is initiated. If this value is less than the volume of material required in a layer, the setting has no effect in this layer, i.e. it is limited to one wipe per layer."
msgstr "다른 노즐 와이프를 시작하기 전에 압출 성형할 수 있는 최대 재료입니다. 이 값이 레이어에 필요한 재료의 양보다 작으면 이 레이어에서는 아무런 효과가 없습니다. 즉, 레이어당 한번 와이프하는 것으로 제한됩니다."

msgctxt "multiple_mesh_overlap label"
msgid "Merged Meshes Overlap"
msgstr "중복된 메쉬 합치기"

msgctxt "meshfix label"
msgid "Mesh Fixes"
msgstr "메쉬 수정"

msgctxt "mesh_position_x label"
msgid "Mesh Position X"
msgstr "메쉬 위치 X"

msgctxt "mesh_position_y label"
msgid "Mesh Position Y"
msgstr "메쉬 위치 Y"

msgctxt "mesh_position_z label"
msgid "Mesh Position Z"
msgstr "메쉬 위치 Z"

msgctxt "infill_mesh_order label"
msgid "Mesh Processing Rank"
msgstr "메쉬 처리 랭크"

msgctxt "mesh_rotation_matrix label"
msgid "Mesh Rotation Matrix"
msgstr "메쉬 회전 행렬"

msgctxt "slicing_tolerance option middle"
msgid "Middle"
msgstr "중간"

msgctxt "mold_width label"
msgid "Minimal Mold Width"
msgstr "최소 몰드 너비"

msgctxt "machine_min_cool_heat_time_window label"
msgid "Minimal Time Standby Temperature"
msgstr "최소 대기 시간"

msgctxt "bridge_wall_min_length label"
msgid "Minimum Bridge Wall Length"
msgstr "최소 브리지 벽 길이"

msgctxt "min_even_wall_line_width label"
msgid "Minimum Even Wall Line Width"
msgstr "최소 짝수 벽 선 너비"

msgctxt "retraction_extrusion_window label"
msgid "Minimum Extrusion Distance Window"
msgstr "최소 압출 영역"

msgctxt "min_feature_size label"
msgid "Minimum Feature Size"
msgstr "최소 피처 크기"

msgctxt "machine_minimum_feedrate label"
msgid "Minimum Feedrate"
msgstr "최소 이송 속도"

msgctxt "support_tree_min_height_to_model label"
msgid "Minimum Height To Model"
msgstr "모델에 대한 최소 높이"

msgctxt "min_infill_area label"
msgid "Minimum Infill Area"
msgstr "최소 내부채움 지역"

msgctxt "cool_min_layer_time label"
msgid "Minimum Layer Time"
msgstr "최소 레이어 시간"

msgctxt "min_odd_wall_line_width label"
msgid "Minimum Odd Wall Line Width"
msgstr "최소 홀수 벽 선 너비"

msgctxt "minimum_polygon_circumference label"
msgid "Minimum Polygon Circumference"
msgstr "최소 다각형 둘레"

msgctxt "min_skin_width_for_expansion label"
msgid "Minimum Skin Width for Expansion"
msgstr "확장을 위한 최소 스킨 폭"

msgctxt "cool_min_speed label"
msgid "Minimum Speed"
msgstr "최저 속도"

msgctxt "minimum_support_area label"
msgid "Minimum Support Area"
msgstr "최소 서포트 지역"

msgctxt "minimum_bottom_area label"
msgid "Minimum Support Floor Area"
msgstr "최소 서포트 바닥 지역"

msgctxt "minimum_interface_area label"
msgid "Minimum Support Interface Area"
msgstr "최소 서포트 인터페이스 지역"

msgctxt "minimum_roof_area label"
msgid "Minimum Support Roof Area"
msgstr "최소 서포트 지붕 지역"

msgctxt "support_xy_distance_overhang label"
msgid "Minimum Support X/Y Distance"
msgstr "최소 서포트 X/Y 거리"

msgctxt "min_bead_width label"
msgid "Minimum Thin Wall Line Width"
msgstr "얇은 벽 선 최소 너비"

msgctxt "coasting_min_volume label"
msgid "Minimum Volume Before Coasting"
msgstr "코스팅(Coasting) 최소 양"

msgctxt "min_wall_line_width label"
msgid "Minimum Wall Line Width"
msgstr "최소 벽 선 너비"

msgctxt "minimum_interface_area description"
msgid "Minimum area size for support interface polygons. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "지원 인터페이스 다각형의 최소 영역 크기입니다. 이 값보다 작은 영역을 갖는 다각형은 정상적인 지원으로 인쇄됩니다."

msgctxt "minimum_support_area description"
msgid "Minimum area size for support polygons. Polygons which have an area smaller than this value will not be generated."
msgstr "서포트 영역에 대한 최소 지역 크기. 이 값보다 작은 지역을 갖는 영역은 생성되지 않습니다."

msgctxt "minimum_bottom_area description"
msgid "Minimum area size for the floors of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "지원 바닥의 최소 면적 크기입니다. 이 값보다 작은 영역을 갖는 다각형은 정상적인 지원으로 인쇄됩니다."

msgctxt "minimum_roof_area description"
msgid "Minimum area size for the roofs of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "서포트 지붕에 대한 최소 면적 크기입니다. 이 값보다 작은 영역을 갖는 다각형은 정상적인 지원으로 인쇄됩니다."

msgctxt "min_feature_size description"
msgid "Minimum thickness of thin features. Model features that are thinner than this value will not be printed, while features thicker than the Minimum Feature Size will be widened to the Minimum Wall Line Width."
msgstr "얇은 피처의 최소 두께 이 값보다 더 얇은 모델 피처는 프린트되지 않으며, 최소 피처 크기보다 더 두꺼운 피처는 최소 벽 선 너비로 넓어집니다."

msgctxt "support_conical_min_width description"
msgid "Minimum width to which the base of the conical support area is reduced. Small widths can lead to unstable support structures."
msgstr "원추형서포트 영역의 베이스가 축소되는 최소 너비. 폭이 좁으면 불안정한 서포트 구조가 생길 수 있습니다."

msgctxt "mold_enabled label"
msgid "Mold"
msgstr "몰드"

msgctxt "mold_angle label"
msgid "Mold Angle"
msgstr "몰드 각도"

msgctxt "mold_roof_height label"
msgid "Mold Roof Height"
msgstr "몰드 지붕 높이"

msgctxt "ironing_monotonic label"
msgid "Monotonic Ironing Order"
msgstr "단면 다림질 순서"

msgctxt "raft_surface_monotonic label"
msgid "Monotonic Raft Top Surface Order"
msgstr "단면 래프트 상단 표면 순서"

msgctxt "roofing_monotonic label"
msgid "Monotonic Top Surface Order"
msgstr "단면 상단 표면 순서"

msgctxt "skin_monotonic label"
msgid "Monotonic Top/Bottom Order"
msgstr "단면 상단/하단 순서"

msgctxt "skirt_line_count description"
msgid "Multiple skirt lines help to prime your extrusion better for small models. Setting this to 0 will disable the skirt."
msgstr "여러 개의 스커트 라인을 사용하여 작은 모델에 더 잘 압출 성형 할 수 있습니다. 이것을 0으로 설정하면 스커트가 비활성화됩니다."

msgctxt "initial_layer_line_width_factor description"
msgid "Multiplier of the line width on the first layer. Increasing this could improve bed adhesion."
msgstr "첫 번째 레이어의 라인 폭 승수입니다. 이것을 늘리면 베드 접착력을 향상시킬 수 있습니다."

msgctxt "material_no_load_move_factor label"
msgid "No Load Move Factor"
msgstr "로드 이동 요인 없음"

msgctxt "skin_no_small_gaps_heuristic label"
msgid "No Skin in Z Gaps"
msgstr "Z 간격에 스킨 없음"

msgctxt "blackmagic description"
msgid "Non-traditional ways to print your models."
msgstr "모델을 프린팅하는 새로운 방법들."

msgctxt "adhesion_type option none"
msgid "None"
msgstr "None"

msgctxt "z_seam_corner option z_seam_corner_none"
msgid "None"
msgstr "없음"

msgctxt "magic_mesh_surface_mode option normal"
msgid "Normal"
msgstr "표준"

msgctxt "prime_tower_mode option normal"
msgid "Normal"
msgstr "일반"

msgctxt "support_structure option normal"
msgid "Normal"
msgstr "표준"

msgctxt "meshfix_keep_open_polygons description"
msgid "Normally Cura tries to stitch up small holes in the mesh and remove parts of a layer with big holes. Enabling this option keeps those parts which cannot be stitched. This option should be used as a last resort option when everything else fails to produce proper g-code."
msgstr "일반적으로 큐라(Cura)는 메쉬의 작은 구멍을 꿰매 붙이고 큰 구멍이있는 레이어의 부분을 제거하려고합니다. 이 옵션을 활성화하면 스티칭 할 수 없는 파트가 유지됩니다. 이 옵션은 다른 모든 설정으로 올바른 GCode를 생성하지 못할 때 최후의 수단으로 사용해야 합니다."

msgctxt "retraction_combing option noskin"
msgid "Not in Skin"
msgstr "스킨에 없음"

msgctxt "retraction_combing option no_outer_surfaces"
msgid "Not on Outer Surface"
msgstr "외부 표면에 없음"

msgctxt "machine_nozzle_expansion_angle label"
msgid "Nozzle Angle"
msgstr "노즐 각도"

msgctxt "machine_nozzle_size label"
msgid "Nozzle Diameter"
msgstr "노즐 지름"

msgctxt "nozzle_disallowed_areas label"
msgid "Nozzle Disallowed Areas"
msgstr "노즐이 위치할 수 없는 구역"

msgctxt "machine_nozzle_id label"
msgid "Nozzle ID"
msgstr "노즐 ID"

msgctxt "machine_nozzle_head_distance label"
msgid "Nozzle Length"
msgstr "노즐 길이"

msgctxt "switch_extruder_extra_prime_amount label"
msgid "Nozzle Switch Extra Prime Amount"
msgstr "노즐 스위치 엑스트라 프라임 양"

msgctxt "switch_extruder_prime_speed label"
msgid "Nozzle Switch Prime Speed"
msgstr "노즐 스위치 프라임 속도"

msgctxt "switch_extruder_retraction_speed label"
msgid "Nozzle Switch Retract Speed"
msgstr "노즐 스위치 후퇴 속도"

msgctxt "switch_extruder_retraction_amount label"
msgid "Nozzle Switch Retraction Distance"
msgstr "노즐 스위치 리트렉션 거리"

msgctxt "switch_extruder_retraction_speeds label"
msgid "Nozzle Switch Retraction Speed"
msgstr "노즐 스위치 리트렉션 속도"

msgctxt "machine_extruder_count label"
msgid "Number of Extruders"
msgstr "익스트루더의 수"

msgctxt "extruders_enabled_count label"
msgid "Number of Extruders That Are Enabled"
msgstr "활성화된 익스트루더의 수"

msgctxt "speed_slowdown_layers label"
msgid "Number of Slower Layers"
msgstr "느리게 프린팅할 레이어의 수"

msgctxt "extruders_enabled_count description"
msgid "Number of extruder trains that are enabled; automatically set in software"
msgstr "사용 가능한 익스트루더 수; 소프트웨어로 자동 설정"

msgctxt "machine_extruder_count description"
msgid "Number of extruder trains. An extruder train is the combination of a feeder, bowden tube, and nozzle."
msgstr "익스트루더의 수. 익스트루더는 피더, 보우 덴 튜브 및 노즐의 조합입니다."

msgctxt "wipe_repeat_count description"
msgid "Number of times to move the nozzle across the brush."
msgstr "브러시 전체에 노즐을 이동하는 횟수입니다."

msgctxt "gradual_infill_steps description"
msgid "Number of times to reduce the infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Infill Density."
msgstr "상단면 아래로 갈 때 내부채움 밀도를 반으로 줄이는 횟수입니다. 상단면에 더 가까운 영역은 내부채움율 농도가 더 높은 밀도를 갖습니다."

msgctxt "gradual_support_infill_steps description"
msgid "Number of times to reduce the support infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Support Infill Density."
msgstr "상단 표면 아래로 올라갈 때 서포트 채움 밀도를 반으로 줄이는 횟수입니다. 상단 표면에 더 가까운 영역은 서포트 채움 밀도까지 더 높은 밀도를 갖습니다."

msgctxt "infill_pattern option tetrahedral"
msgid "Octet"
msgstr "옥텟"

msgctxt "retraction_combing option off"
msgid "Off"
msgstr "끔"

msgctxt "mesh_position_x description"
msgid "Offset applied to the object in the x direction."
msgstr "x 방향으로 객체에 적용된 오프셋입니다."

msgctxt "mesh_position_y description"
msgid "Offset applied to the object in the y direction."
msgstr "y 방향으로 객체에 적용된 오프셋입니다."

msgctxt "mesh_position_z description"
msgid "Offset applied to the object in the z direction. With this you can perform what was used to be called 'Object Sink'."
msgstr "z 방향으로 객체에 적용된 오프셋입니다. 이것을 사용하여 '오프젝 싱크(Object Sink)'라고 불렀던 것을 수행 할 수 있습니다."

msgctxt "machine_use_extruder_offset_to_offset_coords label"
msgid "Offset with Extruder"
msgstr "익스트루더로 오프셋"

msgctxt "support_tree_rest_preference option buildplate"
msgid "On buildplate when possible"
msgstr "가능한 경우 빌드 플레이트에"

msgctxt "support_tree_rest_preference option graceful"
msgid "On model if required"
msgstr "필요한 경우 모델에"

msgctxt "print_sequence option one_at_a_time"
msgid "One at a Time"
msgstr "한번에 하나씩"

msgctxt "retraction_hop_only_when_collides description"
msgid "Only perform a Z Hop when moving over printed parts which cannot be avoided by horizontal motion by Avoid Printed Parts when Traveling."
msgstr "이동 시, 수평 이동으로 피할 수없는 출력물 위로 이동할 때만 Z 홉을 수행하십시오."

msgctxt "ironing_only_highest_layer description"
msgid "Only perform ironing on the very last layer of the mesh. This saves time if the lower layers don't need a smooth surface finish."
msgstr "메쉬의 마지막 레이어에서만 다림질을 수행합니다. 이것은 낮은 레이어에서 매끄러운 표면 처리가 필요하지 않은 경우 시간을 절약 할 수 있습니다."

msgctxt "ooze_shield_angle label"
msgid "Ooze Shield Angle"
msgstr "Ooze 쉴드 각"

msgctxt "ooze_shield_dist label"
msgid "Ooze Shield Distance"
msgstr "Ooze 쉴드 거리"

msgctxt "support_tree_branch_reach_limit label"
msgid "Optimal Branch Range"
msgstr "최적 브랜치 범위"

msgctxt "optimize_wall_printing_order label"
msgid "Optimize Wall Printing Order"
msgstr "벽면 프린팅 순서 명령 최적화"

msgctxt "optimize_wall_printing_order description"
msgid "Optimize the order in which walls are printed so as to reduce the number of retractions and the distance travelled. Most parts will benefit from this being enabled but some may actually take longer so please compare the print time estimates with and without optimization. First layer is not optimized when choosing brim as build plate adhesion type."
msgstr "수축 및 이동 거리를 줄이도록 벽이 인쇄되는 순서를 최적화합니다. 대부분의 부품은 이 기능을 사용하면 도움이 되지만 실제로는 시간이 오래 걸릴 수 있으므로, 최적화 여부와 관계없이 인쇄 시간을 비교하십시오. 빌드 플레이트 접착 유형을 Brim으로 선택하는 경우 첫 번째 층이 최적화되지 않습니다."

msgctxt "machine_nozzle_tip_outer_diameter label"
msgid "Outer Nozzle Diameter"
msgstr "외부 노즐의 외경"

msgctxt "acceleration_wall_0 label"
msgid "Outer Wall Acceleration"
msgstr "외벽 가속도"

msgctxt "wall_0_extruder_nr label"
msgid "Outer Wall Extruder"
msgstr "외벽 익스트루더"

msgctxt "wall_0_material_flow label"
msgid "Outer Wall Flow"
msgstr "외벽 압출량"

msgctxt "wall_0_inset label"
msgid "Outer Wall Inset"
msgstr "외벽 삽입"

msgctxt "jerk_wall_0 label"
msgid "Outer Wall Jerk"
msgstr "외벽 Jerk"

msgctxt "wall_line_width_0 label"
msgid "Outer Wall Line Width"
msgstr "바깥 선 선폭"

msgctxt "speed_wall_0 label"
msgid "Outer Wall Speed"
msgstr "외벽 속도"

msgctxt "wall_0_wipe_dist label"
msgid "Outer Wall Wipe Distance"
msgstr "외벽 이동 거리"

msgctxt "group_outer_walls description"
msgid "Outer walls of different islands in the same layer are printed in sequence. When enabled the amount of flow changes is limited because walls are printed one type at a time, when disabled the number of travels between islands is reduced because walls in the same islands are grouped."
msgstr "동일한 레이어의 서로 다른 섬의 외벽이 순차적으로 인쇄됩니다. 활성화되면 벽 종류별로 하나씩 인쇄되므로 유량 변화량이 제한되며 비활성화되면 동일한 섬의 벽이 그룹화되어 섬 간 이동 수가 감소합니다."

msgctxt "brim_location option outside"
msgid "Outside Only"
msgstr "외부만"

msgctxt "inset_direction option outside_in"
msgid "Outside To Inside"
msgstr "외부에서 내부로"

msgctxt "wall_overhang_angle label"
msgid "Overhanging Wall Angle"
msgstr "오버행된 벽 각도"

msgctxt "wall_overhang_speed_factor label"
msgid "Overhanging Wall Speed"
msgstr "오버행된 벽 속도"

msgctxt "wall_overhang_speed_factor description"
msgid "Overhanging walls will be printed at this percentage of their normal print speed."
msgstr "오버행된 벽은 정상적인 인쇄 속도의 이 비율로 인쇄됩니다."

msgctxt "wipe_pause description"
msgid "Pause after the unretract."
msgstr "리트랙트를 실행 취소한 후 일시 정지합니다."

msgctxt "bridge_fan_speed description"
msgid "Percentage fan speed to use when printing bridge walls and skin."
msgstr "브리지 벽과 스킨을 인쇄 할 때 사용하는 팬 속도 백분율."

msgctxt "bridge_fan_speed_2 description"
msgid "Percentage fan speed to use when printing the second bridge skin layer."
msgstr "두번째 브리지 벽과 스킨을 인쇄 할 때 사용하는 팬 속도 백분율."

msgctxt "support_supported_skin_fan_speed description"
msgid "Percentage fan speed to use when printing the skin regions immediately above the support. Using a high fan speed can make the support easier to remove."
msgstr "서포트 바로 위의 스킨 영역을 인쇄할 때 사용할 팬 속도 백분율 빠른 팬 속도를 사용하면 서포트를 더 쉽게 제거할 수 있습니다."

msgctxt "bridge_fan_speed_3 description"
msgid "Percentage fan speed to use when printing the third bridge skin layer."
msgstr "세번째 브리지 벽과 스킨을 인쇄 할 때 사용하는 팬 속도 백분율."

msgctxt "minimum_polygon_circumference description"
msgid "Polygons in sliced layers that have a circumference smaller than this amount will be filtered out. Lower values lead to higher resolution mesh at the cost of slicing time. It is meant mostly for high resolution SLA printers and very tiny 3D models with a lot of details."
msgstr "레이어가 슬라이스 된, 이 값보다 둘레가 작은 다각형은 필터링됩니다. 값을 낮을수록 슬라이스가 느려지지만, 해상도 메쉬가 높아집니다. 주로 고해상도 SLA 프린터 및 세부 사항이 많은 매우 작은 3D 모델에 적합합니다."

msgctxt "support_tree_angle_slow label"
msgid "Preferred Branch Angle"
msgstr "기본 브랜치 각도"

msgctxt "wall_transition_filter_deviation description"
msgid "Prevent transitioning back and forth between one extra wall and one less. This margin extends the range of line widths which follow to [Minimum Wall Line Width - Margin, 2 * Minimum Wall Line Width + Margin]. Increasing this margin reduces the number of transitions, which reduces the number of extrusion starts/stops and travel time. However, large line width variation can lead to under- or overextrusion problems."
msgstr "하나의 여분 벽과 하나 더 적은 벽 사이를 왔다 갔다 하는 전환을 방지하십시오. 이 여백은 [최소 벽 선 너비 - 여백, 2 * 최소 벽 선 너비 + 여백]을 따르는 선 너비의 범위를 확장합니다. 이 여백을 늘리면 전환 횟수가 줄어들어, 압출 시작/중지 및 이동 시간이 줄어듭니다. 그러나 선 너비 변동이 크면 압출 미달 또는 과잉 문제가 발생할 수 있습니다."

msgctxt "acceleration_prime_tower label"
msgid "Prime Tower Acceleration"
msgstr "프라임 타워 가속"

msgctxt "prime_tower_brim_enable label"
msgid "Prime Tower Base"
msgstr "프라임 타워 베이스"

msgctxt "prime_tower_base_height label"
msgid "Prime Tower Base Height"
msgstr "프라임 타워 베이스 높이"

msgctxt "prime_tower_base_size label"
msgid "Prime Tower Base Size"
msgstr "프라임 타워 베이스 크기"

msgctxt "prime_tower_base_curve_magnitude label"
msgid "Prime Tower Base Slope"
msgstr "프라임 타워 베이스 경사"

msgctxt "prime_tower_flow label"
msgid "Prime Tower Flow"
msgstr "프라임 타워 압출량"

msgctxt "jerk_prime_tower label"
msgid "Prime Tower Jerk"
msgstr "프라임 타워 Jerk"

msgctxt "prime_tower_line_width label"
msgid "Prime Tower Line Width"
msgstr "프라임 타워 라인 폭"

msgctxt "prime_tower_max_bridging_distance label"
msgid "Prime Tower Maximum Bridging Distance"
msgstr "프라임 타워 최대 브리징 거리"

msgctxt "prime_tower_min_volume label"
msgid "Prime Tower Minimum Volume"
msgstr "프라임 타워 최소 볼륨"

msgctxt "prime_tower_raft_base_line_spacing label"
msgid "Prime Tower Raft Line Spacing"
msgstr "프라임 타워 래프트 선 간격"

msgctxt "prime_tower_size label"
msgid "Prime Tower Size"
msgstr "프라임 타워 사이즈"

msgctxt "speed_prime_tower label"
msgid "Prime Tower Speed"
msgstr "프라임 타워 속도"

msgctxt "prime_tower_mode label"
msgid "Prime Tower Type"
msgstr "프라임 타워 유형"

msgctxt "prime_tower_position_x label"
msgid "Prime Tower X Position"
msgstr "프라임 타워 X 위치"

msgctxt "prime_tower_position_y label"
msgid "Prime Tower Y Position"
msgstr "프라임 타워 Y 위치"

msgctxt "acceleration_print label"
msgid "Print Acceleration"
msgstr "프린팅 가속도"

msgctxt "jerk_print label"
msgid "Print Jerk"
msgstr "Jerk 프린팅"

msgctxt "ppr label"
msgid "Print Process Reporting"
msgstr "출력 과정 보고"

msgctxt "print_sequence label"
msgid "Print Sequence"
msgstr "프린팅 순서"

msgctxt "speed_print label"
msgid "Print Speed"
msgstr "프린팅 속도"

msgctxt "fill_outline_gaps label"
msgid "Print Thin Walls"
msgstr "얇은 벽 프린팅"

msgctxt "brim_location description"
msgid "Print a brim on the outside of the model, inside, or both. Depending on the model, this helps reducing the amount of brim you need to remove afterwards, while ensuring a proper bed adhesion."
msgstr "모델 외부, 내부 또는 양쪽 모두에 브림을 출력합니다. 모델에 따라 나중에 제거해야 하는 브림의 양을 줄이는 데 도움이 될 수 있으며, 베드에 적절하게 밀착시킬 수 있습니다."

msgctxt "prime_tower_enable description"
msgid "Print a tower next to the print which serves to prime the material after each nozzle switch."
msgstr "각 노즐을 교체 한 후에 재료를 프라이밍(Priming)하는 프린팅 옆에 타워를 프린팅하십시오."

msgctxt "infill_support_enabled description"
msgid "Print infill structures only where tops of the model should be supported. Enabling this reduces print time and material usage, but leads to ununiform object strength."
msgstr "모델 상단이 지지가 되어야 하는 경우에만 충진물 구조를 인쇄합니다. 이 기능을 사용하면 인쇄 시간 및 재료 사용이 감소하지만, 개체 강도가 균일하지 않습니다."

msgctxt "ironing_monotonic description"
msgid "Print ironing lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "한 방향으로 주변 라인들과 항상 겹치게 하기 위해 다림질 라인들을 배치하여 프린트하십시오. 이는 프린트하는 데 더 많은 시간이 소모되지만 평평한 면이 보다 일관적으로 보이게 합니다."

msgctxt "mold_enabled description"
msgid "Print models as a mold, which can be cast in order to get a model which resembles the models on the build plate."
msgstr "모형을 몰드으로 프린팅하여 모형에 몰드과 유사한 모형을 만들 수 있습니다."

msgctxt "fill_outline_gaps description"
msgid "Print pieces of the model which are horizontally thinner than the nozzle size."
msgstr "노즐 크기보다 수평으로 더 얇은 모델 조각을 프린팅하십시오."

msgctxt "raft_surface_monotonic description"
msgid "Print raft top surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes the surface look more consistent, which is also visible on the model bottom surface."
msgstr "래프트 상단 표면 선이 항상 한 방향으로 인접한 선과 겹치도록 순서대로 출력합니다. 출력 시간은 약간 더 소요되지만, 모델 하단 표면에서도 보이는 표면을 더 일관적으로 보이게 만들 수 있습니다."

msgctxt "bridge_skin_speed_2 description"
msgid "Print speed to use when printing the second bridge skin layer."
msgstr "두번째 브릿지 스킨 레이어를 인쇄 할 때 사용할 인쇄 속도."

msgctxt "bridge_skin_speed_3 description"
msgid "Print speed to use when printing the third bridge skin layer."
msgstr "세번째 브릿지 스킨 레이어를 인쇄 할 때 사용할 인쇄 속도."

msgctxt "print_temp_anomaly_limit label"
msgid "Print temperature Limit"
msgstr "출력 온도 제한"

msgctxt "print_temp_warn_limit label"
msgid "Print temperature Warning"
msgstr "출력 온도 경고"

msgctxt "infill_before_walls description"
msgid "Print the infill before printing the walls. Printing the walls first may lead to more accurate walls, but overhangs print worse. Printing the infill first leads to sturdier walls, but the infill pattern might sometimes show through the surface."
msgstr "벽을 프린팅하기 전에 내부채움물을 프린팅하기. 벽을 먼저 프린팅하면 벽이 더 정확해질 수 있지만 겹침으로 프린팅이 매끄럽지 않습니다. 내부채움을 먼저 프린팅하면 더 튼튼한 벽이 생기지만 내부채움 패턴이 때로 표면을 통해 보일 수 있습니다."

msgctxt "roofing_monotonic description"
msgid "Print top surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "한 방향으로 주변 라인들과 항상 겹치게 하기 위해 상단 표면 라인들을 배치하여 프린트하십시오. 이는 프린트하는 데 더 많은 시간이 소모되지만 평평한 면이 보다 일관적으로 보이게 합니다."

msgctxt "skin_monotonic description"
msgid "Print top/bottom lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "한 방향으로 주변 라인들과 항상 겹치게 하기 위해 상단/하단 라인들을 배치하여 프린트하십시오. 이는 프린트하는 데 더 많은 시간이 소모되지만 평평한 면이 보다 일관적으로 보이게 합니다."

msgctxt "material_print_temperature label"
msgid "Printing Temperature"
msgstr "프린팅 온도"

msgctxt "material_print_temperature_layer_0 label"
msgid "Printing Temperature Initial Layer"
msgstr "첫번째 레이어의 프린팅 온도"

msgctxt "skirt_height description"
msgid "Printing the innermost skirt line with multiple layers makes it easy to remove the skirt."
msgstr "가장 안쪽의 스커트 라인을 여러 겹으로 프린트하면 스커트를 쉽게 제거할 수 있습니다."

msgctxt "alternate_extra_perimeter description"
msgid "Prints an extra wall at every other layer. This way infill gets caught between these extra walls, resulting in stronger prints."
msgstr "다른 모든 레이어에 여분의 벽을 프린팅합니다. 이렇게하면 내부채움이 여분의 벽 사이에 끼어 더 강하게 프린팅됩니다."

msgctxt "resolution label"
msgid "Quality"
msgstr "품질"

msgctxt "infill_pattern option quarter_cubic"
msgid "Quarter Cubic"
msgstr "쿼터 큐빅"

msgctxt "adhesion_type option raft"
msgid "Raft"
msgstr "래프트"

msgctxt "raft_airgap label"
msgid "Raft Air Gap"
msgstr "래프트 에어 갭"

msgctxt "raft_base_margin label"
msgid "Raft Base Extra Margin"
msgstr "래프트 베이스 추가 여백"

msgctxt "raft_base_extruder_nr label"
msgid "Raft Base Extruder"
msgstr "래프트 베이스 익스트루더"

msgctxt "raft_base_fan_speed label"
msgid "Raft Base Fan Speed"
msgstr "래프트 기본 팬 속도"

msgctxt "raft_base_line_spacing label"
msgid "Raft Base Line Spacing"
msgstr "래프트 기준 선 간격"

msgctxt "raft_base_line_width label"
msgid "Raft Base Line Width"
msgstr "래프트 기준 선 너비"

msgctxt "raft_base_acceleration label"
msgid "Raft Base Print Acceleration"
msgstr "래프트 기본 프린팅 가속도"

msgctxt "raft_base_jerk label"
msgid "Raft Base Print Jerk"
msgstr "래프트 기본 프린팅 Jerk"

msgctxt "raft_base_speed label"
msgid "Raft Base Print Speed"
msgstr "래프트 기본 프린팅 속도"

msgctxt "raft_base_smoothing label"
msgid "Raft Base Smoothing"
msgstr "래프트 베이스 부드럽게 하기"

msgctxt "raft_base_thickness label"
msgid "Raft Base Thickness"
msgstr "래프트 기준 두께"

msgctxt "raft_base_wall_count label"
msgid "Raft Base Wall Count"
msgstr "래프트 베이스 벽 개수"

msgctxt "raft_margin label"
msgid "Raft Extra Margin"
msgstr "래프트 추가 여백"

msgctxt "raft_fan_speed label"
msgid "Raft Fan Speed"
msgstr "래프트 팬 속도"

msgctxt "raft_interface_margin label"
msgid "Raft Middle Extra Margin"
msgstr "래프트 중간 추가 여백"

msgctxt "raft_interface_extruder_nr label"
msgid "Raft Middle Extruder"
msgstr "래프트 중간 익스트루더"

msgctxt "raft_interface_fan_speed label"
msgid "Raft Middle Fan Speed"
msgstr "래프트 중앙 팬 속도"

msgctxt "raft_interface_layers label"
msgid "Raft Middle Layers"
msgstr "래프트 중간 레이어"

msgctxt "raft_interface_line_width label"
msgid "Raft Middle Line Width"
msgstr "래프트 중간 선 너비"

msgctxt "raft_interface_acceleration label"
msgid "Raft Middle Print Acceleration"
msgstr "래프트 중앙 프린팅 가속도"

msgctxt "raft_interface_jerk label"
msgid "Raft Middle Print Jerk"
msgstr "래프트 중앙 프린팅 Jerk"

msgctxt "raft_interface_speed label"
msgid "Raft Middle Print Speed"
msgstr "래프트 중앙 프린팅 속도"

msgctxt "raft_interface_smoothing label"
msgid "Raft Middle Smoothing"
msgstr "래프트 중간 부드럽게 하기"

msgctxt "raft_interface_line_spacing label"
msgid "Raft Middle Spacing"
msgstr "래프트 중간 간격"

msgctxt "raft_interface_thickness label"
msgid "Raft Middle Thickness"
msgstr "래프트 중간 두께"

msgctxt "raft_interface_wall_count label"
msgid "Raft Middle Wall Count"
msgstr "래프트 중간 벽 수"

msgctxt "raft_acceleration label"
msgid "Raft Print Acceleration"
msgstr "래프트 프린팅 가속도"

msgctxt "raft_jerk label"
msgid "Raft Print Jerk"
msgstr "래프트 프린팅 Jerk"

msgctxt "raft_speed label"
msgid "Raft Print Speed"
msgstr "래프트 프린팅 속도"

msgctxt "raft_smoothing label"
msgid "Raft Smoothing"
msgstr "래프트 부드럽게하기"

msgctxt "raft_surface_margin label"
msgid "Raft Top Extra Margin"
msgstr "래프트 상단 추가 여백"

msgctxt "raft_surface_extruder_nr label"
msgid "Raft Top Extruder"
msgstr "래프트 상단 익스트루더"

msgctxt "raft_surface_fan_speed label"
msgid "Raft Top Fan Speed"
msgstr "래프트 상단 팬 속도"

msgctxt "raft_surface_thickness label"
msgid "Raft Top Layer Thickness"
msgstr "래프트 상단 레이어 두께"

msgctxt "raft_surface_layers label"
msgid "Raft Top Layers"
msgstr "래프트 탑 레이어"

msgctxt "raft_surface_line_width label"
msgid "Raft Top Line Width"
msgstr "래프트 상단 선 너비"

msgctxt "raft_surface_acceleration label"
msgid "Raft Top Print Acceleration"
msgstr "래프트 상단 프린팅 가속도"

msgctxt "raft_surface_jerk label"
msgid "Raft Top Print Jerk"
msgstr "래프트 상단 프린팅 Jerk"

msgctxt "raft_surface_speed label"
msgid "Raft Top Print Speed"
msgstr "래프트 상단 프린팅 속도"

msgctxt "raft_surface_smoothing label"
msgid "Raft Top Smoothing"
msgstr "래프트 상단 부드럽게 하기"

msgctxt "raft_surface_line_spacing label"
msgid "Raft Top Spacing"
msgstr "래프트 상단 간격"

msgctxt "raft_surface_wall_count label"
msgid "Raft Top Wall Count"
msgstr "래프트 상단 벽 수"

msgctxt "raft_wall_count label"
msgid "Raft Wall Count"
msgstr "래프트 벽 수"

msgctxt "z_seam_type option random"
msgid "Random"
msgstr "랜덤"

msgctxt "infill_randomize_start_location label"
msgid "Randomize Infill Start"
msgstr "무작위 충전 시작"

msgctxt "infill_randomize_start_location description"
msgid "Randomize which infill line is printed first. This prevents one segment becoming the strongest, but it does so at the cost of an additional travel move."
msgstr "가장 먼저 프린트되는 충전 선을 무작위로 결정합니다. 이렇게 하면 특정 세그먼트가 가장 강한 세그먼트가 되는 일이 없지만, 추가 이동이 발생하지 않게 됩니다."

msgctxt "magic_fuzzy_skin_enabled description"
msgid "Randomly jitter while printing the outer wall, so that the surface has a rough and fuzzy look."
msgstr "외벽을 프린팅하는 동안 무작위로 지터가 발생하여 표면이 거칠고 흐릿해 보입니다."

msgctxt "machine_shape option rectangular"
msgid "Rectangular"
msgstr "직사각형"

msgctxt "cool_fan_speed_min label"
msgid "Regular Fan Speed"
msgstr "표준 팬 속도"

msgctxt "cool_fan_full_at_height label"
msgid "Regular Fan Speed at Height"
msgstr "표준 팬 속도시의 높이"

msgctxt "cool_fan_full_layer label"
msgid "Regular Fan Speed at Layer"
msgstr "표준 팬 속도시의 레이어"

msgctxt "cool_min_layer_time_fan_speed_max label"
msgid "Regular/Maximum Fan Speed Threshold"
msgstr "표준/최대 팬 속도 임계 값"

msgctxt "relative_extrusion label"
msgid "Relative Extrusion"
msgstr "상대적 압출"

msgctxt "meshfix_union_all_remove_holes label"
msgid "Remove All Holes"
msgstr "모든 구멍 제거"

msgctxt "remove_empty_first_layers label"
msgid "Remove Empty First Layers"
msgstr "비어 있는 첫 번째 레이어 제거"

msgctxt "carve_multiple_volumes label"
msgid "Remove Mesh Intersection"
msgstr "교차된 메쉬 제거"

msgctxt "raft_base_remove_inside_corners label"
msgid "Remove Raft Base Inside Corners"
msgstr "래프트 베이스 내부 모서리 제거"

msgctxt "raft_remove_inside_corners label"
msgid "Remove Raft Inside Corners"
msgstr "래프트 내부 모서리 제거"

msgctxt "raft_interface_remove_inside_corners label"
msgid "Remove Raft Middle Inside Corners"
msgstr "래프트 중간 내부 모서리 제거"

msgctxt "raft_surface_remove_inside_corners label"
msgid "Remove Raft Top Inside Corners"
msgstr "래프트 상단 내부 모서리 제거"

msgctxt "carve_multiple_volumes description"
msgid "Remove areas where multiple meshes are overlapping with each other. This may be used if merged dual material objects overlap with each other."
msgstr "여러 메시가 서로 겹치는 영역을 제거합니다. 병합 된 2개의 재료가 서로 중첩되는 경우 사용될 수 있습니다."

msgctxt "remove_empty_first_layers description"
msgid "Remove empty layers beneath the first printed layer if they are present. Disabling this setting can cause empty first layers if the Slicing Tolerance setting is set to Exclusive or Middle."
msgstr "첫 번째로 프린팅된 레이어 바로 아래의 비어 있는 레이어를 제거합니다. 이 설정을 해제하면 슬라이싱 허용 오차 설정을 배타 또는 중간으로 설정할 경우 첫 번째 레이어가 비어 있게 될 수 있습니다."

msgctxt "raft_base_remove_inside_corners description"
msgid "Remove inside corners from the raft base, causing the raft to become convex."
msgstr "래프트가 볼록해지도록 래프트 베이스에서 내부 모서리를 제거합니다."

msgctxt "raft_interface_remove_inside_corners description"
msgid "Remove inside corners from the raft middle part, causing the raft to become convex."
msgstr "래프트가 볼록해지도록 래프트 중간 부분에서 내부 모서리를 제거합니다."

msgctxt "raft_surface_remove_inside_corners description"
msgid "Remove inside corners from the raft top part, causing the raft to become convex."
msgstr "래프트가 볼록해지도록 래프트 상단 부분에서 내부 모서리를 제거합니다."

msgctxt "raft_remove_inside_corners description"
msgid "Remove inside corners from the raft, causing the raft to become convex."
msgstr "래프트가 볼록해지도록 래프트에서 내부 모서리를 제거합니다."

msgctxt "meshfix_union_all_remove_holes description"
msgid "Remove the holes in each layer and keep only the outside shape. This will ignore any invisible internal geometry. However, it also ignores layer holes which can be viewed from above or below."
msgstr "각 레이어의 구멍을 제거하고 바깥 쪽 모양 만 유지합니다. 이것은 보이지 않는 내부 지오메트리를 무시합니다. 그러나 위 또는 아래에서 볼 수있는 레이어 구멍도 무시합니다."

msgctxt "machine_gcode_flavor option RepRap (RepRap)"
msgid "RepRap"
msgstr "RepRap"

msgctxt "machine_gcode_flavor option Repetier"
msgid "Repetier"
msgstr "Repetier"

msgctxt "skin_outline_count description"
msgid "Replaces the outermost part of the top/bottom pattern with a number of concentric lines. Using one or two lines improves roofs that start on infill material."
msgstr "위쪽/아래쪽 패턴의 가장 바깥 쪽 부분을 여러 동심 선으로 바꿉니다. 하나 또는 두 개의 선을 사용하면 내부채움 재료로 시작하는 지붕면이 향상됩니다."

msgctxt "ppr description"
msgid "Reporting events that go out of set thresholds"
msgstr "설정된 임곗값을 벗어난 이벤트 보고"

msgctxt "support_tree_rest_preference label"
msgid "Rest Preference"
msgstr "배치 기본 설정"

msgctxt "travel_retract_before_outer_wall label"
msgid "Retract Before Outer Wall"
msgstr "외벽 전에 리트렉션"

msgctxt "retract_at_layer_change label"
msgid "Retract at Layer Change"
msgstr "레이어 변경시 리트렉션"

msgctxt "retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "노즐이 프린팅되지 않은 영역 위로 움직일 때 필라멘트를 리트렉션합니다."

msgctxt "wipe_retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "노즐이 프린팅되지 않은 영역 위로 움직일 때 필라멘트를 리트렉션합니다."

msgctxt "retract_at_layer_change description"
msgid "Retract the filament when the nozzle is moving to the next layer."
msgstr "노즐이 다음 층으로 이동할 때 필라멘트를 리트렉션 시킵니다."

msgctxt "retraction_amount label"
msgid "Retraction Distance"
msgstr "리트렉션 거리"

msgctxt "retraction_extra_prime_amount label"
msgid "Retraction Extra Prime Amount"
msgstr "추가적인 리트렉션 정도"

msgctxt "retraction_min_travel label"
msgid "Retraction Minimum Travel"
msgstr "리트렉션 최소 이동"

msgctxt "retraction_prime_speed label"
msgid "Retraction Prime Speed"
msgstr "리트렉션 초기 속도"

msgctxt "retraction_retract_speed label"
msgid "Retraction Retract Speed"
msgstr "리트렉션 속도"

msgctxt "retraction_speed label"
msgid "Retraction Speed"
msgstr "리트렉션 속도"

msgctxt "z_seam_position option right"
msgid "Right"
msgstr "오른쪽"

msgctxt "machine_scale_fan_speed_zero_to_one label"
msgid "Scale Fan Speed To 0-1"
msgstr "팬 속도를 0-1로 조정"

msgctxt "machine_scale_fan_speed_zero_to_one description"
msgid "Scale the fan speed to be between 0 and 1 instead of between 0 and 256."
msgstr "팬 속도를 0 ~ 256이 아니라 0 ~ 1로 조정합니다."

msgctxt "material_shrinkage_percentage label"
msgid "Scaling Factor Shrinkage Compensation"
msgstr "확장 배율 수축 보상"

msgctxt "support_meshes_present label"
msgid "Scene Has Support Meshes"
msgstr "장면에 서포트 메쉬가 있습니다"

msgctxt "z_seam_corner label"
msgid "Seam Corner Preference"
msgstr "솔기 코너 환경 설정"

msgctxt "user_defined_print_order_enabled label"
msgid "Set Print Sequence Manually"
msgstr "수동으로 인쇄 순서 설정"

msgctxt "draft_shield_height_limitation description"
msgid "Set the height of the draft shield. Choose to print the draft shield at the full height of the model or at a limited height."
msgstr "드래프트 쉴드의 높이를 설정합니다. 모델의 전체 높이 또는 제한된 높이에서 드래프트 쉴드를 프린팅하도록 선택합니다."

msgctxt "dual description"
msgid "Settings used for printing with multiple extruders."
msgstr "여러 익스트루더로 프린팅 할 때 사용되는 설정입니다."

msgctxt "command_line_settings description"
msgid "Settings which are only used if CuraEngine isn't called from the Cura frontend."
msgstr "큐라(Cura) 프론트 엔드에서 큐라엔진(CuraEngine)이 호출되지 않은 경우에만 사용되는 설정입니다."

msgctxt "machine_extruders_shared_nozzle_initial_retraction label"
msgid "Shared Nozzle Initial Retraction"
msgstr "공유된 노즐 초기 수축"

msgctxt "z_seam_type option sharpest_corner"
msgid "Sharpest Corner"
msgstr "날카로운 모서리"

msgctxt "shell description"
msgid "Shell"
msgstr "외곽"

msgctxt "z_seam_type option shortest"
msgid "Shortest"
msgstr "최단경로"

msgctxt "machine_show_variants label"
msgid "Show Machine Variants"
msgstr "기기 세부 설정 표시"

msgctxt "skin_edge_support_layers label"
msgid "Skin Edge Support Layers"
msgstr "스킨 에지의 레이어 지원"

msgctxt "skin_edge_support_thickness label"
msgid "Skin Edge Support Thickness"
msgstr "스킨 에지의 두께 지원"

msgctxt "expand_skins_expand_distance label"
msgid "Skin Expand Distance"
msgstr "스킨 확장 거리"

msgctxt "skin_overlap_mm label"
msgid "Skin Overlap"
msgstr "스킨 겹침"

msgctxt "skin_overlap label"
msgid "Skin Overlap Percentage"
msgstr "스킨 겹침 비율"

msgctxt "skin_preshrink label"
msgid "Skin Removal Width"
msgstr "스킨 제거 폭"

msgctxt "min_skin_width_for_expansion description"
msgid "Skin areas narrower than this are not expanded. This avoids expanding the narrow skin areas that are created when the model surface has a slope close to the vertical."
msgstr "이보다 좁은 스킨 영역은 확장되지 않습니다. 이렇게하면 모델 표면이 수직에 가까운 기울기를 가질 때 생성되는 좁은 스킨 영역을 확장하지 않아도됩니다."

msgctxt "support_zag_skip_count description"
msgid "Skip one in every N connection lines to make the support structure easier to break away."
msgstr "서포트 구조를 쉽게 분리할 수 있도록 모든 N 개의 연결 라인을 건너 뜁니다."

msgctxt "support_skip_some_zags description"
msgid "Skip some support line connections to make the support structure easier to break away. This setting is applicable to the Zig Zag support infill pattern."
msgstr "서포트 구조가 쉽게 끊어 지도록 서포트 라인 연결을 건너 뜁니다. 이 설정은 지그재그 서포트 충전 패턴에 적용됩니다."

msgctxt "adhesion_type option skirt"
msgid "Skirt"
msgstr "스커트"

msgctxt "skirt_gap label"
msgid "Skirt Distance"
msgstr "스커트 거리"

msgctxt "skirt_height label"
msgid "Skirt Height"
msgstr "스커트 높이"

msgctxt "skirt_line_count label"
msgid "Skirt Line Count"
msgstr "스커트 선 수"

msgctxt "acceleration_skirt_brim label"
msgid "Skirt/Brim Acceleration"
msgstr "Skirt/Brim 가속도"

msgctxt "skirt_brim_extruder_nr label"
msgid "Skirt/Brim Extruder"
msgstr "스커트/브림 익스트루더"

msgctxt "skirt_brim_material_flow label"
msgid "Skirt/Brim Flow"
msgstr "스커트/브림 압출량"

msgctxt "jerk_skirt_brim label"
msgid "Skirt/Brim Jerk"
msgstr "Skirt/Brim Jerk"

msgctxt "skirt_brim_line_width label"
msgid "Skirt/Brim Line Width"
msgstr "스커트/브림 선 너비"

msgctxt "skirt_brim_minimal_length label"
msgid "Skirt/Brim Minimum Length"
msgstr "스커트/브림 최소 길이"

msgctxt "skirt_brim_speed label"
msgid "Skirt/Brim Speed"
msgstr "스커트/브림 속도"

msgctxt "slicing_tolerance label"
msgid "Slicing Tolerance"
msgstr "슬라이싱 허용 오차"

msgctxt "small_feature_speed_factor_0 label"
msgid "Small Feature Initial Layer Speed"
msgstr "소형 피처 초기 레이어 속도"

msgctxt "small_feature_max_length label"
msgid "Small Feature Max Length"
msgstr "소형 피처 최대 길이"

msgctxt "small_feature_speed_factor label"
msgid "Small Feature Speed"
msgstr "소형 피처 속도"

msgctxt "small_hole_max_size label"
msgid "Small Hole Max Size"
msgstr "소형 구멍 최대 크기"

msgctxt "cool_min_temperature label"
msgid "Small Layer Printing Temperature"
msgstr "소형 레이어 프린팅 온도"

msgctxt "small_skin_on_surface label"
msgid "Small Top/Bottom On Surface"
msgstr "표면의 작은 상단/하단"

msgctxt "small_skin_width label"
msgid "Small Top/Bottom Width"
msgstr "작은 상단/하단 너비"

msgctxt "small_feature_speed_factor_0 description"
msgid "Small features on the first layer will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr "첫 번째 레이어의 소형 피처는 정상적인 프린트 속도의 이 비율로 프린팅됩니다. 프린트 속도가 느리면 부착과 정확도가 개선됩니다."

msgctxt "small_feature_speed_factor description"
msgid "Small features will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr "소형 피처는 정상적인 프린트 속도의 이 비율로 프린팅됩니다. 프린트 속도가 느리면 부착과 정확도가 개선됩니다."

msgctxt "small_skin_width description"
msgid "Small top/bottom regions are filled with walls instead of the default top/bottom pattern. This helps to avoids jerky motions. Off for the topmost (air-exposed) layer by default (see 'Small Top/Bottom On Surface')."
msgstr "작은 상단/하단 영역은 기본 상단/하단 패턴 대신 벽으로 채워집니다. 이렇게 하면 갑작스러운 모션을 방지하는 데 도움이 됩니다. 기본적으로 최상단(공기에 노출된) 레이어는 꺼져 있습니다('표면의 작은 상단/하단' 참조)."

msgctxt "brim_smart_ordering label"
msgid "Smart Brim"
msgstr "스마트 브림"

msgctxt "z_seam_corner option z_seam_corner_weighted"
msgid "Smart Hiding"
msgstr "스마트 숨김"

msgctxt "smooth_spiralized_contours label"
msgid "Smooth Spiralized Contours"
msgstr "부드러운 나선형 윤곽"

msgctxt "smooth_spiralized_contours description"
msgid "Smooth the spiralized contours to reduce the visibility of the Z seam (the Z seam should be barely visible on the print but will still be visible in the layer view). Note that smoothing will tend to blur fine surface details."
msgstr "나선형 윤곽선을 부드럽게 하여 Z 이음선이 잘 보이지 않도록 합니다(Z- 이음선은 출력물에서는 거의 보이지 않지만 레이어 뷰에서는 여전히 보임). 매끄러움은 표면의 세부 묘사를 흐릿하게 만드는 경향이 있습니다."

msgctxt "retraction_extra_prime_amount description"
msgid "Some material can ooze away during a travel move, which can be compensated for here."
msgstr "이동중에 재료가 새어나올 수 있습니다. 이 재료는 여기에서 보상될 수 있습니다."

msgctxt "wipe_retraction_extra_prime_amount description"
msgid "Some material can ooze away during a wipe travel moves, which can be compensated for here."
msgstr "와이프 이동 중에 재료가 새어 나올 수 있습니다. 이 재료는 여기에서 보상받을 수 있습니다."

msgctxt "blackmagic label"
msgid "Special Modes"
msgstr "특수 모드"

msgctxt "speed description"
msgid "Speed"
msgstr "속도"

msgctxt "speed label"
msgid "Speed"
msgstr "속도"

msgctxt "wipe_hop_speed description"
msgid "Speed to move the z-axis during the hop."
msgstr "홉 중에 z축을 이동하는 속도입니다."

msgctxt "magic_spiralize label"
msgid "Spiralize Outer Contour"
msgstr "외부 윤곽선을 나선형으로 만듦"

msgctxt "magic_spiralize description"
msgid "Spiralize smooths out the Z move of the outer edge. This will create a steady Z increase over the whole print. This feature turns a solid model into a single walled print with a solid bottom. This feature should only be enabled when each layer only contains a single part."
msgstr "바깥 쪽 브림의 Z 이동을 부드럽게합니다. 이렇게 하면 출력물 전체에 걸쳐 꾸준히 Z가 증가합니다. 이 기능은 솔리드 모델을 단단한 바닥이있는 단일 벽으로 프린팅합니다. 이 기능은 각 레이어에 단일 부품 만 포함되어 있을 때만 활성화 해야 합니다."

msgctxt "material_standby_temperature label"
msgid "Standby Temperature"
msgstr "대기 온도"

msgctxt "machine_start_gcode label"
msgid "Start G-code"
msgstr "시작 GCode"

msgctxt "z_seam_type description"
msgid "Starting point of each path in a layer. When paths in consecutive layers start at the same point a vertical seam may show on the print. When aligning these near a user specified location, the seam is easiest to remove. When placed randomly the inaccuracies at the paths' start will be less noticeable. When taking the shortest path the print will be quicker."
msgstr "레이어의 각 패스의 시작점입니다. 연속 레이어의 패스가 같은 지점에서 시작되면 세로 솔기가 출력물에 표시 될 수 있습니다. 사용자가 지정한 위치 근처에서 이들을 정렬 할 때 이음선을 제거하는 것이 가장 쉽습니다. 무작위로 배치 될 때 경로의 시작점은 눈에 잘 띄지 않습니다. 최단 경로를 취할 때 프린팅이 빨라집니다."

msgctxt "machine_steps_per_mm_e label"
msgid "Steps per Millimeter (E)"
msgstr "밀리미터 당 스텝 수 (E)"

msgctxt "machine_steps_per_mm_x label"
msgid "Steps per Millimeter (X)"
msgstr "밀리미터 당 스텝 수 (X)"

msgctxt "machine_steps_per_mm_y label"
msgid "Steps per Millimeter (Y)"
msgstr "밀리미터 당 스텝 수 (Y)"

msgctxt "machine_steps_per_mm_z label"
msgid "Steps per Millimeter (Z)"
msgstr "밀리미터 당 스텝 수 (Z)"

msgctxt "support description"
msgid "Support"
msgstr "서포트"

msgctxt "support label"
msgid "Support"
msgstr "서포트"

msgctxt "acceleration_support label"
msgid "Support Acceleration"
msgstr "서포트 가속도"

msgctxt "support_bottom_distance label"
msgid "Support Bottom Distance"
msgstr "서포트 바닥 거리"

msgctxt "support_bottom_wall_count label"
msgid "Support Bottom Wall Line Count"
msgstr "지지대 하단 벽 라인 수"

msgctxt "support_brim_line_count label"
msgid "Support Brim Line Count"
msgstr "서포트 브림 라인 수"

msgctxt "support_brim_width label"
msgid "Support Brim Width"
msgstr "서포트 브림 폭"

msgctxt "support_zag_skip_count label"
msgid "Support Chunk Line Count"
msgstr "Chunk 라인 카운트 서포트"

msgctxt "support_skip_zag_per_mm label"
msgid "Support Chunk Size"
msgstr "서포트 Chunk 크기"

msgctxt "support_infill_rate label"
msgid "Support Density"
msgstr "서포트 밀도"

msgctxt "support_xy_overrides_z label"
msgid "Support Distance Priority"
msgstr "서포트 거리 우선 순위"

msgctxt "support_extruder_nr label"
msgid "Support Extruder"
msgstr "서포트 익스트루더"

msgctxt "acceleration_support_bottom label"
msgid "Support Floor Acceleration"
msgstr "서포트 바닥 가속도"

msgctxt "support_bottom_density label"
msgid "Support Floor Density"
msgstr "서포트 바닥 밀도"

msgctxt "support_bottom_extruder_nr label"
msgid "Support Floor Extruder"
msgstr "서포트 바닥 익스트루더"

msgctxt "support_bottom_material_flow label"
msgid "Support Floor Flow"
msgstr "지지대 바닥 압출량"

msgctxt "support_bottom_offset label"
msgid "Support Floor Horizontal Expansion"
msgstr "서포트 바닥 수평 확장"

msgctxt "jerk_support_bottom label"
msgid "Support Floor Jerk"
msgstr "서포트 바닥 Jerk"

msgctxt "support_bottom_angles label"
msgid "Support Floor Line Directions"
msgstr "바닥 지붕 선 방향"

msgctxt "support_bottom_line_distance label"
msgid "Support Floor Line Distance"
msgstr "서포트 바닥 선 거리"

msgctxt "support_bottom_line_width label"
msgid "Support Floor Line Width"
msgstr "서포트 바닥 라인 폭"

msgctxt "support_bottom_pattern label"
msgid "Support Floor Pattern"
msgstr "서포트 바닥 패턴"

msgctxt "speed_support_bottom label"
msgid "Support Floor Speed"
msgstr "서포트 바닥 속도"

msgctxt "support_bottom_height label"
msgid "Support Floor Thickness"
msgstr "서포트 바닥 두께"

msgctxt "support_material_flow label"
msgid "Support Flow"
msgstr "지지대 압출량"

msgctxt "support_offset label"
msgid "Support Horizontal Expansion"
msgstr "수평 확장 서포트"

msgctxt "acceleration_support_infill label"
msgid "Support Infill Acceleration"
msgstr "서포트 내부채움 가속도"

msgctxt "support_infill_extruder_nr label"
msgid "Support Infill Extruder"
msgstr "서포트 내부채움 익스트루더"

msgctxt "jerk_support_infill label"
msgid "Support Infill Jerk"
msgstr "서포트 내부채움 Jerk"

msgctxt "support_infill_sparse_thickness label"
msgid "Support Infill Layer Thickness"
msgstr "서포트 내부채움 레이어 두께"

msgctxt "support_infill_angles label"
msgid "Support Infill Line Directions"
msgstr "서포트 내부채움 선 방향"

msgctxt "speed_support_infill label"
msgid "Support Infill Speed"
msgstr "서포트 내부채움 속도"

msgctxt "acceleration_support_interface label"
msgid "Support Interface Acceleration"
msgstr "서포트 인터페이스 가속도"

msgctxt "support_interface_density label"
msgid "Support Interface Density"
msgstr "서포트 인터페이스 밀도"

msgctxt "support_interface_extruder_nr label"
msgid "Support Interface Extruder"
msgstr "서포트 인터페이스 익스트루더"

msgctxt "support_interface_material_flow label"
msgid "Support Interface Flow"
msgstr "지지대 인터페이스 압출량"

msgctxt "support_interface_offset label"
msgid "Support Interface Horizontal Expansion"
msgstr "서포트 인터페이스 수평 확장"

msgctxt "jerk_support_interface label"
msgid "Support Interface Jerk"
msgstr "서포트 인터페이스 Jerk"

msgctxt "support_interface_angles label"
msgid "Support Interface Line Directions"
msgstr "서포트 인터페이스 선 방향"

msgctxt "support_interface_line_width label"
msgid "Support Interface Line Width"
msgstr "서포트 인터페이스의 폭"

msgctxt "support_interface_pattern label"
msgid "Support Interface Pattern"
msgstr "서포트 인터페이스 패턴"

msgctxt "support_interface_priority label"
msgid "Support Interface Priority"
msgstr "서포트 인터페이스 우선순위"

msgctxt "speed_support_interface label"
msgid "Support Interface Speed"
msgstr "서포트 인터페이스 속도"

msgctxt "support_interface_height label"
msgid "Support Interface Thickness"
msgstr "서포트 인터페이스 두께"

msgctxt "support_interface_wall_count label"
msgid "Support Interface Wall Line Count"
msgstr "지지대 인터페이스 벽 라인 수"

msgctxt "jerk_support label"
msgid "Support Jerk"
msgstr "서포트 Jerk"

msgctxt "support_join_distance label"
msgid "Support Join Distance"
msgstr "서포트 Join 거리"

msgctxt "support_line_distance label"
msgid "Support Line Distance"
msgstr "서포트 선 거리"

msgctxt "support_line_width label"
msgid "Support Line Width"
msgstr "서포트의 폭"

msgctxt "support_mesh label"
msgid "Support Mesh"
msgstr "서포트 메쉬"

msgctxt "support_angle label"
msgid "Support Overhang Angle"
msgstr "오버행 각도"

msgctxt "support_pattern label"
msgid "Support Pattern"
msgstr "서포트 패턴"

msgctxt "support_type label"
msgid "Support Placement"
msgstr "서포트 배치"

msgctxt "acceleration_support_roof label"
msgid "Support Roof Acceleration"
msgstr "서포트 상단 가속도"

msgctxt "support_roof_density label"
msgid "Support Roof Density"
msgstr "서포트 지붕 밀도"

msgctxt "support_roof_extruder_nr label"
msgid "Support Roof Extruder"
msgstr "서포트 지붕 익스트루더"

msgctxt "support_roof_material_flow label"
msgid "Support Roof Flow"
msgstr "지지대 지붕 압출량"

msgctxt "support_roof_offset label"
msgid "Support Roof Horizontal Expansion"
msgstr "서포트 지붕 수평 확장"

msgctxt "jerk_support_roof label"
msgid "Support Roof Jerk"
msgstr "서포트 위 Jerk"

msgctxt "support_roof_angles label"
msgid "Support Roof Line Directions"
msgstr "서포트 지붕 선 방향"

msgctxt "support_roof_line_distance label"
msgid "Support Roof Line Distance"
msgstr "서포트 지붕 선 거리"

msgctxt "support_roof_line_width label"
msgid "Support Roof Line Width"
msgstr "서포트 루프 라인 폭"

msgctxt "support_roof_pattern label"
msgid "Support Roof Pattern"
msgstr "서포트 지붕 패턴"

msgctxt "speed_support_roof label"
msgid "Support Roof Speed"
msgstr "서포트 상단 속도"

msgctxt "support_roof_height label"
msgid "Support Roof Thickness"
msgstr "서포트 지붕 두께"

msgctxt "support_roof_wall_count label"
msgid "Support Roof Wall Line Count"
msgstr "지지대 지붕 벽 라인 수"

msgctxt "speed_support label"
msgid "Support Speed"
msgstr "서포트 속도"

msgctxt "support_bottom_stair_step_height label"
msgid "Support Stair Step Height"
msgstr "계단 Step Height 서포트"

msgctxt "support_bottom_stair_step_width label"
msgid "Support Stair Step Maximum Width"
msgstr "서포트 계단 스텝 최대 폭"

msgctxt "support_bottom_stair_step_min_slope label"
msgid "Support Stair Step Minimum Slope Angle"
msgstr "서포트 계단 스텝 최소 경사 각도"

msgctxt "support_structure label"
msgid "Support Structure"
msgstr "서포트 구조"

msgctxt "support_top_distance label"
msgid "Support Top Distance"
msgstr "서포트 상단 거리"

msgctxt "support_wall_count label"
msgid "Support Wall Line Count"
msgstr "지지대 벽 라인 카운트"

msgctxt "support_xy_distance label"
msgid "Support X/Y Distance"
msgstr "X/Y 서포트 거리"

msgctxt "support_z_distance label"
msgid "Support Z Distance"
msgstr "서포트 Z 거리"

msgctxt "support_interface_priority option support_lines_overwrite_interface_area"
msgid "Support lines preferred"
msgstr "서포트 라인 우선"

msgctxt "support_interface_priority option support_area_overwrite_interface_area"
msgid "Support preferred"
msgstr "서포트 우선"

msgctxt "support_supported_skin_fan_speed label"
msgid "Supported Skin Fan Speed"
msgstr "지원되는 스킨 팬 속도"

msgctxt "magic_mesh_surface_mode option surface"
msgid "Surface"
msgstr "표면"

msgctxt "material_surface_energy label"
msgid "Surface Energy"
msgstr "서피스 에너지"

msgctxt "magic_mesh_surface_mode label"
msgid "Surface Mode"
msgstr "표면 모드"

msgctxt "material_adhesion_tendency description"
msgid "Surface adhesion tendency."
msgstr "표면에 점착되는 성항입니다."

msgctxt "material_surface_energy description"
msgid "Surface energy."
msgstr "표면의 에너지입니다."

msgctxt "brim_smart_ordering description"
msgid "Swap print order of the innermost and second innermost brim lines. This improves brim removal."
msgstr "가장 안쪽의 브림 라인과 그다음으로 안쪽에 있는 브림 라인의 프린트 순서를 바꿉니다. 이렇게 하면 브림이 잘 제거됩니다."

msgctxt "alternate_carve_order description"
msgid "Switch to which mesh intersecting volumes will belong with every layer, so that the overlapping meshes become interwoven. Turning this setting off will cause one of the meshes to obtain all of the volume in the overlap, while it is removed from the other meshes."
msgstr "교차하는 메쉬로 교차하는 볼륨으로 전환하면 겹치는 메쉬가 서로 얽히게됩니다. 이 설정을 해제하면 메시 중 하나가 다른 메시에서 제거되는 동안 오버랩의 모든 볼륨을 가져옵니다."

msgctxt "adaptive_layer_height_threshold description"
msgid "Target horizontal distance between two adjacent layers. Reducing this setting causes thinner layers to be used to bring the edges of the layers closer together."
msgstr "두 개의 인접 레이어 사이의 대상 수평 거리. 이러한 설정을 줄이면 레이어들의 가장자리를 더 가깝게 하도록 보다 얇은 레이어들을 사용하게 됩니다."

msgctxt "layer_start_x description"
msgid "The X coordinate of the position near where to find the part to start printing each layer."
msgstr "각 레이어의 프린팅를 시작할 부분을 찾을 위치 근처의 X 좌표입니다."

msgctxt "z_seam_x description"
msgid "The X coordinate of the position near where to start printing each part in a layer."
msgstr "레이어에서 프린팅이 시작할 위치 근처의 X 좌표입니다."

msgctxt "extruder_prime_pos_x description"
msgid "The X coordinate of the position where the nozzle primes at the start of printing."
msgstr "프린팅이 시작될 때 노즐의 X 좌표입니다."

msgctxt "layer_start_y description"
msgid "The Y coordinate of the position near where to find the part to start printing each layer."
msgstr "각 레이어 프린팅를 시작할 부분을 찾을 위치 근처의 위치에 대한 Y 좌표입니다."

msgctxt "z_seam_y description"
msgid "The Y coordinate of the position near where to start printing each part in a layer."
msgstr "레이어에서 프린팅이 시작할 위치 근처의 Y 좌표입니다."

msgctxt "extruder_prime_pos_y description"
msgid "The Y coordinate of the position where the nozzle primes at the start of printing."
msgstr "프린팅이 시작될 때 노즐의 Y 좌표입니다."

msgctxt "extruder_prime_pos_z description"
msgid "The Z coordinate of the position where the nozzle primes at the start of printing."
msgstr "프린팅가 시작될 때 노즐 위치의 Z 좌표입니다."

msgctxt "acceleration_print_layer_0 description"
msgid "The acceleration during the printing of the initial layer."
msgstr "초기 레이어 프린팅 중 가속도."

msgctxt "acceleration_layer_0 description"
msgid "The acceleration for the initial layer."
msgstr "초기 레이어의 가속도입니다."

msgctxt "acceleration_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "헤드가 초기 레이어에서 이동할 때의 가속도."

msgctxt "jerk_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "헤드가 초기 레이어에서 이동할 때의 가속도."

msgctxt "acceleration_wall_x description"
msgid "The acceleration with which all inner walls are printed."
msgstr "모든 내부 벽이 프린팅되는 가속도입니다."

msgctxt "acceleration_infill description"
msgid "The acceleration with which infill is printed."
msgstr "내부채움물이 프린팅되는 가속도."

msgctxt "acceleration_ironing description"
msgid "The acceleration with which ironing is performed."
msgstr "다림질이 수행되는 가속도."

msgctxt "acceleration_print description"
msgid "The acceleration with which printing happens."
msgstr "프린팅 속도가 빨라집니다."

msgctxt "raft_base_acceleration description"
msgid "The acceleration with which the base raft layer is printed."
msgstr "기본 래프트 레이어가 프린팅되는 가속도입니다."

msgctxt "acceleration_support_bottom description"
msgid "The acceleration with which the floors of support are printed. Printing them at lower acceleration can improve adhesion of support on top of your model."
msgstr "지면의 가속도가 프린팅됩니다. 보다 낮은 가속도로 프린팅하면 모델 상단에 서포트력을 향상시킬 수 있습니다."

msgctxt "acceleration_support_infill description"
msgid "The acceleration with which the infill of support is printed."
msgstr "서포트의 내부채움이 프린팅되는 가속도."

msgctxt "raft_interface_acceleration description"
msgid "The acceleration with which the middle raft layer is printed."
msgstr "중간 래프트 층이 프린팅되는 가속도."

msgctxt "acceleration_wall_0 description"
msgid "The acceleration with which the outermost walls are printed."
msgstr "가장 바깥 쪽 벽이 프린팅되는 가속도입니다."

msgctxt "acceleration_prime_tower description"
msgid "The acceleration with which the prime tower is printed."
msgstr "프라임 타워가 프린팅되는 가속도."

msgctxt "raft_acceleration description"
msgid "The acceleration with which the raft is printed."
msgstr "래프트가 프린팅되는 가속도."

msgctxt "acceleration_support_interface description"
msgid "The acceleration with which the roofs and floors of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "서포트의 지붕과 바닥이 프린팅되는 가속도. 낮은 가속도로 프린팅하면 오버행 품질이 향상 될 수 있습니다."

msgctxt "acceleration_support_roof description"
msgid "The acceleration with which the roofs of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "서포트의 지붕이 프린팅되는 가속도. 낮은 가속도로 프린팅하면 오버행 품질이 향상 될 수 있습니다."

msgctxt "acceleration_skirt_brim description"
msgid "The acceleration with which the skirt and brim are printed. Normally this is done with the initial layer acceleration, but sometimes you might want to print the skirt or brim at a different acceleration."
msgstr "스커트와 브림이 프린팅되는 가속도. 일반적으로 이것은 초기 레이어 가속으로 이루어 서포트만 때로는 스커트나 브림을 다른 가속으로 프린팅 할 수 있습니다."

msgctxt "acceleration_support description"
msgid "The acceleration with which the support structure is printed."
msgstr "서포트 구조가 프린팅되는 가속도."

msgctxt "raft_surface_acceleration description"
msgid "The acceleration with which the top raft layers are printed."
msgstr "상단 래프트 레이어가 프린팅되는 가속도입니다."

msgctxt "acceleration_wall_x_roofing description"
msgid "The acceleration with which the top surface inner walls are printed."
msgstr "상면 내벽이 인쇄되는 가속도"

msgctxt "acceleration_wall_0_roofing description"
msgid "The acceleration with which the top surface outermost walls are printed."
msgstr "상면의 가장 바깥 벽이 인쇄되는 가속도"

msgctxt "acceleration_wall description"
msgid "The acceleration with which the walls are printed."
msgstr "벽이 프린팅되는 가속도."

msgctxt "acceleration_roofing description"
msgid "The acceleration with which top surface skin layers are printed."
msgstr "상단 표면 스킨 층이 프린팅되는 가속도."

msgctxt "acceleration_topbottom description"
msgid "The acceleration with which top/bottom layers are printed."
msgstr "위쪽/아래쪽 레이어가 프린팅되는 가속도입니다."

msgctxt "acceleration_travel description"
msgid "The acceleration with which travel moves are made."
msgstr "헤드가 움직일때의 가속도."

msgctxt "ironing_flow description"
msgid "The amount of material, relative to a normal skin line, to extrude during ironing. Keeping the nozzle filled helps filling some of the crevices of the top surface, but too much results in overextrusion and blips on the side of the surface."
msgstr "다림질하는 동안 기본 스킨 라인을 기준으로 한 재료의 압출량. 노즐을 가득 채우면 윗면의 틈새를 채울 수 있지만 표면에 과도한 압출과 필라멘트 덩어리가 생길 수 있습니다."

msgctxt "infill_overlap description"
msgid "The amount of overlap between the infill and the walls as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "내부채움 라인 폭의 비율로 나타낸 내부채움재와 벽 사이의 오버랩 양. 약간의 오버랩으로 벽이 내부채움과 확실하게 연결됩니다."

msgctxt "infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls. A slight overlap allows the walls to connect firmly to the infill."
msgstr "내부채움과 벽 사이의 겹침 정도. 약간 겹치면 벽이 내부채움에 단단히 연결됩니다."

msgctxt "switch_extruder_retraction_amount description"
msgid "The amount of retraction when switching extruders. Set to 0 for no retraction at all. This should generally be the same as the length of the heat zone."
msgstr "익스트루더 전환 시 리트렉션 양. 리트렉션이 전혀 없는 경우 0으로 설정하십시오. 이는 일반적으로 열 영역의 길이와 같아야 합니다."

msgctxt "machine_nozzle_expansion_angle description"
msgid "The angle between the horizontal plane and the conical part right above the tip of the nozzle."
msgstr "노즐 끝 바로 위의 수평면과 원뿔 부분 사이의 각도입니다."

msgctxt "support_tower_roof_angle description"
msgid "The angle of a rooftop of a tower. A higher value results in pointed tower roofs, a lower value results in flattened tower roofs."
msgstr "타워 옥상의 각도. 높은 값을 지정하면 뾰족한 타워 지붕이되고, 값이 낮을수록 평평한 타워 지붕이됩니다."

msgctxt "mold_angle description"
msgid "The angle of overhang of the outer walls created for the mold. 0° will make the outer shell of the mold vertical, while 90° will make the outside of the model follow the contour of the model."
msgstr "몰드에 대해 생성 된 외벽의 오버행 각도입니다. 0도의 각은 금형의 외각을 수직으로 만들고 90도의 각은 모형의 외형을 모델의 외형으로 만듭니다."

msgctxt "support_tree_branch_diameter_angle description"
msgid "The angle of the branches' diameter as they gradually become thicker towards the bottom. An angle of 0 will cause the branches to have uniform thickness over their length. A bit of an angle can increase stability of the tree support."
msgstr "바닥면을 향할수록 점점 더 두꺼워짐에 따른 브랜치의 직경 각도. 이 각도가 0이면 브랜치는 길이 전체에 균일한 두께를 갖게 됩니다. 약간의 각도가 있으면 트리 서포트의 안정성을 높여 줍니다."

msgctxt "support_conical_angle description"
msgid "The angle of the tilt of conical support. With 0 degrees being vertical, and 90 degrees being horizontal. Smaller angles cause the support to be more sturdy, but consist of more material. Negative angles cause the base of the support to be wider than the top."
msgstr "원추형 서포트점의 기울기 각도입니다. 0도가 수직이고 90도가 수평입니다. 각도가 작 으면 서포트가 더 튼튼하지만 더 많은 재료로 구성됩니다. 음수 각도는 서포트의 받침대가 상단보다 넓게 만듭니다."

msgctxt "magic_fuzzy_skin_point_density description"
msgid "The average density of points introduced on each polygon in a layer. Note that the original points of the polygon are discarded, so a low density results in a reduction of the resolution."
msgstr "레이어의 각 다각형에 있는 점의 평균 밀도입니다. 다각형의 원래 점은 버려지므로 밀도가 낮으면 해상도가 감소합니다."

msgctxt "magic_fuzzy_skin_point_dist description"
msgid "The average distance between the random points introduced on each line segment. Note that the original points of the polygon are discarded, so a high smoothness results in a reduction of the resolution. This value must be higher than half the Fuzzy Skin Thickness."
msgstr "각 선분에 있는 임의의 점 사이의 평균 거리입니다. 다각형의 원래 점은 버려지므로 해상도가 감소합니다. 이 값은 퍼지 스킨 두께의 절반보다 커야합니다."

msgctxt "material_brand description"
msgid "The brand of material used."
msgstr "사용된 재료의 브랜드입니다."

msgctxt "machine_acceleration description"
msgid "The default acceleration of print head movement."
msgstr "프린트 헤드 이동시 기본 가속도."

msgctxt "default_material_print_temperature description"
msgid "The default temperature used for printing. This should be the \"base\" temperature of a material. All other print temperatures should use offsets based on this value"
msgstr "프린팅에 사용되는 기본 온도입니다. 이것은 재료의 \"기본\"온도 이여야 합니다. 다른 모든 프린팅 온도는 이 값을 기준으로 오프셋을 사용해야합니다"

msgctxt "default_material_bed_temperature description"
msgid "The default temperature used for the heated build plate. This should be the \"base\" temperature of a build plate. All other print temperatures should use offsets based on this value"
msgstr "히팅 빌드 플레이트에 사용되는 기본 온도입니다. 이것은 재료의 \"기본\"온도입니다. 다른 모든 프린팅 온도는 이 값을 기준으로 오프셋을 사용해야합니다"

msgctxt "bridge_skin_density description"
msgid "The density of the bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "브릿지 스킨 층의 밀도입니다. 값이 100보다 작으면 스킨 라인 사이의 간격이 증가합니다."

msgctxt "support_bottom_density description"
msgid "The density of the floors of the support structure. A higher value results in better adhesion of the support on top of the model."
msgstr "서포트 구조체의 바닥 밀도. 값이 높을수록 서포트가 모델 위에 더 잘 접착됩니다."

msgctxt "support_roof_density description"
msgid "The density of the roofs of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "서포트의 지붕의 밀도. 값이 높을수록 오버행에서 좋지만 서포트를 제거하기가 더 어렵습니다."

msgctxt "bridge_skin_density_2 description"
msgid "The density of the second bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "두번째 브릿지 스킨 층의 밀도입니다. 값이 100보다 작으면 스킨 라인 사이의 간격이 증가합니다."

msgctxt "bridge_skin_density_3 description"
msgid "The density of the third bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "세번째 브릿지 스킨 층의 밀도입니다. 값이 100보다 작으면 스킨 라인 사이의 간격이 증가합니다."

msgctxt "machine_depth description"
msgid "The depth (Y-direction) of the printable area."
msgstr "프린팅 가능 영역의 깊이 (Y 방향)"

msgctxt "support_tower_diameter description"
msgid "The diameter of a special tower."
msgstr "특수 타워의 지름."

msgctxt "support_tree_branch_diameter description"
msgid "The diameter of the thinnest branches of tree support. Thicker branches are more sturdy. Branches towards the base will be thicker than this."
msgstr "트리 서포트의 가장 얇은 브랜치의 직경. 브랜치가 두꺼울수록 더 견고해집니다. 바닥을 향한 브랜치는 이보다 더 두꺼워집니다."

msgctxt "support_tree_tip_diameter description"
msgid "The diameter of the top of the tip of the branches of tree support."
msgstr "트리 서포트 브랜치 팁의 상단 직경입니다."

msgctxt "machine_feeder_wheel_diameter description"
msgid "The diameter of the wheel that drives the material in the feeder."
msgstr "피더에서 재료를 구동시키는 휠의 지름."

msgctxt "support_tree_max_diameter description"
msgid "The diameter of the widest branches of tree support. A thicker trunk is more sturdy; a thinner trunk takes up less space on the build plate."
msgstr "트리 서포트의 가장 굵은 브랜치의 직경. 트렁크가 두꺼울수록 더 견고해집니다. 얇은 트렁크는 빌드 플레이트에서 차지하는 공간이 보다 적습니다."

msgctxt "adaptive_layer_height_variation_step description"
msgid "The difference in height of the next layer height compared to the previous one."
msgstr "이전 높이와 비교되는 다음 레이어 높이의 차이."

msgctxt "ironing_line_spacing description"
msgid "The distance between the lines of ironing."
msgstr "다림질 라인 사이의 거리."

msgctxt "travel_avoid_distance description"
msgid "The distance between the nozzle and already printed parts when avoiding during travel moves."
msgstr "이동 중 출력물을 피할 때 노즐과 이미 프린팅 된 부분 사이의 거리."

msgctxt "raft_base_line_spacing description"
msgid "The distance between the raft lines for the base raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr "기본 래프트 층에 대한 래프트 사이의 거리. 넓은 간격으로 빌드 플레이트에서 래프트를 쉽게 제거 할 수 있습니다."

msgctxt "raft_interface_line_spacing description"
msgid "The distance between the raft lines for the middle raft layer. The spacing of the middle should be quite wide, while being dense enough to support the top raft layers."
msgstr "중간 래프트 층에 대한 래프트 사이의 거리. 중간 틈새는 매우 넓어야하며 래프트 상부 층을서포트 할만큼 충분히 촘촘해야합니다."

msgctxt "raft_surface_line_spacing description"
msgid "The distance between the raft lines for the top raft layers. The spacing should be equal to the line width, so that the surface is solid."
msgstr "상단 래프트 레이어에 대한 래프트 사이의 거리. 간격은 선 너비와 동일해야 표면이 단색입니다."

msgctxt "prime_tower_raft_base_line_spacing description"
msgid "The distance between the raft lines for the unique prime tower raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr "독특한 프라임 타워 래프트 레이어를 위한 래프트 라인 간 거리입니다. 간격이 넓을수록 빌드 플레이트에서 래프트를 쉽게 제거할 수 있습니다."

msgctxt "interlocking_depth description"
msgid "The distance from the boundary between models to generate interlocking structure, measured in cells. Too few cells will result in poor adhesion."
msgstr "연동 구조를 생성하기 위한 모델 간 경계로부터의 거리로, 셀 단위로 측정됩니다. 셀 수가 너무 적으면 접착력이 떨어집니다."

msgctxt "brim_width description"
msgid "The distance from the model to the outermost brim line. A larger brim enhances adhesion to the build plate, but also reduces the effective print area."
msgstr "모델에서 가장 바깥 쪽 브림까지의 거리. 큰 테두리는 빌드 플레이트에 대한 접착력을 향상 시키지만 효과적인 프린팅 영역도 감소시킵니다."

msgctxt "interlocking_boundary_avoidance description"
msgid "The distance from the outside of a model where interlocking structures will not be generated, measured in cells."
msgstr "연동 구조가 생성되지 않는 모델 외부로부터의 거리로, 셀 단위로 측정됩니다."

msgctxt "machine_heat_zone_length description"
msgid "The distance from the tip of the nozzle in which heat from the nozzle is transferred to the filament."
msgstr "노즐의 열이 필라멘트로 전달되는 노즐의 끝에서부터의 거리."

msgctxt "bottom_skin_expand_distance description"
msgid "The distance the bottom skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the skin adhere better to the walls on the layer below. Lower values save amount of material used."
msgstr "바닥 스킨의 거리가 내부채움으로 확장됩니다. 값이 높을수록 스킨가 내부채움 패턴에 더 잘 붙어 스킨가 아래 층의 벽에 잘 밀착됩니다. 값이 낮을수록 사용 된 재료의 양이 절약됩니다."

msgctxt "expand_skins_expand_distance description"
msgid "The distance the skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on neighboring layers adhere better to the skin. Lower values save amount of material used."
msgstr "스킨이 내부채움으로 확장되는 거리입니다. 값이 높을수록 스킨이 내부채움 패턴에 더 잘 부착되고 인접 레이어의 벽이 스킨에 잘 밀착됩니다. 값이 낮을수록 사용 될 재료의 양이 절약됩니다."

msgctxt "top_skin_expand_distance description"
msgid "The distance the top skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on the layer above adhere better to the skin. Lower values save amount of material used."
msgstr "상단 스킨의 거리가 내부채움으로 확장됩니다. 값이 높을수록 스킨이 내부채움 패턴에 더 잘 부착되며 위 레이어의 벽이 스킨에 잘 밀착됩니다. 값이 낮을수록 사용 된 재료의 양이 절약됩니다."

msgctxt "wipe_move_distance description"
msgid "The distance to move the head back and forth across the brush."
msgstr "브러시 전체에 헤드를 앞뒤로 이동하는 거리입니다."

msgctxt "lightning_infill_prune_angle description"
msgid "The endpoints of infill lines are shortened to save on material. This setting is the angle of overhang of the endpoints of these lines."
msgstr "내부채움 선의 종점이 재료를 절약하기 위해 단축됩니다. 이 설정은 해당 선의 종점에 대한 오버행(경사면)의 각도입니다."

msgctxt "material_extrusion_cool_down_speed description"
msgid "The extra speed by which the nozzle cools while extruding. The same value is used to signify the heat up speed lost when heating up while extruding."
msgstr "압출하는 동안 노즐이 냉각되는 추가적인 속도. 압출하는 동안 가열 될 때 상실되는 열 상승 속도를 나타 내기 위해 동일한 값이 사용됩니다."

msgctxt "support_extruder_nr_layer_0 description"
msgid "The extruder train to use for printing the first layer of support infill. This is used in multi-extrusion."
msgstr "첫번째 층의 서포트 채움에 사용되는 익스트루더. 이것은 다중 압출에 사용됩니다."

msgctxt "raft_base_extruder_nr description"
msgid "The extruder train to use for printing the first layer of the raft. This is used in multi-extrusion."
msgstr "스커트 또는 브림 프린팅에 사용되는 익스트루더 트레인. 이것은 다중 압출에 사용됩니다."

msgctxt "support_bottom_extruder_nr description"
msgid "The extruder train to use for printing the floors of the support. This is used in multi-extrusion."
msgstr "서포트의 바닥을 프린팅하는 데 사용할 익스트루더. 이것은 다중 압출에 사용됩니다."

msgctxt "support_infill_extruder_nr description"
msgid "The extruder train to use for printing the infill of the support. This is used in multi-extrusion."
msgstr "서포트의 내부채움을 프린팅하는 데 사용할 익스트루더. 이것은 다중 압출에 사용됩니다."

msgctxt "raft_interface_extruder_nr description"
msgid "The extruder train to use for printing the middle layer of the raft. This is used in multi-extrusion."
msgstr "래프트의 중간 레이어 프린팅에 사용되는 익스트루더 트레인. 이것은 다중 압출에 사용됩니다."

msgctxt "support_interface_extruder_nr description"
msgid "The extruder train to use for printing the roofs and floors of the support. This is used in multi-extrusion."
msgstr "서포트의 지붕과 바닥을 프린팅 할 때 사용하는 익스트루더. 이것은 다중 압출에 사용됩니다."

msgctxt "support_roof_extruder_nr description"
msgid "The extruder train to use for printing the roofs of the support. This is used in multi-extrusion."
msgstr "서포트의 지붕을 프린팅 할 때 사용하는 익스트루더. 이것은 다중 압출에 사용됩니다."

msgctxt "skirt_brim_extruder_nr description"
msgid "The extruder train to use for printing the skirt or brim. This is used in multi-extrusion."
msgstr "스커트 또는 브림 프린팅에 사용되는 익스트루더 트레인. 이것은 다중 압출에 사용됩니다."

msgctxt "adhesion_extruder_nr description"
msgid "The extruder train to use for printing the skirt/brim/raft. This is used in multi-extrusion."
msgstr "스커트 / 브림 / 래프트 프린팅에 사용하는 익스트루더. 이것은 다중 압출에 사용됩니다."

msgctxt "support_extruder_nr description"
msgid "The extruder train to use for printing the support. This is used in multi-extrusion."
msgstr "서포트 프린팅에 사용할 익스트루더. 이것은 다중 압출에 사용됩니다."

msgctxt "raft_surface_extruder_nr description"
msgid "The extruder train to use for printing the top layer(s) of the raft. This is used in multi-extrusion."
msgstr "래프트의 상단 레이어 프린팅에 사용되는 익스트루더 트레인. 이것은 다중 압출에 사용됩니다."

msgctxt "infill_extruder_nr description"
msgid "The extruder train used for printing infill. This is used in multi-extrusion."
msgstr "내부채움용 프린팅에 사용되는 익스트루더. 이것은 다중 압출에 사용됩니다."

msgctxt "wall_x_extruder_nr description"
msgid "The extruder train used for printing the inner walls. This is used in multi-extrusion."
msgstr "내벽 프린팅에 사용되는 익스트루더. 이것은 다중 압출에 사용됩니다."

msgctxt "wall_0_extruder_nr description"
msgid "The extruder train used for printing the outer wall. This is used in multi-extrusion."
msgstr "외벽 프린팅에 사용되는 익스트루더. 이것은 다중 압출에 사용됩니다."

msgctxt "top_bottom_extruder_nr description"
msgid "The extruder train used for printing the top and bottom skin. This is used in multi-extrusion."
msgstr "상단 및 하단 스킨 프린팅에 사용되는 익스트루더. 이것은 다중 압출에 사용됩니다."

msgctxt "roofing_extruder_nr description"
msgid "The extruder train used for printing the top most skin. This is used in multi-extrusion."
msgstr "익스트루더는 최상층을 프린팅하는 데 사용됩니다. 이것은 다중 압출에 사용됩니다."

msgctxt "wall_extruder_nr description"
msgid "The extruder train used for printing the walls. This is used in multi-extrusion."
msgstr "벽을 프린팅하는 데 사용되는 익스트루더. 이것은 다중 압출에 사용됩니다."

msgctxt "raft_base_fan_speed description"
msgid "The fan speed for the base raft layer."
msgstr "기본 래프트 레이어의 팬 속도입니다."

msgctxt "raft_interface_fan_speed description"
msgid "The fan speed for the middle raft layer."
msgstr "중간 래프트 레이어의 팬 속도입니다."

msgctxt "raft_fan_speed description"
msgid "The fan speed for the raft."
msgstr "래프트의 팬 속도."

msgctxt "raft_surface_fan_speed description"
msgid "The fan speed for the top raft layers."
msgstr "상단 래프트 레이어의 팬 속도입니다."

msgctxt "cross_infill_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the infill of the print."
msgstr "인쇄 충진물의 해당 위치에서 밝기 값으로 최소 밀도를 결정하는 이미지의 파일 위치."

msgctxt "cross_support_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the support."
msgstr "지지대의 해당 위치에서 밝기 값으로 최소 밀도를 결정하는 이미지의 파일 위치."

msgctxt "speed_slowdown_layers description"
msgid "The first few layers are printed slower than the rest of the model, to get better adhesion to the build plate and improve the overall success rate of prints. The speed is gradually increased over these layers."
msgstr "처음 몇 개의 레이어는 모델의 나머지 부분보다 느리게 프린팅되어 빌드 플레이트에 대한보다 나은 접착력을 얻고 출력물의 전체 성공률을 향상시킵니다. 속도는 이 층 위로 점진적으로 증가합니다."

msgctxt "raft_airgap description"
msgid "The gap between the final raft layer and the first layer of the model. Only the first layer is raised by this amount to lower the bonding between the raft layer and the model. Makes it easier to peel off the raft."
msgstr "최종 래프트 층과 모델의 첫 번째 층 사이의 틈새. 래프트 층과 모델 사이의 결합을 낮추기 위해 이 양만큼 첫 번째 층만 올립니다. 래프트를 쉽게 떼어 낼 수 있습니다."

msgctxt "machine_height description"
msgid "The height (Z-direction) of the printable area."
msgstr "프린팅 가능 영역의 높이 (Z 방향)입니다."

msgctxt "mold_roof_height description"
msgid "The height above horizontal parts in your model which to print mold."
msgstr "모델의 수평 부분 위의 높이로 몰드를 프린팅합니다."

msgctxt "cool_fan_full_at_height description"
msgid "The height at which the fans spin on regular fan speed. At the layers below the fan speed gradually increases from Initial Fan Speed to Regular Fan Speed."
msgstr "표준 팬 속도로 팬이 회전하는 높이입니다. 이 높이의 아래 레이어에서 팬 속도는 초기 팬 속도에서 표준 팬 속도로 점차 증가합니다."

msgctxt "gantry_height description"
msgid "The height difference between the tip of the nozzle and the gantry system (X and Y axes)."
msgstr "노즐 끝과 갠트리 시스템 사이의 높이 차이 (X 및 Y 축)."

msgctxt "machine_nozzle_head_distance description"
msgid "The height difference between the tip of the nozzle and the lowest part of the print head."
msgstr "노즐의 끝과 프린트 헤드의 가장 낮은 부분 사이의 높이 차이입니다."

msgctxt "retraction_hop_after_extruder_switch_height description"
msgid "The height difference when performing a Z Hop after extruder switch."
msgstr "익스트루더 스위치 후 Z 홉을 수행할 때의 높이 차이."

msgctxt "retraction_hop description"
msgid "The height difference when performing a Z Hop."
msgstr "Z 홉을 수행 할 때의 높이 차이."

msgctxt "wipe_hop_amount description"
msgid "The height difference when performing a Z Hop."
msgstr "Z 홉을 수행할 때의 높이 차이."

msgctxt "layer_height description"
msgid "The height of each layer in mm. Higher values produce faster prints in lower resolution, lower values produce slower prints in higher resolution."
msgstr "각 층의 높이 (mm)입니다. 값이 클수록 해상도가 낮고 프린팅 속도가 빨라지며, 값이 작을수록 해상도가 높고 프린팅 속도가 느려집니다."

msgctxt "gradual_infill_step_height description"
msgid "The height of infill of a given density before switching to half the density."
msgstr "밀도의 절반으로 전환하기 전에 주어진 밀도에서 내부채움의 높이."

msgctxt "gradual_support_infill_step_height description"
msgid "The height of support infill of a given density before switching to half the density."
msgstr "밀도의 절반으로 전환하기 전에 주어진 밀도의 서포트 채움 높이."

msgctxt "interlocking_beam_layer_count description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr "연동 구조의 빔 높이로, 레이어 수로 측정됩니다. 레이어가 적을수록 강하지만 결함이 발생하기 쉽습니다."

msgctxt "interlocking_orientation description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr "연동 구조의 빔 높이로, 레이어 수로 측정됩니다. 레이어가 적을수록 강하지만 결함이 발생하기 쉽습니다."

msgctxt "layer_height_0 description"
msgid "The height of the initial layer in mm. A thicker initial layer makes adhesion to the build plate easier."
msgstr "첫번째 레이어의 높이 (mm)입니다. 첫번째 레이어를 두껍게하면 빌드 플레이트에 쉽게 부착됩니다."

msgctxt "prime_tower_base_height description"
msgid "The height of the prime tower base. Increasing this value will result in a more sturdy prime tower because the base will be wider. If this setting is too low, the prime tower will not have a sturdy base."
msgstr "프라임 타워 베이스의 높이입니다. 이 값을 늘리면 베이스가 넓어져 프라임 타워가 더 튼튼해집니다. 이 설정이 너무 낮으면 프라임 타워에 튼튼한 베이스가 형성되지 않습니다."

msgctxt "support_bottom_stair_step_height description"
msgid "The height of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures. Set to zero to turn off the stair-like behaviour."
msgstr "모델에있는 서포트의 계단 모양 바닥의 계단 높이. 값이 낮으면 서포트를 제거하기가 어려워 서포트만 값이 너무 높으면 불안정한 서포트 구조가 생길 수 있습니다. 계단 모양의 동작을 해제하려면 0으로 설정하십시오."

msgctxt "brim_gap description"
msgid "The horizontal distance between the first brim line and the outline of the first layer of the print. A small gap can make the brim easier to remove while still providing the thermal benefits."
msgstr "첫 번째 브림 선과 첫 번째 레이어 프린팅의 윤곽 사이의 수평 거리입니다. 작은 간격은 브림을 제거하기 쉽도록 하면서 내열성의 이점을 제공할 수 있습니다."

msgctxt "skirt_gap description"
msgid "The horizontal distance between the skirt and the first layer of the print.\nThis is the minimum distance. Multiple skirt lines will extend outwards from this distance."
msgstr "프린트의 스커트와 첫 번째 레이어 사이의 수평 거리입니다."
"이것은 최소 거리입니다. 여러 개의 스커트 선이 이 거리에서 바깥쪽으로 연장됩니다."

msgctxt "lightning_infill_straightening_angle description"
msgid "The infill lines are straightened out to save on printing time. This is the maximum angle of overhang allowed across the length of the infill line."
msgstr "내부채움 선이 인쇄 시간을 절약하기 위해 정리됩니다. 이는 내부채움 선 길이 전체에 허용되는 오버행(경사면)의 최대 각도입니다."

msgctxt "infill_offset_x description"
msgid "The infill pattern is moved this distance along the X axis."
msgstr "내부채움 패턴이 X축을 따라 이 거리만큼 이동합니다."

msgctxt "infill_offset_y description"
msgid "The infill pattern is moved this distance along the Y axis."
msgstr "내부채움 패턴이 Y축을 따라 이 거리만큼 이동합니다."

msgctxt "machine_nozzle_size description"
msgid "The inner diameter of the nozzle. Change this setting when using a non-standard nozzle size."
msgstr "노즐의 내경. 비표준 노즐 크기를 사용할 때 이 설정을 변경하십시오."

msgctxt "raft_base_jerk description"
msgid "The jerk with which the base raft layer is printed."
msgstr "기본 래프트 레이어가 프린팅 될 때의 Jerk입니다."

msgctxt "raft_interface_jerk description"
msgid "The jerk with which the middle raft layer is printed."
msgstr "중간 래프트 층이 프린팅 될 때의 Jerk입니다."

msgctxt "raft_jerk description"
msgid "The jerk with which the raft is printed."
msgstr "래프트가 프린팅 될 때의 Jerk입니다."

msgctxt "raft_surface_jerk description"
msgid "The jerk with which the top raft layers are printed."
msgstr "상단 래프트 레이어가 프린팅 될 때의 Jerk입니다."

msgctxt "bottom_skin_preshrink description"
msgid "The largest width of bottom skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing bottom skin at slanted surfaces in the model."
msgstr "제거 할 바닥 스킨 영역의 최대 너비. 이 값보다 작은 모든 스킨 영역은 사라집니다. 이렇게하면 모델의 경사면에서 밑면 스킨을 프린팅하는 데 소요되는 시간과 재료의 양을 제한하는 데 도움이됩니다."

msgctxt "skin_preshrink description"
msgid "The largest width of skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top/bottom skin at slanted surfaces in the model."
msgstr "제거 할 외부스킨 영역의 가장 큰 너비. 이 값보다 작은 모든 스킨 영역은 사라집니다. 이렇게하면 모델의 경사면에서 위쪽 / 아래쪽 스킨을 프린팅하는 데 소요되는 시간과 재료의 양을 제한하는 데 도움이 됩니다."

msgctxt "top_skin_preshrink description"
msgid "The largest width of top skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top skin at slanted surfaces in the model."
msgstr "제거 할 상단 스킨 영역의 가장 큰 너비. 이 값보다 작은 모든 스킨 영역은 사라집니다. 이렇게 하면 모델의 경사면에서 상단 스킨을 프린팅하는 데 소요되는 시간과 재료의 양을 제한하는 데 도움이됩니다."

msgctxt "cool_fan_full_layer description"
msgid "The layer at which the fans spin on regular fan speed. If regular fan speed at height is set, this value is calculated and rounded to a whole number."
msgstr "팬이 표준 팬 속도로 회전하는 레이어입니다. 표준 팬 속도시의 높이가 설정이 되어있으면, 이 값이 계산되고 정수로 반올림됩니다."

msgctxt "cool_min_layer_time_fan_speed_max description"
msgid "The layer time which sets the threshold between regular fan speed and maximum fan speed. Layers that print slower than this time use regular fan speed. For faster layers the fan speed gradually increases towards the maximum fan speed."
msgstr "표준 팬 속도와 최대 팬 속도 사이의 임계 값을 설정하는 레이어 시간입니다. 이 시간보다 느리게 프린팅되는 레이어는 표준 팬 속도를 사용합니다. 빠른 레이어의 경우 팬 속도가 최대 팬 속도쪽으로 점차 증가합니다."

msgctxt "retraction_amount description"
msgid "The length of material retracted during a retraction move."
msgstr "리트렉션 이동 중에 수축 된 재료의 길이입니다."

msgctxt "prime_tower_base_curve_magnitude description"
msgid "The magnitude factor used for the slope of the prime tower base. If you increase this value, the base will become slimmer. If you decrease it, the base will become thicker."
msgstr "프라임 타워 베이스의 경사에 사용되는 크기 계수입니다. 이 값을 늘리면 베이스가 더 얇아집니다. 줄이면 베이스가 더 두꺼워집니다."

msgctxt "machine_buildplate_type description"
msgid "The material of the build plate installed on the printer."
msgstr "프린터에 설치된 빌드 플레이트의 재질."

msgctxt "adaptive_layer_height_variation description"
msgid "The maximum allowed height different from the base layer height."
msgstr "기본 레이어 높이와 다른 최대 허용 높이."

msgctxt "ooze_shield_angle description"
msgid "The maximum angle a part in the ooze shield will have. With 0 degrees being vertical, and 90 degrees being horizontal. A smaller angle leads to less failed ooze shields, but more material."
msgstr "Ooze 쉴드가 가질 최대 각도. 0도가 수직이고 90도가 수평입니다. 각도가 작으면 Ooze 쉴드가 덜 파손되지만 재료는 더 많이 소모됩니다."

msgctxt "conical_overhang_angle description"
msgid "The maximum angle of overhangs after the they have been made printable. At a value of 0° all overhangs are replaced by a piece of model connected to the build plate, 90° will not change the model in any way."
msgstr "프린팅 가능하게 된 후 오버행의 최대 각도. 0도의 값에서 모든 오버행은 빌드 플레이트에 연결된 모델로 대체됩니다. 90도는 모델을 변경하지 않습니다."

msgctxt "support_tree_angle description"
msgid "The maximum angle of the branches while they grow around the model. Use a lower angle to make them more vertical and more stable. Use a higher angle to be able to have more reach."
msgstr "모델 주위로 브랜치가 자랄 때 브랜치의 최대 각도입니다. 브랜치가 수직에 가깝게 안정적이 되도록 만들려면 더 낮은 각도를 사용하고, 브랜치의 도달 거리를 늘리려면 더 높은 각도를 사용하십시오."

msgctxt "conical_overhang_hole_size description"
msgid "The maximum area of a hole in the base of the model before it's removed by Make Overhang Printable.  Holes smaller than this will be retained.  A value of 0 mm² will fill all holes in the models base."
msgstr "오버행 프린팅 설정에 의해 제거되기 전 모델의 베이스에 있는 구멍의 최대 영역입니다.  이보다 작은 홀은 유지됩니다.  0mm² 값은 모델 베이스의 모든 홀을 채웁니다."

msgctxt "meshfix_maximum_deviation description"
msgid "The maximum deviation allowed when reducing the resolution for the Maximum Resolution setting. If you increase this, the print will be less accurate, but the g-code will be smaller. Maximum Deviation is a limit for Maximum Resolution, so if the two conflict the Maximum Deviation will always be held true."
msgstr "최대 해상도 설정에 대한 해상도를 낮추면 최대 편차를 사용할 수 있습니다.  최대 편차를 높이면 프린트의 정확도는 감소하지만, G 코드도 감소합니다. 최대 편차는 최대 해상도의 한계이며, 따라서 두 항목이 충돌하면 항상 최대 편차가 우선합니다."

msgctxt "support_join_distance description"
msgid "The maximum distance between support structures in the X/Y directions. When separate structures are closer together than this value, the structures merge into one."
msgstr "X/Y 방향으로 지지대 구조물 사이의 최대 거리입니다. 별도의 구조가 이 값보다 가깝게 있으면 구조가 하나로 합쳐집니다."

msgctxt "flow_rate_max_extrusion_offset description"
msgid "The maximum distance in mm to move the filament to compensate for changes in flow rate."
msgstr "압출 속도를 보상하기 위해 필라멘트를 이동하는 최대 거리(mm)."

msgctxt "meshfix_maximum_extrusion_area_deviation description"
msgid "The maximum extrusion area deviation allowed when removing intermediate points from a straight line. An intermediate point may serve as width-changing point in a long straight line. Therefore, if it is removed, it will cause the line to have a uniform width and, as a result, lose (or gain) a bit of extrusion area. If you increase this you may notice slight under- (or over-) extrusion in between straight parallel walls, as more intermediate width-changing points will be allowed to be removed. Your print will be less accurate, but the g-code will be smaller."
msgstr "직선에서 중간 점을 제거할 때 허용되는 최대 압출 영역 편차. 중간 점은 긴 직선에서 너비가 바뀌는 점의 역할을 할 수 있습니다. 따라서 중간 점이 제거되면 선의 너비가 균일해지고 그 결과, 약간의 압출 영역을 잃거나 얻게 됩니다. 이 값을 증가시키면 중간의 너비가 바뀌는 점이 더 많이 제거될 수 있으므로, 직선의 평행한 벽들 사이에 약간의 미달(또는 과잉) 압출이 발생할 수 있습니다. 프린트의 정확도는 감소하지만, G 코드도 감소합니다."

msgctxt "jerk_print_layer_0 description"
msgid "The maximum instantaneous velocity change during the printing of the initial layer."
msgstr "초기 층의 프린팅 중 최대 순간 속도 변화."

msgctxt "jerk_print description"
msgid "The maximum instantaneous velocity change of the print head."
msgstr "프린트 헤드의 최대 순간 속도 변화."

msgctxt "jerk_ironing description"
msgid "The maximum instantaneous velocity change while performing ironing."
msgstr "다림질을하는 동안 최대 순간 속도 변화."

msgctxt "jerk_wall_x description"
msgid "The maximum instantaneous velocity change with which all inner walls are printed."
msgstr "모든 내부 벽이 프린팅되는 최대 순간 속도 변화."

msgctxt "jerk_infill description"
msgid "The maximum instantaneous velocity change with which infill is printed."
msgstr "내부채움이 프린팅되는 최대 순간 속도 변화."

msgctxt "jerk_support_bottom description"
msgid "The maximum instantaneous velocity change with which the floors of support are printed."
msgstr "서포트의 바닥이 프린팅되는 최대 순간 속도 변화."

msgctxt "jerk_support_infill description"
msgid "The maximum instantaneous velocity change with which the infill of support is printed."
msgstr "서포트가 채워지는 최대 순간 속도 변화."

msgctxt "jerk_wall_0 description"
msgid "The maximum instantaneous velocity change with which the outermost walls are printed."
msgstr "가장 바깥 쪽 벽이 프린팅되는 최대 순간 속도 변화."

msgctxt "jerk_prime_tower description"
msgid "The maximum instantaneous velocity change with which the prime tower is printed."
msgstr "프라임 타워가 프린팅되는 최대 순간 속도 변화."

msgctxt "jerk_support_interface description"
msgid "The maximum instantaneous velocity change with which the roofs and floors of support are printed."
msgstr "서포트의 지붕과 바닥이 프린팅되는 최대 순간 속도 변화."

msgctxt "jerk_support_roof description"
msgid "The maximum instantaneous velocity change with which the roofs of support are printed."
msgstr "서포트의 지붕이 프린팅되는 최대 순간 속도 변화."

msgctxt "jerk_skirt_brim description"
msgid "The maximum instantaneous velocity change with which the skirt and brim are printed."
msgstr "스커트와 브림이 프린팅되는 최대 순간 속도 변화."

msgctxt "jerk_support description"
msgid "The maximum instantaneous velocity change with which the support structure is printed."
msgstr "서포트 구조가 프린팅되는 최대 순간 속도 변화."

msgctxt "jerk_wall_x_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface inner walls are printed."
msgstr "상면 바깥 벽이 인쇄되는 최대 순간 속도 변화"

msgctxt "jerk_wall_0_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface outermost walls are printed."
msgstr "상면 내부 벽이 인쇄되는 최대 순간 속도 변화"

msgctxt "jerk_wall description"
msgid "The maximum instantaneous velocity change with which the walls are printed."
msgstr "벽이 프린팅되는 최대 순간 속도 변화."

msgctxt "jerk_roofing description"
msgid "The maximum instantaneous velocity change with which top surface skin layers are printed."
msgstr "상단 표면 스킨 층이 프린팅되는 최대 순간 속도 변화."

msgctxt "jerk_topbottom description"
msgid "The maximum instantaneous velocity change with which top/bottom layers are printed."
msgstr "상단 / 하단 레이어가 프린팅되는 최대 순간 속도 변화."

msgctxt "jerk_travel description"
msgid "The maximum instantaneous velocity change with which travel moves are made."
msgstr "헤드가 이동하는 최대 순간 속도 변화."

msgctxt "prime_tower_max_bridging_distance description"
msgid "The maximum length of the branches which may be printed over the air."
msgstr "공중에서 출력될 수 있는 브랜치의 최대 길이입니다."

msgctxt "machine_max_feedrate_x description"
msgid "The maximum speed for the motor of the X-direction."
msgstr "X 방향의 모터 최대 속도입니다."

msgctxt "machine_max_feedrate_y description"
msgid "The maximum speed for the motor of the Y-direction."
msgstr "Y 방향 모터의 최대 속도입니다."

msgctxt "machine_max_feedrate_z description"
msgid "The maximum speed for the motor of the Z-direction."
msgstr "Z 방향의 모터 최대 속도입니다."

msgctxt "machine_max_feedrate_e description"
msgid "The maximum speed of the filament."
msgstr "필라멘트의 최대 속도."

msgctxt "support_bottom_stair_step_width description"
msgid "The maximum width of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures."
msgstr "모델에있는 서포트의 계단 모양 바닥의 최대 폭. 값이 낮으면 서포트을 제거하기가 어려워 서포트만 값이 너무 높으면 불안정한 서포트 구조가 생길 수 있습니다."

msgctxt "mold_width description"
msgid "The minimal distance between the outside of the mold and the outside of the model."
msgstr "몰드의 바깥쪽과 모델의 바깥쪽 사이의 최소 거리입니다."

msgctxt "machine_minimum_feedrate description"
msgid "The minimal movement speed of the print head."
msgstr "프린트 헤드의 최소 이동 속도."

msgctxt "material_initial_print_temperature description"
msgid "The minimal temperature while heating up to the Printing Temperature at which printing can already start."
msgstr "프린팅이 시작될 수 있는 프린팅 온도까지 가열하는 동안의 최소 온도."

msgctxt "machine_min_cool_heat_time_window description"
msgid "The minimal time an extruder has to be inactive before the nozzle is cooled. Only when an extruder is not used for longer than this time will it be allowed to cool down to the standby temperature."
msgstr "노즐이 냉각되기 전에 익스트루더가 비활성이어야하는 최소 시간. 이 시간보다 오래 익스트루더를 사용하지 않을 경우에만 대기 온도로 냉각시킬 수 있습니다."

msgctxt "infill_support_angle description"
msgid "The minimum angle of internal overhangs for which infill is added. At a value of 0° objects are totally filled with infill, 90° will not provide any infill."
msgstr "충진물이 추가되는 내부 오버행의 최소 각도. 0°에서는 개체가 충진물로 완전히 채워지지만, 90°에서는 충진물이 공급되지 않습니다."

msgctxt "support_angle description"
msgid "The minimum angle of overhangs for which support is added. At a value of 0° all overhangs are supported, 90° will not provide any support."
msgstr "서포트가 추가 된 오버행 각도의 최소값입니다. 0 °의 값에서 모든 돌출부가 서포트가 생성되며 90 °는 지원하지 않습니다."

msgctxt "retraction_min_travel description"
msgid "The minimum distance of travel needed for a retraction to happen at all. This helps to get fewer retractions in a small area."
msgstr "리트렉션이 가능하기 위해 필요한 최소한의 이동 거리. 작은 영역에서 더 적은 리트렉션이 가능합니다."

msgctxt "skirt_brim_minimal_length description"
msgid "The minimum length of the skirt or brim. If this length is not reached by all skirt or brim lines together, more skirt or brim lines will be added until the minimum length is reached. Note: If the line count is set to 0 this is ignored."
msgstr "스커트 또는 브림의 최소 길이. 이 길이에 모든 스커트 또는 브림 선이 모두 도달하지 않으면 최소 길이에 도달 할 때까지 더 많은 스커트 또는 브림 선이 추가됩니다. 참고 : 0으로 설정하면 무시됩니다."

msgctxt "min_odd_wall_line_width description"
msgid "The minimum line width for middle line gap filler polyline walls. This setting determines at which model thickness we switch from printing two wall lines, to printing two outer walls and a single central wall in the middle. A higher Minimum Odd Wall Line Width leads to a higher maximum even wall line width. The maximum odd wall line width is calculated as 2 * Minimum Even Wall Line Width."
msgstr "중간 선 간격 충전재 폴리라인 벽의 최소 선 너비. 이 설정은 두 개의 벽 선을 프린팅 하는 것에서 두 개의 외벽 및 가운데의 단일 중앙 벽 프린팅으로 전환하는 모델 두께를 결정합니다. 최소 짝수 벽 선 너비가 더 높을수록 최대 홀수 벽 선 너비가 높아집니다. 최대 홀수 벽 너비는 2 * 최소 짝수 벽 선 너비로 계산됩니다."

msgctxt "min_even_wall_line_width description"
msgid "The minimum line width for normal polygonal walls. This setting determines at which model thickness we switch from printing a single thin wall line, to printing two wall lines. A higher Minimum Even Wall Line Width leads to a higher maximum odd wall line width. The maximum even wall line width is calculated as Outer Wall Line Width + 0.5 * Minimum Odd Wall Line Width."
msgstr "일반 다각형 벽의 최소 선 너비 이 설정은 단일의 얇은 벽 선 프린팅에서 두 개의 벽 선 프린팅으로 전환하는 모델 두께를 결정합니다. 최소 짝수 벽 선 너비가 더 높을수록 최대 홀수 벽 선 너비가 높아집니다. 최대 짝수 벽 선 너비는 외벽 선 너비 + 0.5 * 최소 홀수 벽 선 너비로 계산됩니다."

msgctxt "cool_min_speed description"
msgid "The minimum print speed, despite slowing down due to the minimum layer time. When the printer would slow down too much, the pressure in the nozzle would be too low and result in bad print quality."
msgstr "최소 프린팅 속도. 프린터의 속도가 너무 느려지면 노즐의 압력이 너무 낮아 프린팅 품질이 나빠질 수 있습니다."

msgctxt "meshfix_maximum_resolution description"
msgid "The minimum size of a line segment after slicing. If you increase this, the mesh will have a lower resolution. This may allow the printer to keep up with the speed it has to process g-code and will increase slice speed by removing details of the mesh that it can't process anyway."
msgstr "슬라이딩 후의 선분의 최소 크기입니다. 이 값을 높이면 메쉬의 해상도가 낮아집니다. 그러면 프린터가 G 코드를 처리하는 데 필요한 속도를 유지할 수 있으며 처리할 수 없는 메쉬의 디테일이 제거되므로 슬라이드 속도가 높아집니다."

msgctxt "meshfix_maximum_travel_resolution description"
msgid "The minimum size of a travel line segment after slicing. If you increase this, the travel moves will have less smooth corners. This may allow the printer to keep up with the speed it has to process g-code, but it may cause model avoidance to become less accurate."
msgstr "슬라이딩 후의 이동 선분의 최소 크기입니다. 이 값을 높이면 코너에서 매끄럽게 이동하는 정도가 감소합니다. 프린터가 G 코드를 처리하는 데 필요한 속도를 유지할 수 있지만, 모델을 피하기 때문에 정확도가 감소합니다."

msgctxt "support_bottom_stair_step_min_slope description"
msgid "The minimum slope of the area for stair-stepping to take effect. Low values should make support easier to remove on shallower slopes, but really low values may result in some very counter-intuitive results on other parts of the model."
msgstr "계단 스텝이 적용되는 영역의 최소 경사입니다. 값이 낮을수록 낮은 각도 서포트 제거가 쉬워지지만 값을 너무 낮게 지정하면 모델의 다른 부분에서 적절하지 않은 결과가 발생할 수 있습니다."

msgctxt "cool_min_layer_time description"
msgid "The minimum time spent in a layer. This forces the printer to slow down, to at least spend the time set here in one layer. This allows the printed material to cool down properly before printing the next layer. Layers may still take shorter than the minimal layer time if Lift Head is disabled and if the Minimum Speed would otherwise be violated."
msgstr "레이어에 소요 된 최소 시간입니다. 이렇게 하면 프린터가 한 레이어에서 여기에 설정된 시간을 소비하게됩니다. 이렇게하면 다음 레이어를 프린팅하기 전에 출력물을 적절히 냉각시킬 수 있습니다. 리프트 헤드가 비활성화되고 최소 속도가 위반되는 경우 레이어가 최소 레이어 시간보다 짧게 걸릴 수 있습니다."

msgctxt "prime_tower_min_volume description"
msgid "The minimum volume for each layer of the prime tower in order to purge enough material."
msgstr "충분한 재료를 퍼지하기 위해 프라임 타워 각 층의 최소 부피."

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model description"
msgid "The most the diameter of a branch that has to connect to the model may increase by merging with branches that could reach the buildplate. Increasing this reduces print time, but increases the area of support that rests on model"
msgstr "모델에 연결해야 하는 브랜치의 직경은 빌드 플레이트에 도달할 수 있는 브랜치와 병합하여 최대로 늘릴 수 있습니다. 이를 늘리면 프린트 시간이 단축되지만 모델에 닿는 서포트 영역이 증가합니다."

msgctxt "machine_name description"
msgid "The name of your 3D printer model."
msgstr "3D 프린터 모델의 이름."

msgctxt "machine_nozzle_id description"
msgid "The nozzle ID for an extruder train, such as \"AA 0.4\" and \"BB 0.8\"."
msgstr "\"AA 0.4\"및 \"BB 0.8\"과 같은 익스트루더의 노즐 ID."

msgctxt "travel_avoid_other_parts description"
msgid "The nozzle avoids already printed parts when traveling. This option is only available when combing is enabled."
msgstr "노즐은 이동 할 때 이미 프린팅 된 부분을 피합니다. 이 옵션은 combing이 활성화 된 경우에만 사용할 수 있습니다."

msgctxt "travel_avoid_supports description"
msgid "The nozzle avoids already printed supports when traveling. This option is only available when combing is enabled."
msgstr "노즐은 이동하는 경우 이미 인쇄된 지지대를 피합니다. 빗질을 사용하는 경우에만 사용할 수 있는 옵션입니다."

msgctxt "bottom_layers description"
msgid "The number of bottom layers. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "아래층의 수. 바닥 두께로 계산을 할때 이 값은 벽 두께로 계산할 때 이 값은 반올림됩니다."

msgctxt "raft_base_wall_count description"
msgid "The number of contours to print around the linear pattern in the base layer of the raft."
msgstr "래프트의 베이스 레이어에 있는 선형 패턴 주위에 프린팅 할 윤곽의 수."

msgctxt "raft_interface_wall_count description"
msgid "The number of contours to print around the linear pattern in the middle layers of the raft."
msgstr "래프트의 중간 레이어에 있는 선형 패턴 주위에 출력할 윤곽선 수입니다."

msgctxt "raft_surface_wall_count description"
msgid "The number of contours to print around the linear pattern in the top layers of the raft."
msgstr "래프트의 상단 레이어에 있는 선형 패턴 주위에 출력할 윤곽선 수입니다."

msgctxt "raft_wall_count description"
msgid "The number of contours to print around the linear pattern of the raft."
msgstr "래프트의 선형 패턴 주위에 출력할 윤곽선 수입니다."

msgctxt "skin_edge_support_layers description"
msgid "The number of infill layers that supports skin edges."
msgstr "스킨 에지를 지원하는 내부채움 레이어의 수."

msgctxt "initial_bottom_layers description"
msgid "The number of initial bottom layers, from the build-plate upwards. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "빌드 플레이트에서 위를 향하는 초기 하단 레이어 수. 하단 두께로 계산할 경우, 이 값이 전체 값으로 반올림됩니다."

msgctxt "raft_interface_layers description"
msgid "The number of layers between the base and the surface of the raft. These comprise the main thickness of the raft. Increasing this creates a thicker, sturdier raft."
msgstr "래프트의 베이스와 표면 사이에 있는 레이어의 수. 이 수가 래프트의 주요 두께를 구성합니다. 이 수가 증가하면 래프트가 더 두껍고 튼튼해집니다."

msgctxt "brim_line_count description"
msgid "The number of lines used for a brim. More brim lines enhance adhesion to the build plate, but also reduces the effective print area."
msgstr "브림에 사용되는 선의 수입니다. 더 많은 브림 선이 빌드 플레이트에 대한 접착력을 향상 시키지만 유효 프린트 영역도 감소시킵니다."

msgctxt "support_brim_line_count description"
msgid "The number of lines used for the support brim. More brim lines enhance adhesion to the build plate, at the cost of some extra material."
msgstr "서포트 브림에 사용되는 라인의 수. 브림 라인이 많아질수록 추가 재료가 소요되지만 빌드 플레이트에 대한 접착력이 향상됩니다."

msgctxt "raft_surface_layers description"
msgid "The number of top layers on top of the 2nd raft layer. These are fully filled layers that the model sits on. 2 layers result in a smoother top surface than 1."
msgstr "두 번째 래프트 레이어 맨 위에있는 최상위 레이어의 수입니다. 이것들은 모델이 위치하는 완전히 채워진 레이어입니다. 2층은 1보다 부드러운 표면을 만듭니다."

msgctxt "top_layers description"
msgid "The number of top layers. When calculated by the top thickness, this value is rounded to a whole number."
msgstr "출력물의 상단 레이어의 수. 상단 두깨로 계산을 할때 이 값은 벽 두께로 계산할 때 이 값은 반올림됩니다."

msgctxt "roofing_layer_count description"
msgid "The number of top most skin layers. Usually only one top most layer is sufficient to generate higher quality top surfaces."
msgstr "최상층의 스킨 층의 수. 일반적으로 고품질의 표면을 생성하기 위해 맨위의 레이어 하나만 있으면 충분합니다."

msgctxt "support_wall_count description"
msgid "The number of walls with which to surround support infill. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "지지대 충진물을 둘러싸는 벽의 개수. 벽을 추가하면 지지물 인쇄 안정성을 높일 수 있고 오버행 지지를 개선할 수 있지만, 인쇄 시간과 사용되는 재료가 늘어납니다."

msgctxt "support_bottom_wall_count description"
msgid "The number of walls with which to surround support interface floor. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "지지대 인터페이스 바닥을 둘러쌀 벽의 수입니다. 벽을 추가하면 지지대 프린트를 더 안정적으로 만들고 오버행(경사면)을 더 잘 지원할 수 있지만, 프린트 시간과 사용되는 재료가 늘어납니다."

msgctxt "support_roof_wall_count description"
msgid "The number of walls with which to surround support interface roof. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "지지대 인터페이스 지붕을 둘러쌀 벽의 수입니다. 벽을 추가하면 지지대 프린트를 더 안정적으로 만들고 오버행(경사면)을 더 잘 지원할 수 있지만, 프린트 시간과 사용되는 재료가 늘어납니다."

msgctxt "support_interface_wall_count description"
msgid "The number of walls with which to surround support interface. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "지지대 인터페이스를 둘러쌀 벽의 수입니다. 벽을 추가하면 지지대 프린트를 더 안정적으로 만들고 오버행(경사면)을 더 잘 지원할 수 있지만, 프린트 시간과 사용되는 재료가 늘어납니다."

msgctxt "wall_distribution_count description"
msgid "The number of walls, counted from the center, over which the variation needs to be spread. Lower values mean that the outer walls don't change in width."
msgstr "중앙에서부터 계산되는 벽의 수로, 이를 통해 오차가 분산되어야 합니다. 값이 작다고 해서 외벽의 너비가 변경되지 않는 것은 아닙니다."

msgctxt "wall_line_count description"
msgid "The number of walls. When calculated by the wall thickness, this value is rounded to a whole number."
msgstr "벽의 수. 벽 두께로 계산할 때 이 값은 반올림됩니다."

msgctxt "machine_nozzle_tip_outer_diameter description"
msgid "The outer diameter of the tip of the nozzle."
msgstr "노즐 끝의 외경."

msgctxt "infill_pattern description"
msgid "The pattern of the infill material of the print. The line and zig zag infill swap direction on alternate layers, reducing material cost. The grid, triangle, tri-hexagon, cubic, octet, quarter cubic, cross and concentric patterns are fully printed every layer. Gyroid, cubic, quarter cubic and octet infill change with every layer to provide a more equal distribution of strength over each direction. Lightning infill tries to minimize the infill, by only supporting the ceiling of the object."
msgstr "프린트 내부채움 재료의 패턴입니다. 선형과 지그재그형 내부채움이 서로 다른 레이어에서 방향을 바꾸므로 재료비가 절감됩니다. 격자, 삼각형, 트라이 헥사곤 (tri-hexagon), 큐빅, 옥텟 (octet), 쿼터 큐빅, 크로스, 동심원 패턴이 레이어마다 완전히 프린트됩니다. 자이로이드 (Gyroid), 큐빅, 쿼터 큐빅, 옥텟 (octet) 내부채움이 레이어마다 변경되므로 각 방향으로 힘이 더 균등하게 분산됩니다. 라이트닝 내부채움이 객체의 천장만 서포트하여 내부채움을 최소화합니다."

msgctxt "support_pattern description"
msgid "The pattern of the support structures of the print. The different options available result in sturdy or easy to remove support."
msgstr "서포트 구조의 패턴. 사용 가능한 여러 가지 옵션을 사용하면 튼튼하고 쉽게 제거 할 수 있습니다."

msgctxt "roofing_pattern description"
msgid "The pattern of the top most layers."
msgstr "최상위 레이어의 패턴."

msgctxt "top_bottom_pattern description"
msgid "The pattern of the top/bottom layers."
msgstr "상단/하단 레이어의 패턴."

msgctxt "top_bottom_pattern_0 description"
msgid "The pattern on the bottom of the print on the first layer."
msgstr "첫 번째 레이어의 프린팅 아래쪽에 있는 패턴입니다."

msgctxt "ironing_pattern description"
msgid "The pattern to use for ironing top surfaces."
msgstr "윗면을 다림질 할 때 사용하는 패턴."

msgctxt "support_bottom_pattern description"
msgid "The pattern with which the floors of the support are printed."
msgstr "서포트의 바닥이 프린팅되는 패턴."

msgctxt "support_interface_pattern description"
msgid "The pattern with which the interface of the support with the model is printed."
msgstr "모델과 서포트 인터페이스를 프린팅하는 패턴입니다."

msgctxt "support_roof_pattern description"
msgid "The pattern with which the roofs of the support are printed."
msgstr "서포트의 지붕이 프린팅되는 패턴."

msgctxt "z_seam_position description"
msgid "The position near where to start printing each part in a layer."
msgstr "레이어에서 각 부품의 프린팅이 시작할 위치 근처입니다."

msgctxt "support_tree_angle_slow description"
msgid "The preferred angle of the branches, when they do not have to avoid the model. Use a lower angle to make them more vertical and more stable. Use a higher angle for branches to merge faster."
msgstr "모델을 피할 필요가 없을 때 브랜치의 기본 각도입니다. 브랜치가 수직에 가깝게 안정적이 되도록 만들려면 더 낮은 각도를 사용하고, 브랜치가 빨리 합쳐지도록 하려면 더 높은 각도를 사용하십시오."

msgctxt "support_tree_rest_preference description"
msgid "The preferred placement of the support structures. If structures can't be placed at the preferred location, they will be place elsewhere, even if that means placing them on the model."
msgstr "서포트 구조물의 기본 배치입니다. 구조물을 원하는 위치에 배치할 수 없는 경우 구조물을 모델 위에 놓더라도 다른 곳에 배치합니다."

msgctxt "jerk_layer_0 description"
msgid "The print maximum instantaneous velocity change for the initial layer."
msgstr "초기 레이어의 프린팅 최대 순간 속도 변화."

msgctxt "machine_shape description"
msgid "The shape of the build plate without taking unprintable areas into account."
msgstr "프린팅 할 수 없는 영역을 고려하지 않은 빌드 플레이트의 모양."

msgctxt "machine_head_with_fans_polygon description"
msgid "The shape of the print head. These are coordinates relative to the position of the print head, which is usually the position of its first extruder. The dimensions left and in front of the print head must be negative coordinates."
msgstr "프린트 헤드의 모양. 일반적으로 첫 번째 압출기의 위치인 프린트 헤드의 위치와 관련된 좌표입니다. 프린트 헤드의 왼쪽 및 앞쪽 치수는 음수 좌표여야 합니다."

msgctxt "cross_infill_pocket_size description"
msgid "The size of pockets at four-way crossings in the cross 3D pattern at heights where the pattern is touching itself."
msgstr "패턴이 접촉되는 높이에서 크로스 3D 패턴의 4 방향 교차점에있는 포켓의 크기입니다."

msgctxt "coasting_min_volume description"
msgid "The smallest volume an extrusion path should have before allowing coasting. For smaller extrusion paths, less pressure has been built up in the bowden tube and so the coasted volume is scaled linearly. This value should always be larger than the Coasting Volume."
msgstr "코스팅(Coasting)을 허용하기 전에 압출 경로에 있어야하는 최소 양. 작은 압출 경로의 경우 보우덴 튜브에 가해지는 압력이 적기 때문에 코스팅(Coasting) 부피가 선형 적으로 조정됩니다. 이 값은 항상 코스팅(Coasting) 양보다 커야합니다."

msgctxt "machine_nozzle_cool_down_speed description"
msgid "The speed (°C/s) by which the nozzle cools down averaged over the window of normal printing temperatures and the standby temperature."
msgstr "노즐이 냉각되는 속도 (°C/s)는 일반적인 프린팅 온도 및 대기 온도에서 평균을 냅니다."

msgctxt "machine_nozzle_heat_up_speed description"
msgid "The speed (°C/s) by which the nozzle heats up averaged over the window of normal printing temperatures and the standby temperature."
msgstr "노즐이 가열되는 속도 (°C/s)는 일반적인 프린팅 온도와 대기 온도에서 평균을 냅니다."

msgctxt "speed_wall_x description"
msgid "The speed at which all inner walls are printed. Printing the inner wall faster than the outer wall will reduce printing time. It works well to set this in between the outer wall speed and the infill speed."
msgstr "모든 내부 벽이 프린팅되는 속도입니다. 내벽을 외벽보다 빠르게 프린팅하면 프린팅 시간이 단축됩니다. 외벽 속도와 충전 속도 사이에서 이것을 설정하는 것이 효과적입니다."

msgctxt "bridge_skin_speed description"
msgid "The speed at which bridge skin regions are printed."
msgstr "브릿지 스킨 층이 프린팅되는 속도."

msgctxt "speed_infill description"
msgid "The speed at which infill is printed."
msgstr "내부채움이 프린팅되는 속도."

msgctxt "speed_print description"
msgid "The speed at which printing happens."
msgstr "프린팅 속도."

msgctxt "raft_base_speed description"
msgid "The speed at which the base raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "기본 래프트 레이어가 프린팅되는 속도입니다. 이것은 노즐에서 나오는 재료의 양이 상당히 많기 때문에 아주 천천히 프린팅되어야 합니다."

msgctxt "bridge_wall_speed description"
msgid "The speed at which the bridge walls are printed."
msgstr "브릿지 벽이 프린팅되는 속도."

msgctxt "cool_fan_speed_0 description"
msgid "The speed at which the fans spin at the start of the print. In subsequent layers the fan speed is gradually increased up to the layer corresponding to Regular Fan Speed at Height."
msgstr "프린팅 시작시 팬이 회전하는 속도입니다. 후속 레이어에서는 팬 속도가 높이의 표준 팬 속도에 해당하는 레이어까지 점차 증가합니다."

msgctxt "cool_fan_speed_min description"
msgid "The speed at which the fans spin before hitting the threshold. When a layer prints faster than the threshold, the fan speed gradually inclines towards the maximum fan speed."
msgstr "팬이 임계 값에 도달하기 전에 회전하는 속도입니다. 레이어가 임계값보다 빠르게 프린팅되면 팬 속도가 최대 팬 속도쪽으로 점차 기울어집니다."

msgctxt "cool_fan_speed_max description"
msgid "The speed at which the fans spin on the minimum layer time. The fan speed gradually increases between the regular fan speed and maximum fan speed when the threshold is hit."
msgstr "최소 레이어 시간에 팬이 회전하는 속도입니다. 임계 값에 도달하면 표준 팬 속도와 최대 팬 속도 사이에서 팬 속도가 서서히 증가합니다."

msgctxt "retraction_prime_speed description"
msgid "The speed at which the filament is primed during a retraction move."
msgstr "리트렉션 이동 중에 필라멘트가 프라이밍되는 속도입니다."

msgctxt "wipe_retraction_prime_speed description"
msgid "The speed at which the filament is primed during a wipe retraction move."
msgstr "와이프 리트랙션 이동 중에 필라멘트가 초기화되는 속도입니다."

msgctxt "switch_extruder_prime_speed description"
msgid "The speed at which the filament is pushed back after a nozzle switch retraction."
msgstr "노즐 스위치 리트렉션 후 필라멘트가 뒤로 밀리는 속도."

msgctxt "retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a retraction move."
msgstr "리트렉션 속도입니다."

msgctxt "wipe_retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a wipe retraction move."
msgstr "와이프 리트랙션 이동 중에 필라멘트가 리트렉션 및 준비되는 속도입니다."

msgctxt "switch_extruder_retraction_speed description"
msgid "The speed at which the filament is retracted during a nozzle switch retract."
msgstr "노즐 스위치 리트렉션시 필라멘트가 리트렉션하는 속도."

msgctxt "retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a retraction move."
msgstr "리트렉션 속도입니다."

msgctxt "wipe_retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a wipe retraction move."
msgstr "와이프 리트랙션 이동 중에 필라멘트가 리트렉트되는 속도입니다."

msgctxt "switch_extruder_retraction_speeds description"
msgid "The speed at which the filament is retracted. A higher retraction speed works better, but a very high retraction speed can lead to filament grinding."
msgstr "필라멘트가 리트렉션 되는 속도입니다. 리트렉션 속도가 빠르면 좋지만 리트렉션 속도가 높으면 필라멘트가 갈릴 수 있습니다."

msgctxt "speed_support_bottom description"
msgid "The speed at which the floor of support is printed. Printing it at lower speed can improve adhesion of support on top of your model."
msgstr "서포트 바닥 프린팅 속도. 더 낮은 속도로 프린팅하면 모델 상단의 서포트력이 향상됩니다."

msgctxt "speed_support_infill description"
msgid "The speed at which the infill of support is printed. Printing the infill at lower speeds improves stability."
msgstr "서포트의 내부채움이 프린팅되는 속도. 내부채움을 저속으로 프린팅하면 안정성이 향상됩니다."

msgctxt "raft_interface_speed description"
msgid "The speed at which the middle raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "중간 래프트 층이 프린팅되는 속도. 이것은 노즐에서 나오는 재료의 양이 상당히 많기 때문에 아주 천천히 프린팅되어야합니다."

msgctxt "speed_wall_0 description"
msgid "The speed at which the outermost walls are printed. Printing the outer wall at a lower speed improves the final skin quality. However, having a large difference between the inner wall speed and the outer wall speed will affect quality in a negative way."
msgstr "가장 바깥 쪽 벽이 프린팅되는 속도입니다. 외벽을 더 낮은 속도로 프린팅하면 최종 스킨 품질이 향상됩니다. 그러나 내벽 속도와 외벽 속도 사이에 큰 차이가있을 경우 부정적인 방식으로 품질에 영향을 미칩니다."

msgctxt "speed_prime_tower description"
msgid "The speed at which the prime tower is printed. Printing the prime tower slower can make it more stable when the adhesion between the different filaments is suboptimal."
msgstr "프라임 타워가 프린팅되는 속도. 프라임 타워를 더 천천히 프린팅하면 다른 필라멘트 사이의 접착을 더 안정적으로 만들 수 있습니다."

msgctxt "cool_fan_speed description"
msgid "The speed at which the print cooling fans spin."
msgstr "프린팅 냉각 팬이 회전하는 속도입니다."

msgctxt "raft_speed description"
msgid "The speed at which the raft is printed."
msgstr "래프트가 프린팅되는 속도."

msgctxt "speed_support_interface description"
msgid "The speed at which the roofs and floors of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "서포트의 지붕과 바닥이 프린팅되는 속도. 프린팅 속도를 느리게하면 오버행 품질이 향상 될 수 있습니다."

msgctxt "speed_support_roof description"
msgid "The speed at which the roofs of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "서포트의 지붕이 프린팅되는 속도입니다. 프린팅 속도를 느리게하면 오버행 품질이 향상 될 수 있습니다."

msgctxt "skirt_brim_speed description"
msgid "The speed at which the skirt and brim are printed. Normally this is done at the initial layer speed, but sometimes you might want to print the skirt or brim at a different speed."
msgstr "스커트와 브림이 프린팅되는 속도입니다. 일반적으로 이것은 초기 레이어 속도에서 수행되지만 때로는 스커트나 브림을 다른 속도로 프린팅하려고 할 수 있습니다."

msgctxt "speed_support description"
msgid "The speed at which the support structure is printed. Printing support at higher speeds can greatly reduce printing time. The surface quality of the support structure is not important since it is removed after printing."
msgstr "서포트 구조가 프린팅되는 속도입니다. 서포트를 고속으로 프린팅하면 프린팅 시간을 크게 단축시킵니다. 서포트 구조체의 표면 품질은 프린팅 후에 제거되므로 중요하지 않습니다."

msgctxt "raft_surface_speed description"
msgid "The speed at which the top raft layers are printed. These should be printed a bit slower, so that the nozzle can slowly smooth out adjacent surface lines."
msgstr "상단 래프트 레이어가 프린팅되는 속도입니다. 이 노즐은 조금 더 느리게 프린팅해야 노즐이 인접한 표면 선을 천천히 부드럽게 할 수 있습니다."

msgctxt "speed_wall_x_roofing description"
msgid "The speed at which the top surface inner walls are printed."
msgstr "상면 내벽이 인쇄되는 속도"

msgctxt "speed_wall_0_roofing description"
msgid "The speed at which the top surface outermost wall is printed."
msgstr "상면의 가장 바깥 벽이 인쇄되는 속도"

msgctxt "speed_z_hop description"
msgid "The speed at which the vertical Z movement is made for Z Hops. This is typically lower than the print speed since the build plate or machine's gantry is harder to move."
msgstr "Z 홉을 위해 수직 Z 이동이 이루어지는 속도입니다. 빌드 플레이트 또는 기기의 갠트리를 움직이기가 더 어렵기 때문에 프린트 속도보다 낮은 것이 일반적입니다."

msgctxt "speed_wall description"
msgid "The speed at which the walls are printed."
msgstr "벽이 프린팅되는 속도입니다."

msgctxt "speed_ironing description"
msgid "The speed at which to pass over the top surface."
msgstr "상단 표면을 통과하는 속도."

msgctxt "material_break_speed description"
msgid "The speed at which to retract the filament in order to break it cleanly."
msgstr "필라멘트가 깔끔하게 파단되기 위해 후퇴해야 하는 속도입니다."

msgctxt "speed_roofing description"
msgid "The speed at which top surface skin layers are printed."
msgstr "상단 표면 스킨 층이 프린팅되는 속도."

msgctxt "speed_topbottom description"
msgid "The speed at which top/bottom layers are printed."
msgstr "위쪽/아래쪽 레이어가 프린팅되는 속도입니다."

msgctxt "speed_travel description"
msgid "The speed at which travel moves are made."
msgstr "움직일때의 이동 속도."

msgctxt "coasting_speed description"
msgid "The speed by which to move during coasting, relative to the speed of the extrusion path. A value slightly under 100% is advised, since during the coasting move the pressure in the bowden tube drops."
msgstr "코스팅(Coasting)시 이동 속도. 압출 경로의 속도에 상대적입니다. 코스팅(Coasting) 이동 중에 보우 덴 튜브의 압력이 떨어지기 때문에 100% 보다 약간 작은 값을 권합니다."

msgctxt "speed_layer_0 description"
msgid "The speed for the initial layer. A lower value is advised to improve adhesion to the build plate. Does not affect the build plate adhesion structures themselves, like brim and raft."
msgstr "초기 레이어의 속도입니다. 빌드 플레이트에 대한 접착력을 향상시키려면 낮은 값을 권장합니다. 브림과 래프트 같은 빌드 플레이트 접착 구조 자체에 영향을 미치지 않습니다."

msgctxt "speed_print_layer_0 description"
msgid "The speed of printing for the initial layer. A lower value is advised to improve adhesion to the build plate."
msgstr "초기 레이어의 프린팅 속도입니다. 빌드 플레이트에 대한 접착력을 향상 시키려면 낮은 값을 권합니다."

msgctxt "speed_travel_layer_0 description"
msgid "The speed of travel moves in the initial layer. A lower value is advised to prevent pulling previously printed parts away from the build plate. The value of this setting can automatically be calculated from the ratio between the Travel Speed and the Print Speed."
msgstr "이동 속도는 초기 레이어에서 이동합니다. 이전에 프린팅 된 부품을 빌드 플레이트에서 떨어지는 것을 방지하려면 더 낮은 값을 권합니다. 이 설정의 값은 이동 속도와 프린팅 속도 사이의 비율로부터 자동으로 계산 될 수 있습니다."

msgctxt "material_break_temperature description"
msgid "The temperature at which the filament is broken for a clean break."
msgstr "필라멘트가 깔끔하게 파단되는 온도입니다."

msgctxt "build_volume_temperature description"
msgid "The temperature of the environment to print in. If this is 0, the build volume temperature will not be adjusted."
msgstr "프린팅되는 환경의 온도입니다. 이 값이 0인 경우 빌드 볼륨 온도는 조정되지 않습니다."

msgctxt "material_standby_temperature description"
msgid "The temperature of the nozzle when another nozzle is currently used for printing."
msgstr "다른 노즐이 현재 프린팅에 사용될 경우 노즐 온도."

msgctxt "material_final_print_temperature description"
msgid "The temperature to which to already start cooling down just before the end of printing."
msgstr "프린팅 종료 직전에 냉각이 시작될 온도입니다."

msgctxt "material_print_temperature_layer_0 description"
msgid "The temperature used for printing the first layer."
msgstr "첫 레이어 인쇄에 사용되는 온도입니다."

msgctxt "material_print_temperature description"
msgid "The temperature used for printing."
msgstr "프린팅에 사용되는 온도."

msgctxt "material_bed_temperature_layer_0 description"
msgid "The temperature used for the heated build plate at the first layer. If this is 0, the build plate is left unheated during the first layer."
msgstr "첫 번째 레이어에서 내열 빌드 플레이트에 사용되는 온도. 0인 경우, 빌드 플레이트가 첫 번째 레이어에서 가열되지 않습니다."

msgctxt "material_bed_temperature description"
msgid "The temperature used for the heated build plate. If this is 0, the build plate is left unheated."
msgstr "내열 빌드 플레이트용으로 사용된 온도 0인 경우 빌드 플레이트가 가열되지 않습니다."

msgctxt "material_break_preparation_temperature description"
msgid "The temperature used to purge material, should be roughly equal to the highest possible printing temperature."
msgstr "재료를 퍼지하는 데 사용하는 온도는 가능한 한 가장 높은 프린팅 온도와 대략 같아야 합니다."

msgctxt "bottom_thickness description"
msgid "The thickness of the bottom layers in the print. This value divided by the layer height defines the number of bottom layers."
msgstr "출력물의 아래쪽 레이어의 두께. 이 값을 레이어 높이로 나눈 값은 맨 아래 레이어의 수 입니다."

msgctxt "skin_edge_support_thickness description"
msgid "The thickness of the extra infill that supports skin edges."
msgstr "스킨 에지를 지원하는 추가 내부채움의 두께."

msgctxt "support_interface_height description"
msgid "The thickness of the interface of the support where it touches with the model on the bottom or the top."
msgstr "밑면 또는 상단의 모델과 접촉하는 서포트 인터페이스 두께입니다."

msgctxt "support_bottom_height description"
msgid "The thickness of the support floors. This controls the number of dense layers that are printed on top of places of a model on which support rests."
msgstr "서포트 바닥의 두께. 이것은 서포트가 놓여있는 모델의 상단에 프린팅되는 조밀 한 층의 수를 제어합니다."

msgctxt "support_roof_height description"
msgid "The thickness of the support roofs. This controls the amount of dense layers at the top of the support on which the model rests."
msgstr "받침 지붕의 두께. 이것은 모델이 놓이는 받침대 상단의 조밀 한 층의 양을 제어합니다."

msgctxt "top_thickness description"
msgid "The thickness of the top layers in the print. This value divided by the layer height defines the number of top layers."
msgstr "출력물의 상단 레이어의 두께. 이 값을 레이어 높이로 나눈 값이 최상위 레이어 수 입니다."

msgctxt "top_bottom_thickness description"
msgid "The thickness of the top/bottom layers in the print. This value divided by the layer height defines the number of top/bottom layers."
msgstr "출력물의 상단/하단 레이어의 두께. 이 값을 레이어 높이로 나눈 값은 위쪽/아래쪽 레이어의 수 입니다."

msgctxt "wall_thickness description"
msgid "The thickness of the walls in the horizontal direction. This value divided by the wall line width defines the number of walls."
msgstr "가로 방향의 벽 두께입니다. 이 값을 벽 선 너비로 나눈 값은 벽의 수 입니다."

msgctxt "infill_sparse_thickness description"
msgid "The thickness per layer of infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "내부채움물 층의 두께. 이 값은 항상 레이어 높이의 배수이어야 하며 반올림됩니다."

msgctxt "support_infill_sparse_thickness description"
msgid "The thickness per layer of support infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "서포트 내부채의 레이어당 두께. 이 값은 항상 레이어 높이의 배수이 어야하며 그렇지 않으면 반올림됩니다."

msgctxt "machine_gcode_flavor description"
msgid "The type of g-code to be generated."
msgstr "생성 될 gcode의 유형."

msgctxt "material_type description"
msgid "The type of material used."
msgstr "사용된 재료의 유형입니다."

msgctxt "coasting_volume description"
msgid "The volume otherwise oozed. This value should generally be close to the nozzle diameter cubed."
msgstr "그렇지 않으면 볼륨이 흘러 나옵니다. 이 값은 일반적으로 노즐 직경 입방체에 가깝습니다."

msgctxt "machine_width description"
msgid "The width (X-direction) of the printable area."
msgstr "프린팅 가능 영역의 폭 (X 방향)."

msgctxt "support_brim_width description"
msgid "The width of the brim to print underneath the support. A larger brim enhances adhesion to the build plate, at the cost of some extra material."
msgstr "서포트 아래를 인쇄하기 위한 브림 폭. 브림이 커질수록 추가 재료가 소요되지만 빌드 플레이트에 대한 접착력이 향상됩니다."

msgctxt "interlocking_beam_width description"
msgid "The width of the interlocking structure beams."
msgstr "연동 구조 빔의 너비입니다."

msgctxt "prime_tower_base_size description"
msgid "The width of the prime tower brim/base. A larger base enhances adhesion to the build plate, but also reduces the effective print area."
msgstr "프라임 타워 브림/베이스의 폭입니다. 베이스가 크면 빌드 플레이트에 대한 접착력이 향상되지만, 실제 인쇄 영역은 줄어듭니다."

msgctxt "prime_tower_size description"
msgid "The width of the prime tower."
msgstr "프라임 타워의 너비."

msgctxt "magic_fuzzy_skin_thickness description"
msgid "The width within which to jitter. It's advised to keep this below the outer wall width, since the inner walls are unaltered."
msgstr "지터가 발생할 너비. 내벽이 변경되지 않으므로 외벽 너비 아래로 유지하는 것이 좋습니다."

msgctxt "retraction_extrusion_window description"
msgid "The window in which the maximum retraction count is enforced. This value should be approximately the same as the retraction distance, so that effectively the number of times a retraction passes the same patch of material is limited."
msgstr "최대 리트렉션 횟수가 시행되는 영역 입니다. 이 값은 수축 거리와 거의 같아야 하므로 같은 수축 패치가 통과하는 횟수가 효과적으로 제한됩니다."

msgctxt "prime_tower_position_x description"
msgid "The x coordinate of the position of the prime tower."
msgstr "프라임 타워 위치의 x 좌표입니다."

msgctxt "prime_tower_position_y description"
msgid "The y coordinate of the position of the prime tower."
msgstr "프라임 타워 위치의 y 좌표입니다."

msgctxt "support_meshes_present description"
msgid "There are support meshes present in the scene. This setting is controlled by Cura."
msgstr "장면에 서포트 메쉬가 있습니다. 이 설정은 Cura가 제어합니다."

msgctxt "bridge_wall_coast description"
msgid "This controls the distance the extruder should coast immediately before a bridge wall begins. Coasting before the bridge starts can reduce the pressure in the nozzle and may produce a flatter bridge."
msgstr "이것은 브릿지 벽이 시작되기 직전에 익스트루더가 있어야하는 거리를 제어합니다. 브릿지가 시작되기 전에 코스팅(coasting)을 하면 노즐의 압력을 낮추고 보다 평평한 브릿지를 만들 수 있습니다."

msgctxt "raft_base_smoothing description"
msgid "This setting controls how much inner corners in the raft base outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "이 설정은 래프트 베이스의 내부 모서리를 얼마나 둥글게 할지 제어합니다. 내부 모서리는 여기에 지정된 값과 동일한 반경을 가진 반원 모양으로 둥글게 다듬어집니다. 이 설정은 래프트 윤곽선에 있는 이러한 원보다 작은 구멍도 제거합니다."

msgctxt "raft_interface_smoothing description"
msgid "This setting controls how much inner corners in the raft middle outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "이 설정은 래프트 중간의 내부 모서리를 얼마나 둥글게 할지 제어합니다. 내부 모서리는 여기에 지정된 값과 동일한 반경을 가진 반원 모양으로 둥글게 다듬어집니다. 이 설정은 래프트 윤곽선에 있는 이러한 원보다 작은 구멍도 제거합니다."

msgctxt "raft_smoothing description"
msgid "This setting controls how much inner corners in the raft outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "이 설정은 래프트 윤곽의 안쪽 구석의 곡률을 제어합니다. 안쪽 구석이 여기에 지정된 값과 동일한 반경으로 반원 모양으로 휘어집니다. 또한 이 설정을 사용하면 래프트 윤곽에서 그러한 원보다 작은 구멍이 제거됩니다."

msgctxt "raft_surface_smoothing description"
msgid "This setting controls how much inner corners in the raft top outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "이 설정은 래프트 상단의 내부 모서리를 얼마나 둥글게 할지 제어합니다. 내부 모서리는 여기에 지정된 값과 동일한 반경을 가진 반원 모양으로 둥글게 다듬어집니다. 이 설정은 래프트 윤곽선에 있는 이러한 원보다 작은 구멍도 제거합니다."

msgctxt "retraction_count_max description"
msgid "This setting limits the number of retractions occurring within the minimum extrusion distance window. Further retractions within this window will be ignored. This avoids retracting repeatedly on the same piece of filament, as that can flatten the filament and cause grinding issues."
msgstr "이 설정은 최소 압출 거리에서 발생하는 리트렉션 수를 제한합니다. 이 거리내에서 더 이상의 리트렉션은 무시됩니다. 이렇게 하면 필라멘트를 평평하게하고 갈리는 문제를 일으킬 수 있으므로 동일한 필라멘트에서 반복적으로 리트렉션하지 않습니다."

msgctxt "draft_shield_enabled description"
msgid "This will create a wall around the model, which traps (hot) air and shields against exterior airflow. Especially useful for materials which warp easily."
msgstr "모델 주위에 벽이 생겨 외부 공기 흐름을 막아 (뜨거운) 공기를 막을 수 있습니다. 왜곡이 쉬운 소재에 특히 유용합니다."

msgctxt "support_tree_tip_diameter label"
msgid "Tip Diameter"
msgstr "팁 직경"

msgctxt "material_shrinkage_percentage_xy description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the XY-direction (horizontally)."
msgstr "냉각됨에 따라 재료의 수축을 보정하기 위해 모델이 이 배율로 XY 방향으로(수평으로) 확장됩니다."

msgctxt "material_shrinkage_percentage_z description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the Z-direction (vertically)."
msgstr "냉각됨에 따라 재료 수축을 보정하기 위해 모델이 이 배율로 Z 방향으로(수직으로) 확장됩니다."

msgctxt "material_shrinkage_percentage description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor."
msgstr "냉각됨에 따라 재료 수축을 보상하기 위해 모델이 이 배율로 확장됩니다."

msgctxt "top_layers label"
msgid "Top Layers"
msgstr "상단 레이어"

msgctxt "top_skin_expand_distance label"
msgid "Top Skin Expand Distance"
msgstr "윗면 스킨 확장 거리"

msgctxt "top_skin_preshrink label"
msgid "Top Skin Removal Width"
msgstr "상단 스킨 제거 폭"

msgctxt "acceleration_wall_x_roofing label"
msgid "Top Surface Inner Wall Acceleration"
msgstr "상면 내벽 가속도"

msgctxt "jerk_wall_x_roofing label"
msgid "Top Surface Inner Wall Jerk"
msgstr "상면 가장 바깥 벽의 최대 순간 속도 변화"

msgctxt "speed_wall_x_roofing label"
msgid "Top Surface Inner Wall Speed"
msgstr "상면 내벽 속도"

msgctxt "wall_x_material_flow_roofing label"
msgid "Top Surface Inner Wall(s) Flow"
msgstr "상면 내벽 흐름"

msgctxt "acceleration_wall_0_roofing label"
msgid "Top Surface Outer Wall Acceleration"
msgstr "상면 외벽 가속도"

msgctxt "wall_0_material_flow_roofing label"
msgid "Top Surface Outer Wall Flow"
msgstr "상면 가장 바깥 벽의 유량"

msgctxt "jerk_wall_0_roofing label"
msgid "Top Surface Outer Wall Jerk"
msgstr "상면 내벽의 최대 순간 속도 변화"

msgctxt "speed_wall_0_roofing label"
msgid "Top Surface Outer Wall Speed"
msgstr "상면 외벽 속도"

msgctxt "acceleration_roofing label"
msgid "Top Surface Skin Acceleration"
msgstr "상단 표면 스킨 가속도"

msgctxt "roofing_extruder_nr label"
msgid "Top Surface Skin Extruder"
msgstr "상단 표면 익스트루더"

msgctxt "roofing_material_flow label"
msgid "Top Surface Skin Flow"
msgstr "상단 표면 스킨 압출량"

msgctxt "jerk_roofing label"
msgid "Top Surface Skin Jerk"
msgstr "스킨 표면 Jerk"

msgctxt "roofing_layer_count label"
msgid "Top Surface Skin Layers"
msgstr "상단 표면 스킨 레이어"

msgctxt "roofing_angles label"
msgid "Top Surface Skin Line Directions"
msgstr "상단 표면 스킨 라인 방향"

msgctxt "roofing_line_width label"
msgid "Top Surface Skin Line Width"
msgstr "상단 표면 스킨 선 너비"

msgctxt "roofing_pattern label"
msgid "Top Surface Skin Pattern"
msgstr "탑 표면 스킨 패턴"

msgctxt "speed_roofing label"
msgid "Top Surface Skin Speed"
msgstr "상단 표면 스킨 속도"

msgctxt "top_thickness label"
msgid "Top Thickness"
msgstr "상단 두께"

msgctxt "max_skin_angle_for_expansion description"
msgid "Top and/or bottom surfaces of your object with an angle larger than this setting, won't have their top/bottom skin expanded. This avoids expanding the narrow skin areas that are created when the model surface has a near vertical slope. An angle of 0° is horizontal and will cause no skin to be expanded, while an angle of 90° is vertical and will cause all skin to be expanded."
msgstr "이 설정보다 큰 각도로 객체의 상단 및 또는 하단 표면은 위쪽/아래쪽 스킨이 확장되지 않습니다. 이렇게하면 모델 표면이 수직 경사가 거의 없을 때 생성되는 좁은 스킨 영역을 확장하지 않아도됩니다. 0도의 각도는 수평이며 스킨의 확장을 유발하지 않고, 90도의 각도는 수직이며 모든 스킨의 확장을 유발합니다."

msgctxt "top_bottom description"
msgid "Top/Bottom"
msgstr "위 / 아래"

msgctxt "top_bottom label"
msgid "Top/Bottom"
msgstr "위 / 아래"

msgctxt "acceleration_topbottom label"
msgid "Top/Bottom Acceleration"
msgstr "상단/하단 가속도"

msgctxt "top_bottom_extruder_nr label"
msgid "Top/Bottom Extruder"
msgstr "상단/하단 익스트루더"

msgctxt "skin_material_flow label"
msgid "Top/Bottom Flow"
msgstr "상단/하단 압출량"

msgctxt "jerk_topbottom label"
msgid "Top/Bottom Jerk"
msgstr "위/아래 Jerk"

msgctxt "skin_angles label"
msgid "Top/Bottom Line Directions"
msgstr "상단/하단 라인 길 방향"

msgctxt "skin_line_width label"
msgid "Top/Bottom Line Width"
msgstr "상단/하단 라인 폭"

msgctxt "top_bottom_pattern label"
msgid "Top/Bottom Pattern"
msgstr "상단/하단 패턴"

msgctxt "speed_topbottom label"
msgid "Top/Bottom Speed"
msgstr "상단/하단 속도"

msgctxt "top_bottom_thickness label"
msgid "Top/Bottom Thickness"
msgstr "상단/하단 두께"

msgctxt "support_type option buildplate"
msgid "Touching Buildplate"
msgstr "빌드 플레이트 위"

msgctxt "support_tower_diameter label"
msgid "Tower Diameter"
msgstr "타워 지름"

msgctxt "support_tower_roof_angle label"
msgid "Tower Roof Angle"
msgstr "타워 지붕 각도"

msgctxt "mesh_rotation_matrix description"
msgid "Transformation matrix to be applied to the model when loading it from file."
msgstr "파일로부터 로드 하는 경유, 모델에 적용될 변환 행렬입니다."

msgctxt "travel label"
msgid "Travel"
msgstr "이동"

msgctxt "acceleration_travel label"
msgid "Travel Acceleration"
msgstr "이동 가속"

msgctxt "travel_avoid_distance label"
msgid "Travel Avoid Distance"
msgstr "이동중 피하는 거리"

msgctxt "jerk_travel label"
msgid "Travel Jerk"
msgstr "이동 Jerk"

msgctxt "speed_travel label"
msgid "Travel Speed"
msgstr "이동 속도"

msgctxt "magic_mesh_surface_mode description"
msgid "Treat the model as a surface only, a volume, or volumes with loose surfaces. The normal print mode only prints enclosed volumes. \"Surface\" prints a single wall tracing the mesh surface with no infill and no top/bottom skin. \"Both\" prints enclosed volumes like normal and any remaining polygons as surfaces."
msgstr "모델을 표면만, 볼륨 또는 느슨한 표면이있는 볼륨으로 취급합니다. 일반 프린팅 모드는 볼륨만 프린팅합니다. \"표면\"은 아무런 내부채움없이 상단 / 하단 스킨없이 메쉬 표면을 추적하는 단일 벽을 프린팅합니다. \"둘 다\"는 정상 및 나머지 폴리곤과 같은 닫힌 볼륨을 서피스로 프린팅합니다."

msgctxt "support_structure option tree"
msgid "Tree"
msgstr "트리"

msgctxt "infill_pattern option trihexagon"
msgid "Tri-Hexagon"
msgstr "삼-육각형"

msgctxt "infill_pattern option triangles"
msgid "Triangles"
msgstr "삼각형"

msgctxt "support_bottom_pattern option triangles"
msgid "Triangles"
msgstr "삼각형"

msgctxt "support_interface_pattern option triangles"
msgid "Triangles"
msgstr "삼각형"

msgctxt "support_pattern option triangles"
msgid "Triangles"
msgstr "삼각형"

msgctxt "support_roof_pattern option triangles"
msgid "Triangles"
msgstr "삼각형"

msgctxt "support_tree_max_diameter label"
msgid "Trunk Diameter"
msgstr "트렁크 직경"

msgctxt "machine_gcode_flavor option UltiGCode"
msgid "Ultimaker 2"
msgstr "Ultimaker 2"

msgctxt "meshfix_union_all label"
msgid "Union Overlapping Volumes"
msgstr "유니언 오버랩 볼륨"

msgctxt "bridge_wall_min_length description"
msgid "Unsupported walls shorter than this will be printed using the normal wall settings. Longer unsupported walls will be printed using the bridge wall settings."
msgstr "이보다 짧은 벽은 일반 벽 설정을 사용하여 인쇄됩니다. 더 이상 지원되지 않는 벽은 브리지 벽 설정을 사용하여 인쇄됩니다."

msgctxt "adaptive_layer_height_enabled label"
msgid "Use Adaptive Layers"
msgstr "어댑티브 레이어 사용"

msgctxt "support_use_towers label"
msgid "Use Towers"
msgstr "타워 사용"

msgctxt "acceleration_travel_enabled description"
msgid "Use a separate acceleration rate for travel moves. If disabled, travel moves will use the acceleration value of the printed line at their destination."
msgstr "이동할 때 별도의 가속도를 사용합니다. 비활성화된 경우 이동 시 프린팅된 라인의 목적지 기준 가속도 값을 사용합니다."

msgctxt "jerk_travel_enabled description"
msgid "Use a separate jerk rate for travel moves. If disabled, travel moves will use the jerk value of the printed line at their destination."
msgstr "이동할 때 별도의 저크 속도를 사용합니다. 비활성화된 경우 이동 시 프린팅된 라인의 목적지 기준 저크 값을 사용합니다."

msgctxt "relative_extrusion description"
msgid "Use relative extrusion rather than absolute extrusion. Using relative E-steps makes for easier post-processing of the g-code. However, it's not supported by all printers and it may produce very slight deviations in the amount of deposited material compared to absolute E-steps. Irrespective of this setting, the extrusion mode will always be set to absolute before any g-code script is output."
msgstr "절대 돌출보다는 상대적 돌출을 사용합니다. 상대적인 E-steps을 사용하면 Gcode를 보다 쉽게 후 처리 할 수 있습니다. 그러나 모든 프린터에서 지원되는 것은 아니며 절대 E 단계와 비교할 때 출력된 재료의 양이 매우 약간 다를 수 있습니다. 이 설정과 관계없이 압출 모드는 Gcode 스크립트가 출력되기 전에 항상 절대 값으로 설정됩니다."

msgctxt "support_use_towers description"
msgid "Use specialized towers to support tiny overhang areas. These towers have a larger diameter than the region they support. Near the overhang the towers' diameter decreases, forming a roof."
msgstr "작은 오버행에 서포트를 생성하기 위해 특수한 타워를 사용. 이 타워들은 그들이 서포트하는 지역보다 더 큰 지름을 가지고 있습니다. 오버행 부근에서 타워의 직경이 감소하여 지붕을 형성합니다."

msgctxt "infill_mesh description"
msgid "Use this mesh to modify the infill of other meshes with which it overlaps. Replaces infill regions of other meshes with regions for this mesh. It's suggested to only print one Wall and no Top/Bottom Skin for this mesh."
msgstr "겹치는 다른 메쉬의 내부채움율을 수정합니다. 다른 메쉬의 내부채움 영역을 이 메쉬의 영역으로 대체합니다. 하나의 벽과 상단/바닥 스킨만을 프린팅하는 것이 추천합니다."

msgctxt "support_mesh description"
msgid "Use this mesh to specify support areas. This can be used to generate support structure."
msgstr "본 메시를 사용하여 서포트 영역을 지정하십시오. 이것은 서포트 구조를 생성하는 데 사용할 수 있습니다."

msgctxt "anti_overhang_mesh description"
msgid "Use this mesh to specify where no part of the model should be detected as overhang. This can be used to remove unwanted support structure."
msgstr "본 메쉬를 사용하여 모델에서 오버행부로 감지되지 않을 부분을 지정합니다. 이것은 원하지 않는 서포트 구조를 제거하는 데 사용될 수 있습니다."

msgctxt "z_seam_type option back"
msgid "User Specified"
msgstr "사용자 지정"

msgctxt "material_shrinkage_percentage_z label"
msgid "Vertical Scaling Factor Shrinkage Compensation"
msgstr "수직 확장 배율 수축 보정"

msgctxt "slicing_tolerance description"
msgid "Vertical tolerance in the sliced layers. The contours of a layer are normally generated by taking cross sections through the middle of each layer's thickness (Middle). Alternatively each layer can have the areas which fall inside of the volume throughout the entire thickness of the layer (Exclusive) or a layer has the areas which fall inside anywhere within the layer (Inclusive). Inclusive retains the most details, Exclusive makes for the best fit and Middle stays closest to the original surface."
msgstr "슬라이스 레이어의 수직 허용 오차입니다. 레이어의 윤곽선은 일반적으로 각 레이어의 두께 중간(중간)을 교차하는 부분을 기준으로 생성됩니다. 또는 각 레이어가 레이어의 높이 전체의 볼륨에 들어가는 영역(포함하지 않음)이 있거나 레이어 안의 어느 지점에 들어가는 영역(포함)이 있을 수 있습니다. 포함된 영역에서 가장 많은 디테일이 포함되고 포함되지 않은 영역을 통해 가장 맞게 만들 수 있으며 중간을 통해 원래 표면과 가장 유사하게 만들어냅니다."

msgctxt "material_bed_temp_wait label"
msgid "Wait for Build Plate Heatup"
msgstr "빌드 플레이트가 가열될 때까지 기다리십시오"

msgctxt "material_print_temp_wait label"
msgid "Wait for Nozzle Heatup"
msgstr "노즐이 가열될 때까지 기다리기"

msgctxt "acceleration_wall label"
msgid "Wall Acceleration"
msgstr "벽 가속도"

msgctxt "wall_distribution_count label"
msgid "Wall Distribution Count"
msgstr "벽 배포 개수"

msgctxt "wall_extruder_nr label"
msgid "Wall Extruder"
msgstr "벽 익스트루더"

msgctxt "wall_material_flow label"
msgid "Wall Flow"
msgstr "벽 압출량"

msgctxt "jerk_wall label"
msgid "Wall Jerk"
msgstr "벽 Jerk"

msgctxt "wall_line_count label"
msgid "Wall Line Count"
msgstr "벽 라인의 수"

msgctxt "wall_line_width label"
msgid "Wall Line Width"
msgstr "벽 선 너비"

msgctxt "inset_direction label"
msgid "Wall Ordering"
msgstr "벽 순서 지정"

msgctxt "speed_wall label"
msgid "Wall Speed"
msgstr "벽 속도"

msgctxt "wall_thickness label"
msgid "Wall Thickness"
msgstr "벽 두께"

msgctxt "wall_transition_length label"
msgid "Wall Transition Length"
msgstr "벽 전환 길이"

msgctxt "wall_transition_filter_distance label"
msgid "Wall Transitioning Filter Distance"
msgstr "벽 전환 필터 거리"

msgctxt "wall_transition_filter_deviation label"
msgid "Wall Transitioning Filter Margin"
msgstr "벽 전환 필터 여백"

msgctxt "wall_transition_angle label"
msgid "Wall Transitioning Threshold Angle"
msgstr "벽 전환 임계각"

msgctxt "shell label"
msgid "Walls"
msgstr "벽"

msgctxt "wall_overhang_angle description"
msgid "Walls that overhang more than this angle will be printed using overhanging wall settings. When the value is 90, no walls will be treated as overhanging. Overhang that gets supported by support will not be treated as overhang either."
msgstr "이 각도보다 놓은 오버행(경사면)의 벽은 오버행 벽 설정을 사용해 인쇄됩니다. 값이 90이면 오버행(경사면)으로 처리되는 벽이 없습니다. 서포트로 지지되는 오버행(경사면)도 오버행(경사면)으로 처리되지 않습니다."

msgctxt "meshfix_fluid_motion_enabled description"
msgid "When enabled tool paths are corrected for printers with smooth motion planners. Small movements that deviate from the general tool path direction are smoothed to improve fluid motions."
msgstr "활성화하면 부드러운 모션 플래너가 있는 프린터의 도구 경로가 수정됩니다. 일반적인 도구 경로 방향에서 벗어나는 작은 움직임이 평활화되어 플루이드 모션이 개선됩니다."

msgctxt "infill_enable_travel_optimization description"
msgid "When enabled, the order in which the infill lines are printed is optimized to reduce the distance travelled. The reduction in travel time achieved very much depends on the model being sliced, infill pattern, density, etc. Note that, for some models that have many small areas of infill, the time to slice the model may be greatly increased."
msgstr "활성화되면, 내부채움 라인 프린팅 순서가 최적화되어 이동 거리를 줄입니다. 이동 시간의 감소는 슬라이스되는 모델, 내부채움 패턴, 밀도 등에 따라 달라집니다. 작은 내부채움 영역이 많은 일부 모델의 경우, 모델을 슬라이스하는 시간이 상당히 늘어납니다."

msgctxt "support_fan_enable description"
msgid "When enabled, the print cooling fan speed is altered for the skin regions immediately above the support."
msgstr "활성화되면 서포트 바로 위의 스킨 영역에 대한 프린팅 냉각 팬 속도가 변경됩니다."

msgctxt "z_seam_relative description"
msgid "When enabled, the z seam coordinates are relative to each part's centre. When disabled, the coordinates define an absolute position on the build plate."
msgstr "활성화 된 경우 z 솔기 좌표는 각 부품의 중심을 기준으로합니다. 비활성화 된 경우 좌표는 빌드 플레이트의 절대 위치를 정의합니다."

msgctxt "retraction_combing_max_distance description"
msgid "When greater than zero, combing travel moves that are longer than this distance will use retraction. If set to zero, there is no maximum and combing moves will not use retraction."
msgstr "0보다 큰 경우 이 거리보다 긴 combing travel은 retraction을 사용합니다. 0으로 설정한 경우 최댓값이 없으며 combing travel은 retraction을 사용하지 않습니다."

msgctxt "hole_xy_offset_max_diameter description"
msgid "When greater than zero, the Hole Horizontal Expansion is gradually applied on small holes (small holes are expanded more). When set to zero the Hole Horizontal Expansion will be applied to all holes. Holes larger than the Hole Horizontal Expansion Max Diameter are not expanded."
msgstr "0보다 큰 값으로 설정하면 구멍 수평 확장이 작은 구멍에 점진적으로 적용되고(작은 구멍이 더 확장됨), 0으로 설정하면 구멍 수평 확장이 모든 구멍에 적용됩니다. 구멍 수평 확장 최대 직경보다 큰 구멍은 확장되지 않습니다."

msgctxt "hole_xy_offset description"
msgid "When greater than zero, the Hole Horizontal Expansion is the amount of offset applied to all holes in each layer. Positive values increase the size of the holes, negative values reduce the size of the holes. When this setting is enabled it can be further tuned with Hole Horizontal Expansion Max Diameter."
msgstr "0보다 크면 홀 수평 확장은 각 레이어의 모든 홀에 적용되는 오프셋의 양입니다. 양수 값은 홀의 크기를 늘리고 음수 값은 홀의 크기를 줄입니다. 이 설정을 활성화하면 홀 수평 확장 최대 직경을 사용하여 추가로 조정할 수 있습니다."

msgctxt "bridge_skin_material_flow description"
msgid "When printing bridge skin regions, the amount of material extruded is multiplied by this value."
msgstr "브릿지 스킨 영역을 프린팅할 때 압출 된 재료의 양에 이 값을 곱합니다."

msgctxt "bridge_wall_material_flow description"
msgid "When printing bridge walls, the amount of material extruded is multiplied by this value."
msgstr "브릿지 스킨 벽 영역을 프린팅할 때 압출 된 재료의 양에 이 값을 곱합니다."

msgctxt "bridge_skin_material_flow_2 description"
msgid "When printing the second bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "두번째 브릿지 스킨 영역을 프린팅할 때 압출 된 재료의 양에 이 값을 곱합니다."

msgctxt "bridge_skin_material_flow_3 description"
msgid "When printing the third bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "세번째 브릿지 스킨 영역을 프린팅할 때 압출 된 재료의 양에 이 값을 곱합니다."

msgctxt "cool_lift_head description"
msgid "When the minimum speed is hit because of minimum layer time, lift the head away from the print and wait the extra time until the minimum layer time is reached."
msgstr "최소 레이어 시간으로 인해 최소 속도에 도달하면 헤드를 출력물에서 들어 올려 최소 레이어 시간에 도달 할 때까지 시간을 기다립니다."

msgctxt "skin_no_small_gaps_heuristic description"
msgid "When the model has small vertical gaps of only a few layers, there should normally be skin around those layers in the narrow space. Enable this setting to not generate skin if the vertical gap is very small. This improves printing time and slicing time, but technically leaves infill exposed to the air."
msgstr "모델의 몇 가지 레이어에만 수직 간격이 작을 경우 보통 좁은 공간의 본 레이어 주위에도 스킨이 있어야 합니다. 수직 간격이 매우 작을 경우 스킨을 생성하지 않도록 이 설정을 활성화합니다. 이렇게 하면 프린팅 시간과 슬라이싱 시간은 개선되지만 기술적으로 내부채움이 공기 중에 노출된 상태로 남게 됩니다."

msgctxt "wall_transition_angle description"
msgid "When to create transitions between even and odd numbers of walls. A wedge shape with an angle greater than this setting will not have transitions and no walls will be printed in the center to fill the remaining space. Reducing this setting reduces the number and length of these center walls, but may leave gaps or overextrude."
msgstr "짝수 벽과 홀수 벽 사이에 전환을 생성할 때입니다. 이 설정보다 더 큰 각도의 웨지 모양은 전환이 없으며 나머지 공간을 채우기 위해 벽이 중앙에 프린트되지는 않습니다. 이 설정을 줄이면 이러한 중앙 벽의 수와 길이가 줄어들지만, 간격이 생기거나 과잉 압출될 수 있습니다."

msgctxt "wall_transition_length description"
msgid "When transitioning between different numbers of walls as the part becomes thinner, a certain amount of space is allotted to split or join the wall lines."
msgstr "부품이 얇아지면서 서로 다른 수의 벽 사이에서 전환될 때 벽 선을 분할하거나 결합하기 위해 일정 양의 공간이 할당됩니다."

msgctxt "wipe_hop_enable description"
msgid "When wiping, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "와이프할 때, 노즐과 출력물 사이에 간격이 생기도록 빌드 플레이트를 내립니다. 이동 중에 노즐이 출력물에 부딪히는 것을 방지하여 제조판에서 출력물을 칠 가능성을 줄입니다."

msgctxt "retraction_hop_enabled description"
msgid "Whenever a retraction is done, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "리트렉션이 일어날 때마다 빌드 플레이트가 낮아져 노즐과 출력물 사이에 여유 공간이 생깁니다. 이동 중에 노즐이 출력물에 부딪치지 않도록 합니다."

msgctxt "support_xy_overrides_z description"
msgid "Whether the Support X/Y Distance overrides the Support Z Distance or vice versa. When X/Y overrides Z the X/Y distance can push away the support from the model, influencing the actual Z distance to the overhang. We can disable this by not applying the X/Y distance around overhangs."
msgstr "서포트 X/Y 거리가 서포트 Z 거리를 무시하는지 여부를 나타냅니다. X/Y가 Z를 오버라이드하면 X/Y 거리는 모델에서 서포트점을 밀어내어 돌출부까지의 실제 Z 거리에 영향을 줄 수 있습니다. 오버행 주위에 X/Y 거리를 적용하지 않음으로써이 기능을 비활성화 할 수 있습니다."

msgctxt "machine_center_is_zero description"
msgid "Whether the X/Y coordinates of the zero position of the printer is at the center of the printable area."
msgstr "프린터의 0 위치의 X/Y 좌표가 프린팅 가능 영역의 중앙에 있는지 여부."

msgctxt "machine_endstop_positive_direction_x description"
msgid "Whether the endstop of the X axis is in the positive direction (high X coordinate) or negative (low X coordinate)."
msgstr "X 축의 엔드 스톱이 양의 방향 (높은 X 좌표) 또는 음의 (낮은 X 좌표)인지 여부."

msgctxt "machine_endstop_positive_direction_y description"
msgid "Whether the endstop of the Y axis is in the positive direction (high Y coordinate) or negative (low Y coordinate)."
msgstr "Y 축의 엔드 스톱이 양의 방향 (높은 Y 좌표) 또는 음의 (낮은 Y 좌표)인지 여부."

msgctxt "machine_endstop_positive_direction_z description"
msgid "Whether the endstop of the Z axis is in the positive direction (high Z coordinate) or negative (low Z coordinate)."
msgstr "Z 축의 엔드 스톱이 양의 방향 (높은 Z 좌표) 또는 음의 (낮은 Z 좌표)인지 여부."

msgctxt "machine_extruders_share_heater description"
msgid "Whether the extruders share a single heater rather than each extruder having its own heater."
msgstr "압출기가 자체 히터를 가지고 있지 않고 단일 히터를 공유하는지에 대한 여부."

msgctxt "machine_extruders_share_nozzle description"
msgid "Whether the extruders share a single nozzle rather than each extruder having its own nozzle. When set to true, it is expected that the printer-start gcode script properly sets up all extruders in an initial retraction state that is known and mutually compatible (either zero or one filament not retracted); in that case the initial retraction status is described, per extruder, by the 'machine_extruders_shared_nozzle_initial_retraction' parameter."
msgstr "익스트루더가 자체 노즐을 가지고 있지 않고 단일 노즐을 공유하는지에 대한 여부. True로 설정하면 프린터 시동 gcode 스크립트가 알려진 상호 호환 가능한 초기 수축 상태(0 또는 1개의 필라멘트가 수축되지 않음)에서 모든 익스트루더를 적절하게 설정해야 합니다. 이 경우 초기 수축 상태는 'machine_extruders_shared_nozzle_initial_retraction' 매개 변수에 의해 익스트루더마다 표시됩니다."

msgctxt "machine_heated_bed description"
msgid "Whether the machine has a heated build plate present."
msgstr "기기에 히팅 빌드 플레이트가 있는지 여부."

msgctxt "machine_heated_build_volume description"
msgid "Whether the machine is able to stabilize the build volume temperature."
msgstr "기기의 빌드 볼륨 온도 안정화 지원 여부입니다."

msgctxt "center_object description"
msgid "Whether to center the object on the middle of the build platform (0,0), instead of using the coordinate system in which the object was saved."
msgstr "객체가 저장된 좌표계를 사용하는 대신 빌드 플랫폼 중간 (0,0)를 중심으로 할지 여부."

msgctxt "machine_nozzle_temp_enabled description"
msgid "Whether to control temperature from Cura. Turn this off to control nozzle temperature from outside of Cura."
msgstr "Cura에서 온도를 제어할지 여부. Cura 외부에서 노즐 온도를 제어하려면 이 기능을 끄십시오."

msgctxt "material_bed_temp_prepend description"
msgid "Whether to include build plate temperature commands at the start of the gcode. When the start_gcode already contains build plate temperature commands Cura frontend will automatically disable this setting."
msgstr "gcode가 시작될 때 빌드 플레이트 온도 명령을 포함할지 여부. start_gcode에 빌드 플레이트 온도 명령이 이미 있으면 Cura는 이 설정을 자동으로 비활성화 합니다."

msgctxt "material_print_temp_prepend description"
msgid "Whether to include nozzle temperature commands at the start of the gcode. When the start_gcode already contains nozzle temperature commands Cura frontend will automatically disable this setting."
msgstr "gcode의 시작 부분에 노즐 온도 명령을 포함할지 여부. start_gcode에 이미 노즐 온도 명령이 포함되어있을 때 Cura는 이 설정을 자동으로 비활성화 합니다."

msgctxt "clean_between_layers description"
msgid "Whether to include nozzle wipe G-Code between layers (maximum 1 per layer). Enabling this setting could influence behavior of retract at layer change. Please use Wipe Retraction settings to control retraction at layers where the wipe script will be working."
msgstr "노즐 와이퍼 작동 G-코드를 레이어 사이에 포함할지 여부(레이어당 최대 1개) 이 설정을 활성화하면 레이어 변경 시 리트렉트 동작에 영향을 미칠 수 있습니다. 와이프 스크립트가 작동할 레이어의 감속을 제어하려면 와이프 리트랙션 설정을 사용하십시오."

msgctxt "material_bed_temp_wait description"
msgid "Whether to insert a command to wait until the build plate temperature is reached at the start."
msgstr "시작 시, 빌드 플레이트가 가열될 때까지 대기하라는 명령을 삽입할지 여부."

msgctxt "prime_blob_enable description"
msgid "Whether to prime the filament with a blob before printing. Turning this setting on will ensure that the extruder will have material ready at the nozzle before printing. Printing Brim or Skirt can act like priming too, in which case turning this setting off saves some time."
msgstr "프린팅하기 전에 프라이밍할지 여부. 이 설정을 켜면 프린팅하기 전에 익스트루더가 노즐에서 재료를 준비 할 수 있습니다. 브림 또는 스커트 프린팅는 프라이밍처럼 작동 할 수 있습니다.이 경우이 설정을 해제하면 시간이 절약됩니다."

msgctxt "print_sequence description"
msgid "Whether to print all models one layer at a time or to wait for one model to finish, before moving on to the next. One at a time mode is possible if a) only one extruder is enabled and b) all models are separated in such a way that the whole print head can move in between and all models are lower than the distance between the nozzle and the X/Y axes."
msgstr "모든 모델을 한 번에 한 레이어씩 프린팅할 것인지, 아니면 한 모델이 완료될 때까지 기다릴 것인지, 다음 단계로 넘어가기 전에 대한 여부 a) 한 번에 하나의 압출기만 활성화하고 b) 모든 모델은 전체 프린트 헤드가 이동할 수 있는 방식으로 분리되며 모든 모델은 노즐과 X/Y 축 사이의 거리보다 낮습니다."

msgctxt "machine_show_variants description"
msgid "Whether to show the different variants of this machine, which are described in separate json files."
msgstr "별도의 json 파일에 설명된 기기의 다양한 세부 설정을 표시할지 여부."

msgctxt "machine_firmware_retract description"
msgid "Whether to use firmware retract commands (G10/G11) instead of using the E property in G1 commands to retract the material."
msgstr "재료를 리트렉션하는 G1 명령어에서 E 속성을 사용하는 대신 펌웨어 리트렉션 명령어(G10/G11)를 사용할 지 여부."

msgctxt "material_print_temp_wait description"
msgid "Whether to wait until the nozzle temperature is reached at the start."
msgstr "시작 시, 노즐이 가열될 때까지 대기할지 여부."

msgctxt "infill_line_width description"
msgid "Width of a single infill line."
msgstr "단일 내부채움 라인의 너비."

msgctxt "support_interface_line_width description"
msgid "Width of a single line of support roof or floor."
msgstr "서포트의 지붕, 바닥의 폭."

msgctxt "roofing_line_width description"
msgid "Width of a single line of the areas at the top of the print."
msgstr "프린팅 상단 부분의 한 줄 너비."

msgctxt "line_width description"
msgid "Width of a single line. Generally, the width of each line should correspond to the width of the nozzle. However, slightly reducing this value could produce better prints."
msgstr "한 줄의 두께. 일반적으로 각 라인의 너비는 노즐 폭과 일치해야합니다. 그러나 이 값을 약간 줄이면 더 나은 인쇄를 할 수 있습니다."

msgctxt "prime_tower_line_width description"
msgid "Width of a single prime tower line."
msgstr "단일 주요 타워 라인의 폭."

msgctxt "skirt_brim_line_width description"
msgid "Width of a single skirt or brim line."
msgstr "단일 스커트 또는 브림의 너비."

msgctxt "support_bottom_line_width description"
msgid "Width of a single support floor line."
msgstr "단일 서포트 플로어 라인의 폭."

msgctxt "support_roof_line_width description"
msgid "Width of a single support roof line."
msgstr "단일 서포트 지붕 라인 폭."

msgctxt "support_line_width description"
msgid "Width of a single support structure line."
msgstr "단일 서포트 구조 선의 폭입니다."

msgctxt "skin_line_width description"
msgid "Width of a single top/bottom line."
msgstr "한 라인의 단일 위쪽/아래쪽 선의 너비입니다."

msgctxt "wall_line_width_x description"
msgid "Width of a single wall line for all wall lines except the outermost one."
msgstr "가장 바깥 쪽 벽 선을 제외한 모든 벽 선에 대해 단일 벽 선의 폭입니다."

msgctxt "wall_line_width description"
msgid "Width of a single wall line."
msgstr "단일 벽 선의 너비입니다."

msgctxt "raft_base_line_width description"
msgid "Width of the lines in the base raft layer. These should be thick lines to assist in build plate adhesion."
msgstr "기본 래프트 층에있는 선의 너비. 이것은 빌드 플레이트 접착을 돕기 위해 두꺼운 선 이어야 합니다."

msgctxt "raft_interface_line_width description"
msgid "Width of the lines in the middle raft layer. Making the second layer extrude more causes the lines to stick to the build plate."
msgstr "중간 래프트 층의 선폭. 두 번째 레이어를 더 돌출 시키면 선이 빌드 플레이트에 달라 붙습니다."

msgctxt "raft_surface_line_width description"
msgid "Width of the lines in the top surface of the raft. These can be thin lines so that the top of the raft becomes smooth."
msgstr "래프트의 윗면에 있는 선의 폭. 래프트의 상단이 매끄럽도록 얇은 선으로 구성 될 수 있습니다."

msgctxt "wall_line_width_0 description"
msgid "Width of the outermost wall line. By lowering this value, higher levels of detail can be printed."
msgstr "가장 바깥 쪽 벽 선의 너비. 이 값을 낮춤으로써 높은 수준의 디테일을 프린팅 할 수 있습니다."

msgctxt "min_bead_width description"
msgid "Width of the wall that will replace thin features (according to the Minimum Feature Size) of the model. If the Minimum Wall Line Width is thinner than the thickness of the feature, the wall will become as thick as the feature itself."
msgstr "모델의 (최소 피처 크기에 따라) 얇은 피처를 대체할 벽의 너비 최소 벽 선 너비가 피처의 두께보다 더 얇다면 벽은 피처 자체만큼 두꺼워집니다."

msgctxt "wipe_brush_pos_x label"
msgid "Wipe Brush X Position"
msgstr "와이프 브러시 X 위치"

msgctxt "wipe_hop_speed label"
msgid "Wipe Hop Speed"
msgstr "와이프 홉 속도"

msgctxt "prime_tower_wipe_enabled label"
msgid "Wipe Inactive Nozzle on Prime Tower"
msgstr "프라임 타워에서 비활성 노즐 닦기"

msgctxt "wipe_move_distance label"
msgid "Wipe Move Distance"
msgstr "와이프 이동 거리"

msgctxt "clean_between_layers label"
msgid "Wipe Nozzle Between Layers"
msgstr "레이어 사이의 와이프 노즐"

msgctxt "wipe_pause label"
msgid "Wipe Pause"
msgstr "와이프 일시 정지"

msgctxt "wipe_repeat_count label"
msgid "Wipe Repeat Count"
msgstr "와이프 반복 횟수"

msgctxt "wipe_retraction_amount label"
msgid "Wipe Retraction Distance"
msgstr "와이프 리트랙션 거리"

msgctxt "wipe_retraction_enable label"
msgid "Wipe Retraction Enable"
msgstr "와이프 리트랙션 활성화"

msgctxt "wipe_retraction_extra_prime_amount label"
msgid "Wipe Retraction Extra Prime Amount"
msgstr "와이프 리트랙션 추가 초기 양"

msgctxt "wipe_retraction_prime_speed label"
msgid "Wipe Retraction Prime Speed"
msgstr "와이프 리트렉션 초기 속도"

msgctxt "wipe_retraction_retract_speed label"
msgid "Wipe Retraction Retract Speed"
msgstr "와이프 리트랙션 리트렉트 속도"

msgctxt "wipe_retraction_speed label"
msgid "Wipe Retraction Speed"
msgstr "와이프 리트랙션 속도"

msgctxt "wipe_hop_enable label"
msgid "Wipe Z Hop"
msgstr "와이프 Z 홉"

msgctxt "wipe_hop_amount label"
msgid "Wipe Z Hop Height"
msgstr "화이프 Z 홉 높이"

msgctxt "retraction_combing option infill"
msgid "Within Infill"
msgstr "내부채움 내"

msgctxt "machine_always_write_active_tool description"
msgid "Write active tool after sending temp commands to inactive tool. Required for Dual Extruder printing with Smoothie or other firmware with modal tool commands."
msgstr "임시 명령을 비활성 도구로 전송한 후 활성 도구를 작성하십시오. Smoothie 또는 모달 도구 명령어를 사용하는 다른 펌웨어를 사용해 프린팅하는 듀얼 압출기용 프린팅에 필요합니다."

msgctxt "machine_endstop_positive_direction_x label"
msgid "X Endstop in Positive Direction"
msgstr "양의 방향 X 엔드 스톱"

msgctxt "wipe_brush_pos_x description"
msgid "X location where wipe script will start."
msgstr "와이프 스크립트가 시작되는 X 위치입니다."

msgctxt "support_xy_overrides_z option xy_overrides_z"
msgid "X/Y overrides Z"
msgstr "X/Y가 Z를 무시합니다"

msgctxt "machine_endstop_positive_direction_y label"
msgid "Y Endstop in Positive Direction"
msgstr "양의 방향 Y 엔드 스톱"

msgctxt "machine_endstop_positive_direction_z label"
msgid "Z Endstop in Positive Direction"
msgstr "양의 방향 Z 엔드 스톱"

msgctxt "retraction_hop_after_extruder_switch label"
msgid "Z Hop After Extruder Switch"
msgstr "익스트루더 스위치 후 Z 홉"

msgctxt "retraction_hop_after_extruder_switch_height label"
msgid "Z Hop After Extruder Switch Height"
msgstr "익스트루더 스위치 높이 후 Z 홉"

msgctxt "retraction_hop label"
msgid "Z Hop Height"
msgstr "Z 홉 높이"

msgctxt "retraction_hop_only_when_collides label"
msgid "Z Hop Only Over Printed Parts"
msgstr "프린팅 된 부분에만 Z Hop"

msgctxt "speed_z_hop label"
msgid "Z Hop Speed"
msgstr "Z 홉 속도"

msgctxt "retraction_hop_enabled label"
msgid "Z Hop When Retracted"
msgstr "리트렉션했을 때의 Z Hop"

msgctxt "z_seam_type label"
msgid "Z Seam Alignment"
msgstr "Z 솔기 정렬"

msgctxt "z_seam_position label"
msgid "Z Seam Position"
msgstr "Z 경계 위치"

msgctxt "z_seam_relative label"
msgid "Z Seam Relative"
msgstr "상대적 Z 솔기"

msgctxt "z_seam_x label"
msgid "Z Seam X"
msgstr "Z 솔기 X"

msgctxt "z_seam_y label"
msgid "Z Seam Y"
msgstr "Z 솔기 Y"

msgctxt "support_xy_overrides_z option z_overrides_xy"
msgid "Z overrides X/Y"
msgstr "Z가 X/Y를 무시합니다"

msgctxt "infill_pattern option zigzag"
msgid "Zig Zag"
msgstr "지그재그"

msgctxt "ironing_pattern option zigzag"
msgid "Zig Zag"
msgstr "지그재그"

msgctxt "roofing_pattern option zigzag"
msgid "Zig Zag"
msgstr "지그재그"

msgctxt "support_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "지그재그"

msgctxt "support_interface_pattern option zigzag"
msgid "Zig Zag"
msgstr "지그재그"

msgctxt "support_pattern option zigzag"
msgid "Zig Zag"
msgstr "지그재그"

msgctxt "support_roof_pattern option zigzag"
msgid "Zig Zag"
msgstr "지그재그"

msgctxt "top_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "지그재그"

msgctxt "top_bottom_pattern_0 option zigzag"
msgid "Zig Zag"
msgstr "지그재그"

msgctxt "travel description"
msgid "travel"
msgstr "이동"

msgctxt "cool_during_extruder_switch description"
msgid "<html>Whether to activate the cooling fans during a nozzle switch. This can help reducing oozing by cooling the nozzle faster:<ul><li><b>Unchanged:</b> keep the fans as they were previously</li><li><b>Only last extruder:</b> turn on the fan of the last used extruder, but turn the others off (if any). This is useful if you have completely separate extruders.</li><li><b>All fans:</b> turn on all fans during nozzle switch. This is useful if you have a single cooling fan, or multiple fans that stay close to each other.</li></ul></html>"
msgstr "<html>노즐 전환 중 냉각 팬의 활성화 여부를 선택합니다. 노즐을 더 빨리 냉각시켜 흘러내림을 줄일 수 있습니다:<ul><li><b>변경 없음:</b> 팬을 이전 상태와 같이 유지합니다</li><li><b>마지막 압출기만:</b> 마지막으로 사용한 압출기의 팬을 켜고, 나머지 팬이 있을 경우 모두 끕니다. 완전한 별도의 압출기가 있는 경우 유용합니다.</li><li><b>모든 팬:</b> 노즐 전환 중 모든 팬을 켭니다. 냉각팬이 1개만 있거나, 여러 개의 팬이 서로 가까이 있는 경우 유용합니다.</li></ul></html>"

msgctxt "cool_during_extruder_switch option all_fans"
msgid "All fans"
msgstr "모든 팬"

msgctxt "cool_during_extruder_switch label"
msgid "Cooling during extruder switch"
msgstr "압출기 전환 중 냉각"

msgctxt "support_z_seam_away_from_model description"
msgid "Manage the spatial relationship between the z seam of the support structure and the actual 3D model. This control is crucial as it allows users to ensure the seamless removal of support structures post-printing, without inflicting damage or leaving marks on the printed model."
msgstr "서포트 구조물의 Z 심과 실제 3D 모델 간의 공간 관계를 관리합니다. 이 컨트롤은 인쇄 결과 모델에 손상이나 자국을 남기지 않고 인쇄 후 서포트 구조물을 원활히 제거할 수 있도록 해주므로 매우 중요합니다."

msgctxt "support_z_seam_min_distance label"
msgid "Min Z Seam Distance from Model"
msgstr "모델과의 최소 Z 심 거리"

msgctxt "support_infill_density_multiplier_initial_layer description"
msgid "Multiplier for the infill on the initial layers of the support. Increasing this may help for bed adhesion."
msgstr "서포트의 초기 레이어에서 인필을 위한 압출 배율입니다. 이 값을 높이면 베드 밀착에 도움이 될 수 있습니다."

msgctxt "cool_during_extruder_switch option only_last_extruder"
msgid "Only last extruder"
msgstr "마지막 압출기만"

msgctxt "z_seam_on_vertex description"
msgid "Place the z-seam on a polygon vertex. Switching this off can place the seam between vertices as well. (Keep in mind that this won't override the restrictions on placing the seam on an unsupported overhang.)"
msgstr "다각형 꼭지점에 Z 심을 배치합니다. 이 기능을 끄면 정점 사이에도 심을 배치할 수 있습니다. (단, 이 경우 지원되지 않는 오버행에 심을 배치하는 데 대한 제약을 무시하지는 않는다는 점에 유의하세요)"

msgctxt "prime_tower_min_shell_thickness label"
msgid "Prime Tower Minimum Shell Thickness"
msgstr "프라임 타워 최소 셸 두께"

msgctxt "raft_base_flow label"
msgid "Raft Base Flow"
msgstr "래프트 기본 흐름"

msgctxt "raft_base_infill_overlap_mm label"
msgid "Raft Base Infill Overlap"
msgstr "래프트 베이스 인필 오버랩"

msgctxt "raft_base_infill_overlap label"
msgid "Raft Base Infill Overlap Percentage"
msgstr "래프트 베이스 인필 오버랩 백분율"

msgctxt "raft_flow label"
msgid "Raft Flow"
msgstr "래프트 흐름"

msgctxt "raft_interface_flow label"
msgid "Raft Interface Flow"
msgstr "래프트 인터페이스 흐름"

msgctxt "raft_interface_infill_overlap_mm label"
msgid "Raft Interface Infill Overlap"
msgstr "래프트 인터페이스 인필 오버랩"

msgctxt "raft_interface_infill_overlap label"
msgid "Raft Interface Infill Overlap Percentage"
msgstr "래프트 인터페이스 인필 오버랩 백분율"

msgctxt "raft_interface_z_offset label"
msgid "Raft Interface Z Offset"
msgstr "래프트 인터페이스 Z 오프셋"

msgctxt "raft_surface_flow label"
msgid "Raft Surface Flow"
msgstr "래프트 서피스 흐름"

msgctxt "raft_surface_infill_overlap_mm label"
msgid "Raft Surface Infill Overlap"
msgstr "래프트 서피스 인필 오버랩"

msgctxt "raft_surface_infill_overlap label"
msgid "Raft Surface Infill Overlap Percentage"
msgstr "래프트 서피스 인필 오버랩 백분율"

msgctxt "raft_surface_z_offset label"
msgid "Raft Surface Z Offset"
msgstr "래프트 서피스 Z 오프셋"

msgctxt "seam_overhang_angle label"
msgid "Seam Overhanging Wall Angle"
msgstr "심 오버행잉 월 각도"

msgctxt "support_infill_density_multiplier_initial_layer label"
msgid "Support Infill Density Multiplier Initial Layer"
msgstr "서포트 인필 밀도 압출 배율 초기 레이어"

msgctxt "support_z_seam_away_from_model label"
msgid "Support Z Seam Away from Model"
msgstr "모델과 떨어진 서포트 Z 심"

msgctxt "raft_base_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft base printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "래프트 베이스 인쇄 시 일반 압출 라인 대비 압출할 재료의 양입니다. 유동이 증가하면 접착력 및 래프트 구조 강도가 향상될 수 있습니다."

msgctxt "raft_interface_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft interface printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "래프트 인터페이스 인쇄 시 일반 압출 라인 대비 압출할 재료의 양입니다. 유동이 증가하면 접착력 및 래프트 구조 강도가 향상될 수 있습니다."

msgctxt "raft_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "일반 압출 라인 대비 래프트 인쇄 중 압출할 재료의 양입니다. 유동이 증가하면 접착력 및 래프트 구조 강도가 향상될 수 있습니다."

msgctxt "raft_surface_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft surface printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "래프트 표면 인쇄 시 일반 압출 라인 대비 압출할 재료의 양입니다. 유동이 증가하면 접착력 및 래프트 구조 강도가 향상될 수 있습니다."

msgctxt "raft_base_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft base, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "래프트 베이스의 인필과 월 사이의 겹침 정도(인필 라인 너비의 백분율)입니다. 약간의 겹침이 있을 경우 월이 인필에 보다 단단히 연결될 수 있습니다."

msgctxt "raft_base_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft base. A slight overlap allows the walls to connect firmly to the infill."
msgstr "래프트 베이스의 인필과 월 사이의 겹침 정도입니다. 약간의 겹침이 있을 경우 월이 인필에 보다 단단히 연결될 수 있습니다."

msgctxt "raft_interface_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft interface, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "래프트 인터페이스의 인필과 월 사이의 겹침 정도(인필 라인 너비의 백분율)입니다. 약간의 겹침이 있을 경우 월이 인필에 보다 단단히 연결될 수 있습니다."

msgctxt "raft_interface_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft interface. A slight overlap allows the walls to connect firmly to the infill."
msgstr "래프트 인터페이스의 인필과 월 사이의 겹침 정도입니다. 약간의 겹침이 있을 경우 월이 인필에 보다 단단히 연결될 수 있습니다."

msgctxt "raft_surface_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft surface, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "래프트 표면의 인필과 월 사이의 겹침 정도(인필 라인 너비의 백분율)입니다. 약간의 겹침이 있을 경우 월이 인필에 보다 단단히 연결될 수 있습니다."

msgctxt "raft_surface_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft surface. A slight overlap allows the walls to connect firmly to the infill."
msgstr "래프트 표면의 인필과 월 사이의 겹침 정도입니다. 약간의 겹침이 있을 경우 월이 인필에 보다 단단히 연결될 수 있습니다."

msgctxt "support_z_seam_min_distance description"
msgid "The distance between the model and its support structure at the z-axis seam."
msgstr "Z축 심에서 모델과 서포트 구조물 사이의 거리입니다."

msgctxt "prime_tower_min_shell_thickness description"
msgid "The minimum thickness of the prime tower shell. You may increase it to make the prime tower stronger."
msgstr "프라임 타워 셸의 최소 두께입니다. 이 값을 늘림으로써 프라임 타워의 강도를 높일 수 있습니다."

msgctxt "seam_overhang_angle description"
msgid "Try to prevent seams on walls that overhang more than this angle. When the value is 90, no walls will be treated as overhanging."
msgstr "이 각도보다 오버행이 더 큰 월에는 이음새가 생기지 않도록 해야 합니다. 값이 90이면 어떠한 월도 오버행잉으로 처리되지 않습니다."

msgctxt "cool_during_extruder_switch option unchanged"
msgid "Unchanged"
msgstr "변경 없음"

msgctxt "raft_interface_z_offset description"
msgid "When printing the first layer of the raft interface, translate by this offset to customize the adhesion between base and interface. A negative offset should improve the adhesion."
msgstr "래프트 인터페이스의 첫 번째 레이어 인쇄 시, 해당 오프셋으로 변환하여 베이스와 인터페이스 사이의 접착력을 지정할 수 있습니다. 오프셋이 음수일 경우 접착력 향상을 기대할 수 있습니다."

msgctxt "raft_surface_z_offset description"
msgid "When printing the first layer of the raft surface, translate by this offset to customize the adhesion between interface and surface. A negative offset should improve the adhesion."
msgstr "래프트 서피스의 첫 번째 레이어를 인쇄 시, 해당 오프셋으로 변환하여 인터페이스와 표면 사이의 접착력을 지정할 수 있습니다. 오프셋이 음수일 경우 접착력 향상을 기대할 수 있습니다."

msgctxt "z_seam_on_vertex label"
msgid "Z Seam On Vertex"
msgstr "버텍스 상의 Z 심"

msgctxt "extra_infill_lines_to_support_skins description"
msgid "Add extra lines into the infill pattern to support skins above.   This option prevents holes or plastic blobs that sometime show in complex shaped skins due to the infill below not correctly supporting the skin layer being printed above.  'Walls' supports just the outlines of the skin, whereas 'Walls and Lines' also supports the ends of the lines that make up the skin."
msgstr "위 스킨을 지지하기 위해 내부채움 패턴에 선을 추가로 더합니다. 이 옵션은 아래의 내부채움이 위에 출력되는 스킨 레이어를 제대로 지지하지 못해 복잡한 모양의 스킨에 구멍이나 플라스틱 방울이 생기는 것을 방지합니다. '벽'은 스킨의 윤곽선만 지지하는 반면 '벽 및 선'은 스킨을 이루는 선의 끝부분도 지지합니다."

msgctxt "build_fan_full_at_height label"
msgid "Build Fan Speed at Height"
msgstr "높이에서 팬 속도 설정"

msgctxt "build_fan_full_layer label"
msgid "Build Fan Speed at Layer"
msgstr "레이어에서 팬 속도 설정"

msgctxt "build_volume_fan_nr label"
msgid "Build volume fan number"
msgstr "빌드 볼륨 팬 번호"

msgctxt "scarf_split_distance description"
msgid "Determines the length of each step in the flow change when extruding along the scarf seam. A smaller distance will result in a more precise but also more complex G-code."
msgstr "스카프 솔기를 따라 압출할 때 흐름 변화에서 각 단계의 길이를 결정합니다. 거리가 짧을수록 더 정밀하지만 G코드도 복잡해집니다."

msgctxt "scarf_joint_seam_length description"
msgid "Determines the length of the scarf seam, a seam type that should make the Z seam less visible. Must be higher than 0 to be effective."
msgstr "Z 솔기를 덜 보이게 하는 솔기 유형인 스카프 솔기의 길이를 결정합니다. 0보다 커야 효과적입니다."

msgctxt "gradual_flow_discretisation_step_size description"
msgid "Duration of each step in the gradual flow change"
msgstr "점진적 흐름 변경에서 각 단계의 지속 시간"

msgctxt "gradual_flow_enabled description"
msgid "Enable gradual flow changes. When enabled, the flow is gradually increased/decreased to the target flow. This is useful for printers with a bowden tube where the flow is not immediately changed when the extruder motor starts/stops."
msgstr "점진적 흐름 변경을 활성화합니다. 활성화하면 흐름이 목표 흐름까지 점진적으로 증가/감소합니다. 이는 압출기 모터가 시작/정지될 때 흐름이 즉시 변경되지 않는 보우덴 튜브가 있는 프린터에 유용합니다."

msgctxt "extra_infill_lines_to_support_skins label"
msgid "Extra Infill Lines To Support Skins"
msgstr "스킨을 지지하는 추가 내부채움 선"

msgctxt "reset_flow_duration description"
msgid "For any travel move longer than this value, the material flow is reset to the paths target flow"
msgstr "이 값보다 긴 이동에 대해서는 재료 흐름이 경로 목표 흐름으로 재설정됩니다"

msgctxt "gradual_flow_discretisation_step_size label"
msgid "Gradual flow discretisation step size"
msgstr "점진적 흐름 이산화 단계 크기"

msgctxt "gradual_flow_enabled label"
msgid "Gradual flow enabled"
msgstr "점진적 흐름 활성화됨"

msgctxt "max_flow_acceleration label"
msgid "Gradual flow max acceleration"
msgstr "점진적 흐름 최대 가속"

msgctxt "layer_0_max_flow_acceleration label"
msgid "Initial layer max flow acceleration"
msgstr "초기 레이어 최대 흐름 가속"

msgctxt "max_flow_acceleration description"
msgid "Maximum acceleration for gradual flow changes"
msgstr "점진적 흐름 변경에 대한 최대 가속"

msgctxt "layer_0_max_flow_acceleration description"
msgid "Minimum speed for gradual flow changes for the first layer"
msgstr "첫 번째 레이어의 점진적 흐름 변경에 대한 최소 속도"

msgctxt "extra_infill_lines_to_support_skins option none"
msgid "None"
msgstr "없음"

msgctxt "wall_0_acceleration label"
msgid "Outer Wall Acceleration"
msgstr "외벽 가속"

msgctxt "wall_0_deceleration label"
msgid "Outer Wall Deceleration"
msgstr "외벽 감속"

msgctxt "wall_0_end_speed_ratio label"
msgid "Outer Wall End Speed Ratio"
msgstr "외벽 종료 속도 비율"

msgctxt "wall_0_speed_split_distance label"
msgid "Outer Wall Speed Split Distance"
msgstr "외벽 속도 분할 거리"

msgctxt "wall_0_start_speed_ratio label"
msgid "Outer Wall Start Speed Ratio"
msgstr "외벽 시작 속도 비율"

msgctxt "reset_flow_duration label"
msgid "Reset flow duration"
msgstr "흐름 지속 시간 재설정"

msgctxt "scarf_joint_seam_length label"
msgid "Scarf Seam Length"
msgstr "스카프 솔기 길이"

msgctxt "scarf_joint_seam_start_height_ratio label"
msgid "Scarf Seam Start Height"
msgstr "스카프 솔기 시작 높이"

msgctxt "scarf_split_distance label"
msgid "Scarf Seam Step Length"
msgstr "스카프 솔기 단계 길이"

msgctxt "build_fan_full_at_height description"
msgid "The height at which the fans spin on regular fan speed. At the layers below the fan speed gradually increases from Initial Fan Speed to Regular Fan Speed."
msgstr "일반 팬 속도에서 팬이 회전하는 높이입니다. 아래 레이어에서는 팬 속도가 초기 팬 속도에서 일반 팬 속도로 점차 증가합니다."

msgctxt "build_fan_full_layer description"
msgid "The layer at which the build fans spin on full fan speed. This value is calculated and rounded to a whole number."
msgstr "빌드 팬이 최대 팬 속도로 회전하는 레이어입니다. 이 값은 계산되어 정수로 반올림됩니다."

msgctxt "build_volume_fan_nr description"
msgid "The number of the fan that cools the build volume. If this is set to 0, it's means that there is no build volume fan"
msgstr "빌드 볼륨을 냉각하는 팬의 수입니다. 0으로 설정하면 빌드 볼륨 팬이 없는 것을 나타냅니다."

msgctxt "scarf_joint_seam_start_height_ratio description"
msgid "The ratio of the selected layer height at which the scarf seam will begin. A lower number will result in a larger seam height. Must be lower than 100 to be effective."
msgstr "스카프 솔기가 시작되는 선택한 레이어 높이의 비율입니다. 숫자가 낮을수록 솔기 높이가 커집니다. 100보다 낮아야 효과적입니다."

msgctxt "wall_0_acceleration description"
msgid "This is the acceleration with which to reach the top speed when printing an outer wall."
msgstr "외벽을 출력할 때 최고 속도에 도달하는 가속도입니다."

msgctxt "wall_0_deceleration description"
msgid "This is the deceleration with which to end printing an outer wall."
msgstr "외벽 출력을 종료할 때의 감속도입니다."

msgctxt "wall_0_speed_split_distance description"
msgid "This is the maximum length of an extrusion path when splitting a longer path to apply the outer wall acceleration/deceleration. A smaller distance will create a more precise but also more verbose G-Code."
msgstr "외벽 가속/감속을 적용하기 위해 긴 경로를 분할 때 압출 경로의 최대 길이입니다. 거리가 짧을수록 더 정밀해지지만 G코드도 복잡해집니다."

msgctxt "wall_0_end_speed_ratio description"
msgid "This is the ratio of the top speed to end with when printing an outer wall."
msgstr "외벽을 출력할 때 종료되는 최고 속도의 비율입니다."

msgctxt "wall_0_start_speed_ratio description"
msgid "This is the ratio of the top speed to start with when printing an outer wall."
msgstr "외벽을 출력할 때 시작되는 최고 속도의 비율입니다."

msgctxt "extra_infill_lines_to_support_skins option walls"
msgid "Walls Only"
msgstr "벽만"

msgctxt "extra_infill_lines_to_support_skins option walls_and_lines"
msgid "Walls and Lines"
msgstr "벽 및 선"

msgctxt "wall_overhang_angle description"
msgid "Walls that overhang more than this angle will be printed using overhanging wall settings. When the value is 90, no walls will be treated as overhanging. Overhang that gets supported by support will not be treated as overhang either. Furthermore, any line that's less than half overhanging will also not be treated as overhang."
msgstr "이 각도보다 더 돌출된 벽은 돌출 벽 설정을 사용하여 출력됩니다. 값이 90인 경우 벽은 돌출부로 간주하지 않습니다. 지지대가 지지하는 돌출부도 돌출부로 간주하지 않습니다. 또한 돌출부의 절반보다 짧은 선도 돌출부로 간주하지 않습니다."

msgctxt "flooring_angles description"
msgid "A list of integer line directions to use when the bottom surface skin layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "하단 표면 스킨 레이어에 선 또는 지그재그 패턴을 사용할 때 사용할 정수 라인 방향의 목록입니다. 목록의 요소는 레이어가 진행됨에 따라 순차적으로 사용되며, 목록의 끝에 도달하면 처음부터 다시 시작합니다. 목록 항목은 쉼표로 구분하며 전체 목록은 대괄호 안에 포함됩니다. 기본값은 빈 목록으로, 기존의 기본 각도(45도 및 135도)를 사용합니다."

msgctxt "acceleration_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Acceleration"
msgstr "바닥 표면 내벽 가속도"

msgctxt "jerk_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Jerk"
msgstr "바닥 표면 내벽 충격"

msgctxt "speed_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Speed"
msgstr "바닥 표면 내벽 속도"

msgctxt "wall_x_material_flow_flooring label"
msgid "Bottom Surface Inner Wall(s) Flow"
msgstr "바닥 표면 내벽 흐름"

msgctxt "acceleration_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Acceleration"
msgstr "바닥 표면 외벽 가속도"

msgctxt "wall_0_material_flow_flooring label"
msgid "Bottom Surface Outer Wall Flow"
msgstr "바닥 표면 외벽 흐름"

msgctxt "jerk_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Jerk"
msgstr "바닥 표면 외벽 충격"

msgctxt "speed_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Speed"
msgstr "바닥 표면 외벽 속도"

msgctxt "acceleration_flooring label"
msgid "Bottom Surface Skin Acceleration"
msgstr "바닥 표면 스킨 가속"

msgctxt "flooring_extruder_nr label"
msgid "Bottom Surface Skin Extruder"
msgstr "바닥 표면 스킨 압출기"

msgctxt "flooring_material_flow label"
msgid "Bottom Surface Skin Flow"
msgstr "바닥 표면 스킨 흐름"

msgctxt "jerk_flooring label"
msgid "Bottom Surface Skin Jerk"
msgstr "바닥 표면 스킨 충격"

msgctxt "flooring_layer_count label"
msgid "Bottom Surface Skin Layers"
msgstr "바닥 표면 스킨 레이어"

msgctxt "flooring_angles label"
msgid "Bottom Surface Skin Line Directions"
msgstr "바닥 표면 스킨 라인 방향"

msgctxt "flooring_line_width label"
msgid "Bottom Surface Skin Line Width"
msgstr "바닥 표면 스킨 라인 폭"

msgctxt "flooring_pattern label"
msgid "Bottom Surface Skin Pattern"
msgstr "바닥 표면 스킨 패턴"

msgctxt "speed_flooring label"
msgid "Bottom Surface Skin Speed"
msgstr "바닥 표면 스킨 속도"

msgctxt "flooring_pattern option concentric"
msgid "Concentric"
msgstr "동심원"

msgctxt "variant_name"
msgid "Extruder"
msgstr "압출기"

msgctxt "wall_x_material_flow_flooring description"
msgid "Flow compensation on bottom surface wall lines for all wall lines except the outermost one."
msgstr "바깥쪽 벽선을 제외한 모든 벽선의 바닥 표면 벽선에서 흐름 보정."

msgctxt "flooring_material_flow description"
msgid "Flow compensation on lines of the areas at the bottom of the print."
msgstr "인쇄 하단의 영역에 있는 줄에서 흐름 보정."

msgctxt "wall_0_material_flow_flooring description"
msgid "Flow compensation on the bottom surface outermost wall line."
msgstr "바닥 표면의 가장 바깥쪽 벽선에서 흐름 보정."

msgctxt "machine_gcode_flavor option Cheetah"
msgid "Griffin+Cheetah"
msgstr "그리핀+치타"

msgctxt "retraction_combing_avoid_distance label"
msgid "Inside Travel Avoid Distance"
msgstr "내부 이동 거리 최소화"

msgctxt "flooring_pattern option lines"
msgid "Lines"
msgstr "라인"

msgctxt "cool_min_layer_time_overhang label"
msgid "Minimum Layer Time with Overhang"
msgstr "오버행 시 최소 레이어 시간"

msgctxt "cool_min_layer_time_overhang_min_segment_length label"
msgid "Minimum Overhang Segment Length"
msgstr "최소 오버행 구간 길이"

msgctxt "flooring_monotonic label"
msgid "Monotonic Bottom Surface Order"
msgstr "단조 바닥 표면 정렬"

msgctxt "wall_0_deceleration label"
msgid "Outer Wall End Deceleration"
msgstr "외벽 끝부분 감속"

msgctxt "wall_0_acceleration label"
msgid "Outer Wall Start Acceleration"
msgstr "외벽 시작 부분 가속"

msgctxt "wall_overhang_speed_factors label"
msgid "Overhanging Wall Speeds"
msgstr "오버행 벽 출력 속도"

msgctxt "wall_overhang_speed_factors description"
msgid "Overhanging walls will be printed at a percentage of their normal print speed. You can specify multiple values, so that even more overhanging walls will be printed even slower, e.g. by setting [75, 50, 25]"
msgstr "돌출된 벽은 정상 인쇄 속도의 일정 비율로 인쇄됩니다. 여러 값을 지정할 수 있으므로, 예를 들어 [75, 50, 25]를 설정하여 더 많은 돌출된 벽을 더 느리게 인쇄할 수 있습니다."

msgctxt "material_pressure_advance_factor label"
msgid "Pressure advance factor"
msgstr "압력 상승 계수"

msgctxt "variant_name"
msgid "Print Core"
msgstr "인쇄 코어"

msgctxt "flooring_monotonic description"
msgid "Print bottom surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "인쇄 시에는 하단 표면의 선을 한 방향으로 인접한 선과 항상 겹치게 인쇄하세요. 인쇄하는 데 시간이 조금 더 걸리지만, 평평한 표면이 더 일관성 있게 보입니다."

msgctxt "machine_start_gcode_first label"
msgid "Start GCode must be first"
msgstr "시작 GCode가 반드시 우선합니다"

msgctxt "acceleration_flooring description"
msgid "The acceleration with which bottom surface skin layers are printed."
msgstr "바닥 표면 스킨 레이어의 인쇄 속도가 빨라집니다."

msgctxt "acceleration_wall_x_flooring description"
msgid "The acceleration with which the bottom surface inner walls are printed."
msgstr "바닥 표면 내벽의 인쇄 속도가 빨라집니다."

msgctxt "acceleration_wall_0_flooring description"
msgid "The acceleration with which the bottom surface outermost walls are printed."
msgstr "바닥 표면의 가장 바깥쪽 벽이 인쇄되는 속도."

msgctxt "machine_head_with_fans_polygon description"
msgid "The dimensions of the print head used to determine 'Safe Model Distance' when printing 'One at a Time'. These numbers relate to the centerline of the first extruder nozzle. Left of the nozzle is 'X Min' and must be negative.  Rear of the nozzle is 'Y Min' and must be negative.  X Max (right) and Y Max (front) are positive numbers.  Gantry height is the dimension from the build plate to the X gantry beam."
msgstr "'한 번에 하나씩' 인쇄 시 '안전 모델 거리'를 결정하는 데 쓰이는 프린트 헤드의 제원입니다. 이 숫자들은 첫 번째 압출기 노즐의 중심선과 관련이 있습니다. 노즐의 왼쪽은 'X 최소'이고 음수여야 합니다.  노즐의 뒤쪽은 'Y 최소'이고 음수여야 합니다. X 최대(오른쪽)와 Y 최대(앞쪽)는 양수입니다. 갠트리 높이는 빌드 플레이트에서 X 갠트리 빔까지의 거리입니다."

msgctxt "retraction_combing_avoid_distance description"
msgid "The distance between the nozzle and already printed outer walls when travelling inside a model."
msgstr "모델 내부에서 이동할 때 노즐과 이미 인쇄된 외벽 사이의 거리입니다."

msgctxt "flooring_extruder_nr description"
msgid "The extruder train used for printing the bottom most skin. This is used in multi-extrusion."
msgstr "가장 아래쪽 스킨을 인쇄하는 데 사용되는 압출기 트레인입니다. 이것은 다중 압출에 사용됩니다."

msgctxt "jerk_flooring description"
msgid "The maximum instantaneous velocity change with which bottom surface skin layers are printed."
msgstr "바닥 표면 스킨 레이어가 인쇄되는 최대 순간 속도 변화."

msgctxt "jerk_wall_x_flooring description"
msgid "The maximum instantaneous velocity change with which the bottom surface inner walls are printed."
msgstr "바닥 표면 내벽이 인쇄되는 최대 순간 속도 변화."

msgctxt "jerk_wall_0_flooring description"
msgid "The maximum instantaneous velocity change with which the bottom surface outermost walls are printed."
msgstr "바닥 표면의 가장 바깥쪽 벽에 인쇄되는 최대 순간 속도 변화."

msgctxt "cool_min_layer_time_overhang description"
msgid "The minimum time spent in a layer that contains overhanging extrusions. This forces the printer to slow down, to at least spend the time set here in one layer. This allows the printed material to cool down properly before printing the next layer. Layers may still take shorter than the minimal layer time if Lift Head is disabled and if the Minimum Speed would otherwise be violated."
msgstr "겹쳐진 돌출부가 포함된 레이어에 소요되는 최소 시간입니다. 이 값은 프린터의 속도를 늦추고, 최소한 여기에 설정된 시간을 한 레이어에 사용하도록 합니다. 이렇게 하면 인쇄된 재료가 다음 레이어 인쇄 전 적절하게 냉각될 수 있습니다. 리프트 헤드가 비활성화되어 있고 최소 속도가 위반되는 경우, 레이어 시간은 여전히 최소 레이어 시간보다 짧을 수 있습니다."

msgctxt "flooring_layer_count description"
msgid "The number of bottom most skin layers. Usually only one bottom most layer is sufficient to generate higher quality bottom surfaces."
msgstr "최하단에 있는 스킨 레이어 수입니다. 일반적으로 가장 아래에 있는 레이어 하나만으로 더 높은 품질의 바닥 표면을 생성할 수 있습니다."

msgctxt "flooring_pattern description"
msgid "The pattern of the bottom most layers."
msgstr "가장 아래 레이어의 패턴입니다."

msgctxt "speed_flooring description"
msgid "The speed at which bottom surface skin layers are printed."
msgstr "바닥 표면의 스킨 레이어가 인쇄되는 속도입니다."

msgctxt "speed_wall_x_flooring description"
msgid "The speed at which the bottom surface inner walls are printed."
msgstr "바닥 표면의 내벽이 인쇄되는 속도입니다."

msgctxt "speed_wall_0_flooring description"
msgid "The speed at which the bottom surface outermost wall is printed."
msgstr "바닥 표면의 가장 바깥쪽 벽이 인쇄되는 속도입니다."

msgctxt "machine_start_gcode_first description"
msgid "This setting controls if the start-gcode is forced to always be the first g-code. Without this option other g-code, such as a T0 can be inserted before the start g-code."
msgstr "이 설정은 시작 g-코드를 항상 첫 번째 g-코드로 강제 적용할지 여부를 제어합니다. 이 옵션이 없으면 T0과 같은 다른 g-코드를 시작 g-코드 앞에 삽입할 수 있습니다."

msgctxt "material_pressure_advance_factor description"
msgid "Tuning factor for pressure advance, which is meant to synchronize extrusion with motion"
msgstr "압력 전진에 대한 조정 요소로, 이는 압출과 동작을 동기화하기 위한 것입니다."

msgctxt "cool_min_layer_time_overhang_min_segment_length description"
msgid "When trying to apply the minimum layer time specific for overhanging layers, it will be applied only if at least one consecutive overhanging extrusion move is longer than this value."
msgstr "오버행 레이어에 대한 최소 레이어 시간을 적용하고자 할 때, 해당 값보다 긴 오버행 돌출 이동이 연속적으로 하나 이상 있는 경우에만 적용됩니다."

msgctxt "flooring_line_width description"
msgid "Width of a single line of the areas at the bottom of the print."
msgstr "인쇄 하단 영역에 있는 한 줄의 너비."

msgctxt "flooring_pattern option zigzag"
msgid "Zig Zag"
msgstr "지그재그"
