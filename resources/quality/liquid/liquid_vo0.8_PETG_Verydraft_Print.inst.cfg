[general]
definition = liquid
name = Extra Fast
version = 4

[metadata]
material = generic_petg
quality_type = verydraft
setting_version = 25
type = quality
variant = VO 0.8
weight = -3

[values]
cool_fan_full_at_height = =layer_height_0 + 2 * layer_height
cool_fan_speed_max = =100
cool_min_speed = 2
gradual_infill_step_height = =3 * layer_height
infill_line_width = =round(line_width * 0.65 / 0.75, 2)
infill_sparse_density = 15
layer_height_0 = 0.4
line_width = =machine_nozzle_size * 0.9375
machine_nozzle_cool_down_speed = 0.75
machine_nozzle_heat_up_speed = 1.6
material_final_print_temperature = =max(-273.15, material_print_temperature - 15)
material_initial_print_temperature = =max(-273.15, material_print_temperature - 10)
material_print_temperature = =default_material_print_temperature + 10
prime_tower_enable = True
retract_at_layer_change = False
speed_print = 45
speed_topbottom = =math.ceil(speed_print * 35 / 45)
speed_wall = =math.ceil(speed_print * 40 / 45)
speed_wall_0 = =math.ceil(speed_wall * 35 / 40)
speed_wall_x = =speed_wall
support_angle = 70
support_line_width = =line_width * 0.75
support_xy_distance = =wall_line_width_0 * 1.5
top_bottom_thickness = =layer_height * 4
wall_line_width = =round(line_width * 0.75 / 0.75, 2)
wall_line_width_x = =round(wall_line_width * 0.625 / 0.75, 2)
wall_thickness = =wall_line_width_0 + wall_line_width_x

