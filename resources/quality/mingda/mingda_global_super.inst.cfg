[general]
definition = mingda_base
name = Super Quality
version = 4

[metadata]
global_quality = True
quality_type = super
setting_version = 25
type = quality
weight = -1

[values]
infill_line_width = =round(line_width * 0.42 / 0.35, 2)
infill_sparse_density = 20
jerk_travel = 50
layer_height = 0.12
layer_height_0 = 0.12
raft_airgap = 0.18
raft_base_thickness = =round(layer_height*1.5, 2)
raft_interface_thickness = =round(layer_height*1.2, 2)
support_interface_height = =layer_height*8
top_bottom_thickness = =layer_height_0+layer_height*6
wall_thickness = =line_width*3

