[general]
definition = ultimaker_s5
name = Normal
version = 4

[metadata]
material = generic_cpe
quality_type = fast
setting_version = 25
type = quality
variant = AA 0.4
weight = -1

[values]
infill_pattern = ='zigzag' if infill_sparse_density > 80 else 'triangles'
retraction_prime_speed = =retraction_speed
speed_infill = =math.ceil(speed_print * 50 / 60)
speed_print = 60
speed_topbottom = =math.ceil(speed_print * 30 / 60)
speed_wall = =math.ceil(speed_print * 40 / 60)
speed_wall_0 = =math.ceil(speed_wall * 30 / 40)
support_bottom_distance = =support_z_distance
support_interface_enable = True
support_top_distance = =support_z_distance
support_z_distance = =math.ceil(0.3/layer_height)*layer_height

