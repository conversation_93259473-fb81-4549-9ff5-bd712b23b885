[general]
definition = ultimaker_s5
name = Fine
version = 4

[metadata]
material = generic_petg
quality_type = normal
setting_version = 25
type = quality
variant = AA 0.4
weight = 0

[values]
infill_pattern = ='zigzag' if infill_sparse_density > 80 else 'triangles'
machine_nozzle_cool_down_speed = 0.85
machine_nozzle_heat_up_speed = 1.5
material_print_temperature = =default_material_print_temperature - 5
speed_infill = =math.ceil(speed_print * 45 / 55)
speed_print = 55
speed_topbottom = =math.ceil(speed_print * 30 / 55)
speed_wall = =math.ceil(speed_print * 30 / 55)
support_bottom_distance = =support_z_distance
support_interface_enable = True
support_top_distance = =support_z_distance
support_z_distance = =math.ceil(0.3/layer_height)*layer_height

