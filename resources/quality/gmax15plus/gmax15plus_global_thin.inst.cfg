[general]
definition = gmax15plus
name = gMax 1.5+ Thin Layers
version = 4

[metadata]
global_quality = True
quality_type = high
setting_version = 25
type = quality
weight = 1

[values]
acceleration_enabled = True
acceleration_print = 400
acceleration_travel = 700
adhesion_type = skirt
bottom_layers = 3
coasting_enable = False
coasting_volume = 0.0625
cool_fan_speed = 70
cool_fan_speed_max = 85
infill_before_walls = False
infill_overlap = 20
infill_pattern = grid
layer_height = 0.16
layer_height_0 = 0.2
material_flow = 98
retraction_amount = 0.75
retraction_combing = off
retraction_speed = 70
skin_overlap = 10
skirt_gap = 2
speed_infill = =math.ceil(speed_print * 0.85)
speed_layer_0 = =math.ceil(speed_print * 0.6)
speed_print = 55
speed_slowdown_layers = 1
speed_topbottom = =math.ceil(speed_print * 0.75)
speed_wall = =math.ceil(speed_print * 0.5)
speed_wall_x = =math.ceil(speed_wall * 0.7)
support_interface_enable = True
support_interface_pattern = lines
support_join_distance = 10
support_use_towers = False
support_xy_distance = 1.5
support_z_distance = 0.2
top_bottom_pattern = lines
top_layers = 5
top_thickness = 1
wall_line_count = 2
z_seam_corner = z_seam_corner_none

