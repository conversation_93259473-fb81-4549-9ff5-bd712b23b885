[general]
definition = fusion3
name = Rough Quality
version = 4

[metadata]
material = generic_asa_175
quality_type = rough
setting_version = 25
type = quality
variant = 0.8mm Nozzle

[values]
cool_fan_enabled = True
cool_fan_full_layer = 70
cool_fan_speed = 15
cool_fan_speed_min = 15
cool_min_layer_time = 13
infill_overlap = 15
infill_sparse_density = 30
inset_direction = outside_in
layer_height_0 = 0.48
line_width = 0.85
material_bed_temperature = 100
material_flow = 98
material_flow_layer_0 = 98
material_print_temperature = 290
retract_at_layer_change = False
retraction_amount = 4
retraction_combing = noskin
retraction_combing_max_distance = 10
retraction_speed = 115
skirt_gap = 5
skirt_line_count = 1
speed_print = 55
support_angle = 45
support_enable = True
support_infill_rate = 30
top_bottom_thickness = 1.5
wall_thickness = 1.7
xy_offset_layer_0 = -0.25
zig_zaggify_infill = True

