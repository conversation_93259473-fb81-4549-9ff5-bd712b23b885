[general]
definition = elegoo_base
name = abs_noz0.40_lay0.30
version = 4

[metadata]
material = generic_abs
quality_type = Elegoo_layer_030
setting_version = 25
type = quality
variant = 0.40mm_Elegoo_Nozzle

[values]
brim_gap = 0
brim_width = 10
cool_fan_enabled = False
cool_fan_speed = 0
cool_fan_speed_0 = 0
default_material_bed_temperature = 80
default_material_print_temperature = 240
layer_0_z_overlap = =raft_airgap*0.8
material_shrinkage_percentage_xy = 100.3
raft_airgap = =0.2
raft_margin = 10
retraction_speed = 25
support_top_distance = =extruderValue(support_roof_extruder_nr if support_roof_enable else support_infill_extruder_nr, 'support_z_distance') + (0 if support_structure == 'tree' else 0)
support_xy_distance_overhang = =machine_nozzle_size*0.8
support_z_distance = =layer_height/2
top_bottom_thickness = 0.84
wall_thickness = =line_width*2

