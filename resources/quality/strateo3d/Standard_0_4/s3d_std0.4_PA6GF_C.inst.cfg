[general]
definition = strateo3d
name = C
version = 4

[metadata]
material = emotiontech_pa6gf
quality_type = c
setting_version = 25
type = quality
variant = Standard 0.4
weight = -1

[values]
bridge_fan_speed = 80
bridge_settings_enabled = True
bridge_skin_density = 80
bridge_skin_material_flow = 60
bridge_skin_speed = 8
bridge_wall_coast = 50
bridge_wall_material_flow = 50
bridge_wall_speed = 8
cool_fan_enabled = True
cool_fan_full_at_height = =layer_height_0 + 2 * layer_height
cool_fan_speed = 20
cool_fan_speed_max = 80
cool_lift_head = True
cool_min_layer_time = 2
cool_min_layer_time_fan_speed_max = 20
cool_min_speed = 2
layer_height_0 = =round(0.75*machine_nozzle_size, 2)
line_width = =machine_nozzle_size/machine_nozzle_size*0.4
material_final_print_temperature = =default_material_print_temperature
material_flow = 91
material_initial_print_temperature = =default_material_print_temperature
material_print_temperature = =default_material_print_temperature
material_print_temperature_layer_0 = =default_material_print_temperature + 5
meshfix_maximum_deviation = 0.04
meshfix_maximum_resolution = 0.5
prime_tower_enable = True
retraction_extra_prime_amount = 0.1
retraction_hop_only_when_collides = True
retraction_min_travel = =3*line_width
skin_material_flow = 92
skin_overlap = 20
speed_layer_0 = =math.ceil(speed_print * 30/60)
speed_print = 35
speed_slowdown_layers = 2
speed_topbottom = =math.ceil(speed_print * 40/60)
speed_wall = =math.ceil(speed_print * 40/60)
speed_wall_0 = =math.ceil(speed_wall * 35/40)
support_angle = 60
support_bottom_distance = =support_z_distance*0.5
support_interface_density = 100
support_offset = 1
support_xy_distance = =line_width * 1.7
support_xy_distance_overhang = =wall_line_width_0
support_z_distance = =layer_height*2
wall_0_wipe_dist = =machine_nozzle_size/2
wall_line_count = 3
wall_line_width = =machine_nozzle_size/machine_nozzle_size*0.35
wall_line_width_x = =machine_nozzle_size/machine_nozzle_size*0.3

