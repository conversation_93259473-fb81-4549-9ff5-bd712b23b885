[general]
definition = cartesio
name = Coarse
version = 4

[metadata]
material = generic_pva
quality_type = coarse
setting_version = 25
type = quality
variant = 0.8mm thermoplastic extruder
weight = 3

[values]
coasting_enable = True
coasting_min_volume = 0.17
coasting_speed = 90
coasting_volume = 0.1
cool_min_layer_time = 20
cool_min_layer_time_fan_speed_max = =cool_min_layer_time
infill_line_width = 0.9
infill_pattern = grid
infill_sparse_density = 40
material_final_print_temperature = =material_print_temperature
material_initial_print_temperature = =material_print_temperature_layer_0 + 5
material_print_temperature_layer_0 = =material_print_temperature + 5
retraction_hop = 1
retraction_hop_enabled = True
retraction_min_travel = =round(line_width * 10)
retraction_prime_speed = 8
skirt_brim_minimal_length = 50
speed_infill = =speed_print
speed_layer_0 = =round(speed_print / 5 * 4)
speed_print = 30
speed_support_interface = =speed_topbottom
speed_topbottom = =round(speed_print / 5 * 4)
speed_travel = =round(speed_print if magic_spiralize else 150)
speed_travel_layer_0 = =speed_travel
speed_wall = =round(speed_print / 2)
speed_wall_0 = =10 if speed_wall < 11 else (speed_print / 5 *3)
switch_extruder_prime_speed = =retraction_prime_speed
switch_extruder_retraction_amount = 2
switch_extruder_retraction_speeds = =retraction_speed
top_bottom_thickness = =layer_height * 3
wall_0_inset = -0.05
wall_thickness = 2.4

