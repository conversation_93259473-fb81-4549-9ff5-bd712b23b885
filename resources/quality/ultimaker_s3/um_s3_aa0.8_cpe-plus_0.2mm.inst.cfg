[general]
definition = ultimaker_s3
name = Fast - Experimental
version = 4

[metadata]
is_experimental = True
material = generic_cpe_plus
quality_type = draft
setting_version = 25
type = quality
variant = AA 0.8
weight = -2

[values]
brim_width = 14
machine_nozzle_cool_down_speed = 0.9
machine_nozzle_heat_up_speed = 1.4
material_print_temperature = =default_material_print_temperature - 20
prime_tower_enable = True
retraction_hop = 0.1
retraction_hop_enabled = False
speed_print = 50
speed_topbottom = =math.ceil(speed_print * 35 / 50)
speed_wall = =math.ceil(speed_print * 40 / 50)
speed_wall_0 = =math.ceil(speed_wall * 35 / 40)
support_z_distance = =layer_height
top_bottom_thickness = 1.2

