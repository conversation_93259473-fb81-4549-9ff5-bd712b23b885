#!/bin/bash

# MacOS Cura Build Script with Custom CuraEngine
# This script builds Cura for MacOS using a custom CuraEngine
# Compatible with both MacOS and Windows development environments

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"
CUSTOM_CURAENGINE_PATH="/Users/<USER>/PycharmProjects/CuraProject/CuraEngine/build/Release/CuraEngine"
BUILD_DIR="$PROJECT_ROOT/build_macos"
DIST_DIR="$PROJECT_ROOT/dist_macos"
VENV_DIR="$PROJECT_ROOT/venv_build"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're on macOS
check_platform() {
    if [[ "$OSTYPE" != "darwin"* ]]; then
        log_error "This script is designed for macOS. Current OS: $OSTYPE"
        exit 1
    fi
    log_success "Running on macOS"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if custom CuraEngine exists
    if [[ ! -f "$CUSTOM_CURAENGINE_PATH" ]]; then
        log_error "Custom CuraEngine not found at: $CUSTOM_CURAENGINE_PATH"
        log_error "Please ensure your CuraEngine is compiled and available."
        exit 1
    fi
    log_success "Custom CuraEngine found: $CUSTOM_CURAENGINE_PATH"
    
    # Check if CuraEngine is executable
    if [[ ! -x "$CUSTOM_CURAENGINE_PATH" ]]; then
        log_error "CuraEngine is not executable: $CUSTOM_CURAENGINE_PATH"
        log_error "Run: chmod +x $CUSTOM_CURAENGINE_PATH"
        exit 1
    fi
    log_success "CuraEngine is executable"
    
    # Check required tools
    local required_tools=("python3" "conan" "msgfmt" "git")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "Required tool not found: $tool"
            case $tool in
                "conan")
                    log_error "Install with: pip install conan"
                    ;;
                "msgfmt")
                    log_error "Install with: brew install gettext"
                    ;;
            esac
            exit 1
        fi
        log_success "Found $tool: $(which $tool)"
    done
}

# Setup build environment
setup_build_environment() {
    log_info "Setting up build environment..."
    
    # Create directories
    mkdir -p "$BUILD_DIR"
    mkdir -p "$DIST_DIR"
    
    # Create Python virtual environment if it doesn't exist
    if [[ ! -d "$VENV_DIR" ]]; then
        log_info "Creating Python virtual environment..."
        python3 -m venv "$VENV_DIR"
    fi
    
    # Activate virtual environment
    source "$VENV_DIR/bin/activate"
    
    # Upgrade pip and install conan if needed
    pip install --upgrade pip
    pip install conan
    
    log_success "Build environment ready"
}

# Copy custom CuraEngine
copy_custom_curaengine() {
    log_info "Copying custom CuraEngine..."
    
    local dest_path="$PROJECT_ROOT/CuraEngine"
    
    # Remove existing CuraEngine if it exists
    if [[ -f "$dest_path" ]]; then
        rm -f "$dest_path"
    fi
    
    # Copy the custom CuraEngine
    cp "$CUSTOM_CURAENGINE_PATH" "$dest_path"
    chmod +x "$dest_path"
    
    log_success "Copied CuraEngine to: $dest_path"
    
    # Verify the copy
    if [[ ! -x "$dest_path" ]]; then
        log_error "Failed to copy CuraEngine properly"
        exit 1
    fi
}

# Compile translations
compile_translations() {
    log_info "Compiling translations..."
    
    cd "$PROJECT_ROOT"
    
    # Make sure the script is executable
    chmod +x "./compile_translations.sh"
    
    # Run translation compilation
    if ./compile_translations.sh; then
        log_success "Translations compiled successfully"
    else
        log_error "Translation compilation failed"
        exit 1
    fi
}

# Install Conan dependencies
install_conan_dependencies() {
    log_info "Installing Conan dependencies..."
    
    cd "$BUILD_DIR"
    
    # Detect architecture
    local arch="x86_64"
    if [[ "$(uname -m)" == "arm64" ]]; then
        arch="armv8"
    fi
    
    log_info "Building for architecture: $arch"
    
    # Run conan install
    if conan install "$PROJECT_ROOT" \
        --build=missing \
        -s build_type=Release \
        -s arch="$arch" \
        -s os=Macos; then
        log_success "Conan dependencies installed"
    else
        log_error "Conan install failed"
        exit 1
    fi
}

# Build Cura
build_cura() {
    log_info "Building Cura..."
    
    cd "$BUILD_DIR"
    
    if conan build "$PROJECT_ROOT"; then
        log_success "Cura built successfully"
    else
        log_error "Cura build failed"
        exit 1
    fi
}

# Create distribution package
create_distribution() {
    log_info "Creating distribution package..."
    
    cd "$BUILD_DIR"
    
    # Run conan deploy to prepare for packaging
    if conan deploy "$PROJECT_ROOT" --deployer-folder="$DIST_DIR"; then
        log_success "Conan deploy completed"
    else
        log_error "Conan deploy failed"
        exit 1
    fi
    
    # Find and run PyInstaller
    local spec_file
    spec_file=$(find "$DIST_DIR" -name "*.spec" | head -1)
    
    if [[ -z "$spec_file" ]]; then
        log_error "No PyInstaller spec file found"
        exit 1
    fi
    
    log_info "Using spec file: $spec_file"
    
    cd "$DIST_DIR"
    
    if pyinstaller --clean --noconfirm "$spec_file"; then
        log_success "PyInstaller package created"
    else
        log_error "PyInstaller failed"
        exit 1
    fi
}

# Verify CuraEngine in package
verify_curaengine() {
    log_info "Verifying CuraEngine in package..."
    
    # Find the .app bundle
    local app_bundle
    app_bundle=$(find "$DIST_DIR" -name "*.app" -type d | head -1)
    
    if [[ -z "$app_bundle" ]]; then
        log_error "No .app bundle found"
        exit 1
    fi
    
    log_info "Found app bundle: $app_bundle"
    
    # Look for CuraEngine in the bundle
    local curaengine_paths=(
        "$app_bundle/Contents/MacOS/CuraEngine"
        "$app_bundle/Contents/Resources/CuraEngine"
        "$app_bundle/Contents/Frameworks/CuraEngine"
    )
    
    local found_curaengine=""
    for path in "${curaengine_paths[@]}"; do
        if [[ -f "$path" ]]; then
            found_curaengine="$path"
            break
        fi
    done
    
    if [[ -z "$found_curaengine" ]]; then
        log_error "CuraEngine not found in app bundle"
        exit 1
    fi
    
    log_success "CuraEngine found in bundle: $found_curaengine"
    
    # Verify it's executable
    if [[ ! -x "$found_curaengine" ]]; then
        log_warning "CuraEngine in bundle is not executable, fixing..."
        chmod +x "$found_curaengine"
    fi
    
    log_success "CuraEngine verification completed"
}

# Create DMG installer
create_dmg() {
    log_info "Creating DMG installer..."
    
    # Find the .app bundle
    local app_bundle
    app_bundle=$(find "$DIST_DIR" -name "*.app" -type d | head -1)
    
    if [[ -z "$app_bundle" ]]; then
        log_error "No .app bundle found for DMG creation"
        exit 1
    fi
    
    local app_name
    app_name=$(basename "$app_bundle" .app)
    
    # Determine architecture and version
    local arch="X64"
    if [[ "$(uname -m)" == "arm64" ]]; then
        arch="ARM64"
    fi
    
    local version="5.11.0-alpha.0"
    local filename="UltiMaker-Cura-${version}-MacOS-${arch}"
    
    # Use the existing MacOS build script
    local build_script="$PROJECT_ROOT/packaging/MacOS/build_macos.py"
    
    if [[ ! -f "$build_script" ]]; then
        log_error "MacOS build script not found: $build_script"
        exit 1
    fi
    
    if python3 "$build_script" \
        --source_path "$PROJECT_ROOT" \
        --dist_path "$DIST_DIR" \
        --cura_conan_version "$version" \
        --filename "$filename" \
        --build_dmg \
        --app_name "$app_name"; then
        
        local dmg_file="$DIST_DIR/${filename}.dmg"
        if [[ -f "$dmg_file" ]]; then
            log_success "DMG created: $dmg_file"
        else
            log_error "DMG file not found after build"
            exit 1
        fi
    else
        log_error "DMG creation failed"
        exit 1
    fi
}

# Main function
main() {
    log_info "Starting MacOS package build for Cura with custom CuraEngine"
    log_info "Project root: $PROJECT_ROOT"
    log_info "Custom CuraEngine: $CUSTOM_CURAENGINE_PATH"
    
    check_platform
    check_prerequisites
    setup_build_environment
    copy_custom_curaengine
    compile_translations
    install_conan_dependencies
    build_cura
    create_distribution
    verify_curaengine
    create_dmg
    
    log_success "🎉 MacOS package build completed successfully!"
    log_success "📁 Output directory: $DIST_DIR"
    
    # List the created files
    log_info "Created files:"
    find "$DIST_DIR" -name "*.dmg" -o -name "*.app" | while read -r file; do
        log_info "  - $file"
    done
}

# Run main function
main "$@"
