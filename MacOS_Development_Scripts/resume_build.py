#!/usr/bin/env python3
"""
Resume Build Script for Cura MacOS Package

This script can resume a build from where it was interrupted,
skipping steps that have already been completed successfully.
"""

import os
import sys
import argparse
from pathlib import Path
from build_cura_complete import CuraCompleteBuilder

def check_step_completion(builder):
    """Check which build steps have been completed."""
    completed_steps = {}
    
    # Check if CuraEngine is copied
    curaengine_path = builder.project_root / "CuraEngine"
    completed_steps['environment_setup'] = curaengine_path.exists() and os.access(curaengine_path, os.X_OK)
    
    # Check if translations are compiled
    i18n_dir = builder.project_root / "resources" / "i18n"
    mo_files = list(i18n_dir.glob("**/*.mo")) if i18n_dir.exists() else []
    completed_steps['translation_compilation'] = len(mo_files) > 0
    
    # Check if conan install is done
    build_generators = builder.build_dir / "generators"
    completed_steps['dependency_installation'] = build_generators.exists()
    
    # Check if conan build is done
    completed_steps['cura_build'] = (builder.build_dir / "build").exists()
    
    # Check if package is created
    spec_files = list(builder.dist_dir.glob("*.spec"))
    completed_steps['package_creation'] = len(spec_files) > 0
    
    # Check if app bundle exists
    app_bundles = list(builder.dist_dir.glob("**/UltiMaker-Cura.app"))
    if not app_bundles:
        app_bundles = list(builder.dist_dir.glob("**/*.app"))
    completed_steps['package_verification'] = len(app_bundles) > 0
    
    # Check if DMG exists
    dmg_files = list(builder.dist_dir.glob("*.dmg"))
    completed_steps['dmg_creation'] = len(dmg_files) > 0
    
    return completed_steps

def resume_build(version="5.11.0-alpha.0", create_dmg=True, cleanup=True):
    """Resume build from where it was interrupted."""
    builder = CuraCompleteBuilder()
    
    print("🔄 Resuming Cura MacOS Package Build")
    print("=" * 50)
    
    # Check what's already completed
    completed = check_step_completion(builder)
    
    print("📋 Build Status Check:")
    step_names = {
        'environment_setup': 'Environment Setup',
        'translation_compilation': 'Translation Compilation', 
        'dependency_installation': 'Dependency Installation',
        'cura_build': 'Cura Build',
        'package_creation': 'Package Creation',
        'package_verification': 'Package Verification',
        'dmg_creation': 'DMG Creation'
    }
    
    for step_key, step_name in step_names.items():
        status = "✅ Completed" if completed.get(step_key, False) else "⏸️  Pending"
        print(f"  {step_name}: {status}")
    
    # Determine where to start
    if not completed.get('environment_setup', False):
        print("\n🚀 Starting from the beginning...")
        return builder.build_complete_package(version, create_dmg, cleanup)
    
    elif not completed.get('translation_compilation', False):
        print("\n🌐 Resuming from Translation Compilation...")
        steps = [
            ("Translation Compilation", builder.compile_translations),
            ("Dependency Installation", builder.install_dependencies),
            ("Cura Build", builder.build_cura),
            ("Package Creation", builder.create_package),
            ("Package Verification", builder.verify_package),
        ]
        if create_dmg:
            steps.append(("DMG Creation", lambda: builder.create_dmg(version)))
        if cleanup:
            steps.append(("Cleanup", builder.cleanup_build_artifacts))
            
    elif not completed.get('dependency_installation', False):
        print("\n📦 Resuming from Dependency Installation...")
        steps = [
            ("Dependency Installation", builder.install_dependencies),
            ("Cura Build", builder.build_cura),
            ("Package Creation", builder.create_package),
            ("Package Verification", builder.verify_package),
        ]
        if create_dmg:
            steps.append(("DMG Creation", lambda: builder.create_dmg(version)))
        if cleanup:
            steps.append(("Cleanup", builder.cleanup_build_artifacts))
            
    elif not completed.get('cura_build', False):
        print("\n🔨 Resuming from Cura Build...")
        steps = [
            ("Cura Build", builder.build_cura),
            ("Package Creation", builder.create_package),
            ("Package Verification", builder.verify_package),
        ]
        if create_dmg:
            steps.append(("DMG Creation", lambda: builder.create_dmg(version)))
        if cleanup:
            steps.append(("Cleanup", builder.cleanup_build_artifacts))
            
    elif not completed.get('package_creation', False):
        print("\n📦 Resuming from Package Creation...")
        steps = [
            ("Package Creation", builder.create_package),
            ("Package Verification", builder.verify_package),
        ]
        if create_dmg:
            steps.append(("DMG Creation", lambda: builder.create_dmg(version)))
        if cleanup:
            steps.append(("Cleanup", builder.cleanup_build_artifacts))
            
    elif not completed.get('package_verification', False):
        print("\n🔍 Resuming from Package Verification...")
        steps = [
            ("Package Verification", builder.verify_package),
        ]
        if create_dmg:
            steps.append(("DMG Creation", lambda: builder.create_dmg(version)))
        if cleanup:
            steps.append(("Cleanup", builder.cleanup_build_artifacts))
            
    elif create_dmg and not completed.get('dmg_creation', False):
        print("\n💿 Resuming from DMG Creation...")
        steps = [
            ("DMG Creation", lambda: builder.create_dmg(version)),
        ]
        if cleanup:
            steps.append(("Cleanup", builder.cleanup_build_artifacts))
            
    else:
        print("\n🎉 Build appears to be complete!")
        
        # List existing files
        print("\n📦 Existing output files:")
        for pattern in ["*.dmg", "*.app"]:
            for file in builder.dist_dir.glob(f"**/{pattern}"):
                file_size = file.stat().st_size / 1024 / 1024  # MB
                print(f"  📄 {file.name} ({file_size:.1f} MB)")
        
        return True
    
    # Execute remaining steps
    for step_name, step_func in steps:
        print(f"\n📋 Executing: {step_name}")
        try:
            if not step_func():
                print(f"❌ {step_name} failed")
                return False
            print(f"✅ {step_name} completed")
        except Exception as e:
            print(f"❌ {step_name} failed with exception: {e}")
            return False
    
    print("\n🎉 Build completed successfully!")
    return True

def main():
    parser = argparse.ArgumentParser(description="Resume interrupted Cura MacOS build")
    parser.add_argument("--version", default="5.11.0-alpha.0", help="Cura version")
    parser.add_argument("--no-dmg", action="store_true", help="Skip DMG creation")
    parser.add_argument("--no-cleanup", action="store_true", help="Skip cleanup")
    
    args = parser.parse_args()
    
    success = resume_build(
        version=args.version,
        create_dmg=not args.no_dmg,
        cleanup=not args.no_cleanup
    )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
