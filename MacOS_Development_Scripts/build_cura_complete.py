#!/usr/bin/env python3
"""
Complete Cura MacOS Package Builder

This is the main script for building a complete MacOS installation package for Cura
with your custom CuraEngine. It handles all steps from source compilation to final DMG creation.

Features:
- 1-hour timeout for <PERSON> builds with verbose logging
- Complete automation from source to installer
- Cross-platform compatibility (MacOS/Windows development)
- Standardized one-script build process
"""

import os
import sys
import subprocess
import shutil
import tempfile
import time
from pathlib import Path
import argparse
from datetime import datetime

class CuraCompleteBuilder:
    def __init__(self):
        # Get the parent directory (Cura project root)
        self.script_dir = Path(__file__).parent.absolute()
        self.project_root = self.script_dir.parent
        
        # Custom CuraEngine path
        self.custom_curaengine = Path("/Users/<USER>/PycharmProjects/CuraProject/CuraEngine/build/Release/CuraEngine")
        
        # Build directories
        self.build_dir = self.project_root / "build_macos_complete"
        self.dist_dir = self.project_root / "dist_macos_complete"
        
        # Log file
        self.log_file = self.script_dir / f"build_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        # Timeout settings (1 hour for Conan)
        self.conan_timeout = 3600  # 1 hour
        self.default_timeout = 300  # 5 minutes for other operations
        
    def log(self, level, message, print_to_console=True):
        """Log a message with timestamp and color coding."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] [{level}] {message}"
        
        # Write to log file
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')
        
        if print_to_console:
            colors = {
                'INFO': '\033[0;34m',
                'SUCCESS': '\033[0;32m',
                'WARNING': '\033[1;33m',
                'ERROR': '\033[0;31m',
                'NC': '\033[0m'
            }
            colored_message = f"{colors.get(level, '')}{log_message}{colors['NC']}"
            print(colored_message)
    
    def run_command(self, cmd, cwd=None, timeout=None, show_output=True):
        """Run a command with real-time output and logging."""
        if timeout is None:
            timeout = self.default_timeout
        
        if cwd is None:
            cwd = self.project_root
        
        cmd_str = ' '.join(cmd) if isinstance(cmd, list) else cmd
        self.log('INFO', f"Running command: {cmd_str}")
        self.log('INFO', f"Working directory: {cwd}")
        self.log('INFO', f"Timeout: {timeout} seconds")
        
        try:
            # Use Popen for real-time output
            process = subprocess.Popen(
                cmd,
                cwd=cwd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True,
                shell=isinstance(cmd, str)
            )
            
            output_lines = []
            start_time = time.time()
            
            # Read output line by line
            try:
                while True:
                    output = process.stdout.readline()
                    if output == '' and process.poll() is not None:
                        break

                    if output:
                        line = output.strip()
                        output_lines.append(line)

                        # Log to file
                        with open(self.log_file, 'a', encoding='utf-8') as f:
                            f.write(f"[{datetime.now().strftime('%H:%M:%S')}] {line}\n")

                        # Print to console if requested
                        if show_output:
                            print(f"  {line}")

                    # Check timeout
                    if time.time() - start_time > timeout:
                        process.terminate()
                        self.log('ERROR', f"Command timed out after {timeout} seconds")
                        return False

            except KeyboardInterrupt:
                self.log('WARNING', "Build interrupted by user (Ctrl+C)")
                process.terminate()
                process.wait()
                return False
            
            # Wait for process to complete
            return_code = process.poll()
            
            if return_code == 0:
                self.log('SUCCESS', f"Command completed successfully")
                return True
            else:
                self.log('ERROR', f"Command failed with return code {return_code}")
                return False
                
        except Exception as e:
            self.log('ERROR', f"Command failed with exception: {e}")
            return False
    
    def check_prerequisites(self):
        """Check if all prerequisites are met."""
        self.log('INFO', "🔍 Checking prerequisites...")
        
        # Check custom CuraEngine
        if not self.custom_curaengine.exists():
            self.log('ERROR', f"Custom CuraEngine not found: {self.custom_curaengine}")
            self.log('INFO', "Please compile CuraEngine first:")
            self.log('INFO', "cd /Users/<USER>/PycharmProjects/CuraProject/CuraEngine")
            self.log('INFO', "cmake -B build -DCMAKE_BUILD_TYPE=Release")
            self.log('INFO', "cmake --build build --config Release")
            return False
        
        if not os.access(self.custom_curaengine, os.X_OK):
            self.log('ERROR', f"Custom CuraEngine is not executable: {self.custom_curaengine}")
            self.log('INFO', f"Run: chmod +x {self.custom_curaengine}")
            return False
        
        self.log('SUCCESS', f"✅ Custom CuraEngine found: {self.custom_curaengine}")
        
        # Check CuraEngine version
        try:
            result = subprocess.run([str(self.custom_curaengine), "--version"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version = result.stdout.strip() or "Unknown"
                self.log('SUCCESS', f"✅ CuraEngine version: {version}")
            else:
                self.log('WARNING', "⚠️  Could not get CuraEngine version")
        except Exception as e:
            self.log('WARNING', f"⚠️  CuraEngine version check failed: {e}")
        
        # Check required tools
        required_tools = {
            'conan': ['conan', '--version'],
            'python3': ['python3', '--version'],
            'msgfmt': ['msgfmt', '--version'],
            'cmake': ['cmake', '--version'],
            'git': ['git', '--version'],
            'hdiutil': ['hdiutil', 'help']  # hdiutil doesn't have --version
        }
        
        for tool_name, cmd in required_tools.items():
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    version_line = result.stdout.split('\n')[0]
                    self.log('SUCCESS', f"✅ {tool_name}: {version_line}")
                else:
                    self.log('ERROR', f"❌ {tool_name} not working properly")
                    return False
            except FileNotFoundError:
                self.log('ERROR', f"❌ {tool_name} not found")
                self.log('INFO', f"Install with: brew install {tool_name}")
                return False
            except Exception as e:
                self.log('ERROR', f"❌ {tool_name} check failed: {e}")
                return False
        
        # Check platform
        if sys.platform != "darwin":
            self.log('ERROR', f"❌ This script is for macOS only. Current platform: {sys.platform}")
            return False
        
        # Check architecture
        arch = os.uname().machine
        self.log('INFO', f"🏗️  Building for architecture: {arch}")
        
        return True
    
    def setup_environment(self):
        """Set up the build environment."""
        self.log('INFO', "🏗️  Setting up build environment...")
        
        # Create directories
        self.build_dir.mkdir(exist_ok=True)
        self.dist_dir.mkdir(exist_ok=True)
        
        self.log('SUCCESS', f"✅ Build directory: {self.build_dir}")
        self.log('SUCCESS', f"✅ Distribution directory: {self.dist_dir}")
        
        # Copy custom CuraEngine to project root
        dest_curaengine = self.project_root / "CuraEngine"
        if dest_curaengine.exists():
            dest_curaengine.unlink()
        
        shutil.copy2(self.custom_curaengine, dest_curaengine)
        os.chmod(dest_curaengine, 0o755)
        
        self.log('SUCCESS', f"✅ Copied CuraEngine to: {dest_curaengine}")
        
        # Verify the copy
        if not os.access(dest_curaengine, os.X_OK):
            self.log('ERROR', "❌ Failed to copy CuraEngine properly")
            return False
        
        return True
    
    def compile_translations(self):
        """Compile translation files."""
        self.log('INFO', "🌐 Compiling translations...")
        
        # Use the translation script from our scripts directory
        script_path = self.script_dir / "compile_translations.sh"
        if not script_path.exists():
            # Fallback to project root
            script_path = self.project_root / "compile_translations.sh"
        
        if not script_path.exists():
            self.log('ERROR', "❌ Translation script not found")
            return False
        
        # Make sure it's executable
        os.chmod(script_path, 0o755)
        
        return self.run_command([str(script_path)], cwd=self.project_root)
    
    def install_dependencies(self):
        """Install Conan dependencies with verbose logging."""
        self.log('INFO', "📦 Installing Conan dependencies (this may take up to 1 hour)...")
        
        # Detect architecture
        arch = "armv8" if "arm64" in os.uname().machine.lower() else "x86_64"
        self.log('INFO', f"🏗️  Building for architecture: {arch}")
        
        # Conan install command with verbose output
        cmd = [
            "conan", "install", str(self.project_root),
            "--build=missing",
            "-s", "build_type=Release",
            "-s", f"arch={arch}",
            "-s", "os=Macos",
            "-v"  # Verbose output
        ]
        
        self.log('INFO', "⏰ Starting Conan dependency installation...")
        self.log('INFO', "📝 This process will show detailed logs and may take 30-60 minutes")
        
        return self.run_command(cmd, cwd=self.build_dir, timeout=self.conan_timeout, show_output=True)
    
    def build_cura(self):
        """Build Cura with verbose logging."""
        self.log('INFO', "🔨 Building Cura...")
        
        cmd = ["conan", "build", str(self.project_root), "-v"]
        return self.run_command(cmd, cwd=self.build_dir, timeout=1800, show_output=True)  # 30 minutes
    
    def create_package(self):
        """Create the distribution package."""
        self.log('INFO', "📦 Creating distribution package...")
        
        # Deploy with Conan
        cmd = ["conan", "deploy", str(self.project_root), "--deployer-folder", str(self.dist_dir), "-v"]
        if not self.run_command(cmd, cwd=self.build_dir, timeout=600, show_output=True):
            return False
        
        # Find PyInstaller spec file
        spec_files = list(self.dist_dir.glob("*.spec"))
        if not spec_files:
            self.log('ERROR', "❌ No PyInstaller spec file found")
            return False
        
        spec_file = spec_files[0]
        self.log('INFO', f"📄 Using spec file: {spec_file}")
        
        # Run PyInstaller with verbose output
        cmd = ["pyinstaller", "--clean", "--noconfirm", "--log-level", "INFO", str(spec_file)]
        return self.run_command(cmd, cwd=self.dist_dir, timeout=1800, show_output=True)  # 30 minutes

    def verify_package(self):
        """Verify the created package."""
        self.log('INFO', "🔍 Verifying package...")

        # Find .app bundle
        app_bundles = list(self.dist_dir.glob("**/UltiMaker-Cura.app"))
        if not app_bundles:
            app_bundles = list(self.dist_dir.glob("**/*.app"))

        if not app_bundles:
            self.log('ERROR', "❌ No .app bundle found")
            return False

        app_bundle = app_bundles[0]
        self.log('SUCCESS', f"✅ Found app bundle: {app_bundle}")

        # Check for CuraEngine in bundle
        possible_paths = [
            app_bundle / "Contents" / "MacOS" / "CuraEngine",
            app_bundle / "Contents" / "Resources" / "CuraEngine",
            app_bundle / "Contents" / "Frameworks" / "CuraEngine"
        ]

        curaengine_found = None
        for path in possible_paths:
            if path.exists():
                curaengine_found = path
                break

        if curaengine_found:
            self.log('SUCCESS', f"✅ CuraEngine found in bundle: {curaengine_found}")

            # Verify it's executable
            if os.access(curaengine_found, os.X_OK):
                self.log('SUCCESS', "✅ CuraEngine is executable")
            else:
                self.log('WARNING', "⚠️  CuraEngine is not executable, fixing...")
                os.chmod(curaengine_found, 0o755)

            # Verify it's our custom version
            original_size = self.custom_curaengine.stat().st_size
            bundle_size = curaengine_found.stat().st_size

            if original_size == bundle_size:
                self.log('SUCCESS', "✅ CuraEngine size matches custom version")
            else:
                self.log('WARNING', f"⚠️  CuraEngine size differs: original={original_size}, bundle={bundle_size}")
                self.log('INFO', "This might be normal due to packaging/stripping")
        else:
            self.log('ERROR', "❌ CuraEngine not found in bundle")
            return False

        # Check app bundle structure
        required_paths = [
            app_bundle / "Contents" / "Info.plist",
            app_bundle / "Contents" / "MacOS",
            app_bundle / "Contents" / "Resources"
        ]

        for path in required_paths:
            if path.exists():
                self.log('SUCCESS', f"✅ Found: {path.name}")
            else:
                self.log('WARNING', f"⚠️  Missing: {path}")

        return True

    def create_dmg(self, version="5.11.0-alpha.0"):
        """Create DMG installer."""
        self.log('INFO', "💿 Creating DMG installer...")

        # Find app bundle
        app_bundles = list(self.dist_dir.glob("**/UltiMaker-Cura.app"))
        if not app_bundles:
            app_bundles = list(self.dist_dir.glob("**/*.app"))

        if not app_bundles:
            self.log('ERROR', "❌ No .app bundle found for DMG creation")
            return False

        app_bundle = app_bundles[0]

        # Determine architecture and create filename
        arch = "ARM64" if "arm64" in os.uname().machine.lower() else "X64"
        timestamp = datetime.now().strftime("%Y%m%d_%H%M")
        filename = f"UltiMaker-Cura-{version}-MacOS-{arch}-{timestamp}"

        # Try using existing MacOS build script first
        build_script = self.project_root / "packaging" / "MacOS" / "build_macos.py"

        if build_script.exists():
            self.log('INFO', "📄 Using official MacOS build script...")

            cmd = [
                sys.executable, str(build_script),
                "--source_path", str(self.project_root),
                "--dist_path", str(self.dist_dir),
                "--cura_conan_version", version,
                "--filename", filename,
                "--build_dmg",
                "--app_name", app_bundle.stem
            ]

            if self.run_command(cmd, timeout=600):
                dmg_file = self.dist_dir / f"{filename}.dmg"
                if dmg_file.exists():
                    self.log('SUCCESS', f"✅ DMG created using official script: {dmg_file}")
                    return dmg_file

        # Fallback: create simple DMG
        self.log('INFO', "📄 Creating DMG using hdiutil...")

        dmg_name = f"{filename}.dmg"
        dmg_path = self.dist_dir / dmg_name

        # Create temporary directory for DMG contents
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            # Copy app bundle to temp directory
            temp_app = temp_path / app_bundle.name
            shutil.copytree(app_bundle, temp_app)

            # Create Applications symlink
            applications_link = temp_path / "Applications"
            os.symlink("/Applications", applications_link)

            # Create DMG
            cmd = [
                "hdiutil", "create",
                "-volname", f"UltiMaker Cura {version}",
                "-srcfolder", str(temp_path),
                "-ov", "-format", "UDZO",
                str(dmg_path)
            ]

            if self.run_command(cmd, timeout=600):
                self.log('SUCCESS', f"✅ DMG created: {dmg_path}")
                return dmg_path

        return None

    def cleanup_build_artifacts(self):
        """Clean up build artifacts to save space."""
        self.log('INFO', "🧹 Cleaning up build artifacts...")

        # Remove intermediate build files but keep the final package
        cleanup_patterns = [
            self.build_dir / "**" / "*.o",
            self.build_dir / "**" / "*.obj",
            self.build_dir / "**" / "CMakeFiles",
            self.build_dir / "**" / ".conan",
        ]

        cleaned_size = 0
        for pattern in cleanup_patterns:
            for path in self.build_dir.glob(str(pattern.relative_to(self.build_dir))):
                if path.is_file():
                    cleaned_size += path.stat().st_size
                    path.unlink()
                elif path.is_dir():
                    shutil.rmtree(path, ignore_errors=True)

        if cleaned_size > 0:
            self.log('SUCCESS', f"✅ Cleaned up {cleaned_size / 1024 / 1024:.1f} MB of build artifacts")

        return True

    def build_complete_package(self, version="5.11.0-alpha.0", create_dmg=True, cleanup=True):
        """Main build process for complete package."""
        start_time = time.time()

        self.log('INFO', "🚀 Starting Complete Cura MacOS Package Build")
        self.log('INFO', f"📍 Project root: {self.project_root}")
        self.log('INFO', f"🔧 Custom CuraEngine: {self.custom_curaengine}")
        self.log('INFO', f"📝 Build log: {self.log_file}")
        self.log('INFO', f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # Build steps with estimated times
        steps = [
            ("Prerequisites Check", self.check_prerequisites, "1 minute"),
            ("Environment Setup", self.setup_environment, "1 minute"),
            ("Translation Compilation", self.compile_translations, "2 minutes"),
            ("Dependency Installation", self.install_dependencies, "30-60 minutes"),
            ("Cura Build", self.build_cura, "10-30 minutes"),
            ("Package Creation", self.create_package, "10-20 minutes"),
            ("Package Verification", self.verify_package, "1 minute"),
        ]

        if create_dmg:
            steps.append(("DMG Creation", lambda: self.create_dmg(version), "5 minutes"))

        if cleanup:
            steps.append(("Cleanup", self.cleanup_build_artifacts, "1 minute"))

        # Execute build steps
        failed_step = None
        for i, (step_name, step_func, estimated_time) in enumerate(steps, 1):
            self.log('INFO', f"📋 Step {i}/{len(steps)}: {step_name} (estimated: {estimated_time})")
            step_start = time.time()

            try:
                if not step_func():
                    failed_step = step_name
                    break

                step_duration = time.time() - step_start
                self.log('SUCCESS', f"✅ {step_name} completed in {step_duration/60:.1f} minutes")

            except Exception as e:
                self.log('ERROR', f"❌ {step_name} failed with exception: {e}")
                failed_step = step_name
                break

        # Build summary
        total_duration = time.time() - start_time

        if failed_step:
            self.log('ERROR', f"❌ Build failed at step: {failed_step}")
            self.log('ERROR', f"⏰ Total time before failure: {total_duration/60:.1f} minutes")
            self.log('INFO', f"📝 Check the full log at: {self.log_file}")
            return False

        self.log('SUCCESS', "🎉 Complete Cura MacOS package build finished successfully!")
        self.log('SUCCESS', f"⏰ Total build time: {total_duration/60:.1f} minutes")
        self.log('INFO', f"📁 Output directory: {self.dist_dir}")

        # List created files
        self.log('INFO', "📦 Created files:")
        output_files = []
        for pattern in ["*.dmg", "*.app"]:
            for file in self.dist_dir.glob(f"**/{pattern}"):
                file_size = file.stat().st_size / 1024 / 1024  # MB
                self.log('INFO', f"  📄 {file.name} ({file_size:.1f} MB)")
                output_files.append(file)

        if output_files:
            self.log('SUCCESS', f"✅ Successfully created {len(output_files)} output file(s)")

        self.log('INFO', f"📝 Complete build log saved to: {self.log_file}")

        return True

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Complete Cura MacOS Package Builder with Custom CuraEngine",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 build_cura_complete.py                    # Full build with DMG
  python3 build_cura_complete.py --no-dmg          # Build without DMG
  python3 build_cura_complete.py --version 5.11.0  # Specify version
  python3 build_cura_complete.py --no-cleanup      # Keep build artifacts
        """
    )

    parser.add_argument("--version", default="5.11.0-alpha.0",
                       help="Cura version for the package (default: 5.11.0-alpha.0)")
    parser.add_argument("--no-dmg", action="store_true",
                       help="Skip DMG creation, only create .app bundle")
    parser.add_argument("--no-cleanup", action="store_true",
                       help="Skip cleanup of build artifacts")
    parser.add_argument("--log-level", choices=["INFO", "DEBUG"], default="INFO",
                       help="Log level (default: INFO)")

    args = parser.parse_args()

    # Create builder instance
    builder = CuraCompleteBuilder()

    # Print banner
    print("=" * 80)
    print("🚀 Cura MacOS Complete Package Builder")
    print("=" * 80)
    print(f"📍 Project: {builder.project_root}")
    print(f"🔧 CuraEngine: {builder.custom_curaengine}")
    print(f"📦 Version: {args.version}")
    print(f"💿 Create DMG: {'Yes' if not args.no_dmg else 'No'}")
    print(f"🧹 Cleanup: {'Yes' if not args.no_cleanup else 'No'}")
    print(f"📝 Log file: {builder.log_file}")
    print("=" * 80)

    # Start build
    success = builder.build_complete_package(
        version=args.version,
        create_dmg=not args.no_dmg,
        cleanup=not args.no_cleanup
    )

    # Exit with appropriate code
    if success:
        print("\n🎉 Build completed successfully!")
        print(f"📁 Check output in: {builder.dist_dir}")
        sys.exit(0)
    else:
        print("\n❌ Build failed!")
        print(f"📝 Check log file: {builder.log_file}")
        sys.exit(1)

if __name__ == "__main__":
    main()
