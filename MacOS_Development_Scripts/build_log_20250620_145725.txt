[2025-06-20 14:57:25] [INFO] 🚀 Starting Complete Cura MacOS Package Build
[2025-06-20 14:57:25] [INFO] 📍 Project root: /Users/<USER>/PycharmProjects/CuraProject/Cura
[2025-06-20 14:57:25] [INFO] 🔧 Custom CuraEngine: /Users/<USER>/PycharmProjects/CuraProject/CuraEngine/build/Release/CuraEngine
[2025-06-20 14:57:25] [INFO] 📝 Build log: /Users/<USER>/PycharmProjects/CuraProject/Cura/MacOS_Development_Scripts/build_log_20250620_145725.txt
[2025-06-20 14:57:25] [INFO] ⏰ Started at: 2025-06-20 14:57:25
[2025-06-20 14:57:25] [INFO] 📋 Step 1/9: Prerequisites Check (estimated: 1 minute)
[2025-06-20 14:57:25] [INFO] 🔍 Checking prerequisites...
[2025-06-20 14:57:25] [SUCCESS] ✅ Custom CuraEngine found: /Users/<USER>/PycharmProjects/CuraProject/CuraEngine/build/Release/CuraEngine
[2025-06-20 14:57:25] [WARNING] ⚠️  Could not get CuraEngine version
[2025-06-20 14:57:25] [SUCCESS] ✅ conan: Conan version 2.7.0
[2025-06-20 14:57:25] [SUCCESS] ✅ python3: Python 3.13.5
[2025-06-20 14:57:25] [SUCCESS] ✅ msgfmt: msgfmt (GNU gettext-tools) 0.25
[2025-06-20 14:57:25] [SUCCESS] ✅ cmake: cmake version 3.27.9
[2025-06-20 14:57:25] [SUCCESS] ✅ git: git version 2.49.0
[2025-06-20 14:57:25] [ERROR] ❌ hdiutil not working properly
[2025-06-20 14:57:25] [ERROR] ❌ Build failed at step: Prerequisites Check
[2025-06-20 14:57:25] [ERROR] ⏰ Total time before failure: 0.0 minutes
[2025-06-20 14:57:25] [INFO] 📝 Check the full log at: /Users/<USER>/PycharmProjects/CuraProject/Cura/MacOS_Development_Scripts/build_log_20250620_145725.txt
