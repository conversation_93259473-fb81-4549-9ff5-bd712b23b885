[2025-06-20 15:26:42] [INFO] 📦 Installing Conan dependencies (this may take up to 1 hour)...
[2025-06-20 15:26:42] [INFO] 🏗️  Building for architecture: armv8
[2025-06-20 15:26:42] [INFO] ⏰ Starting Conan dependency installation...
[2025-06-20 15:26:42] [INFO] 📝 This process will show detailed logs and may take 30-60 minutes
[2025-06-20 15:26:42] [INFO] Running command: conan install /Users/<USER>/PycharmProjects/CuraProject/Cura --build=missing -s build_type=Release -s arch=armv8 -s os=Macos -v
[2025-06-20 15:26:42] [INFO] Working directory: /Users/<USER>/PycharmProjects/CuraProject/Cura/build_macos_complete
[2025-06-20 15:26:42] [INFO] Timeout: 3600 seconds
[15:26:42] 
[15:26:42] ======== Input profiles ========
[15:26:42] Profile host:
[15:26:42] [settings]
[15:26:42] arch=armv8
[15:26:42] build_type=Release
[15:26:42] compiler=apple-clang
[15:26:42] compiler.cppstd=17
[15:26:42] compiler.libcxx=libc++
[15:26:42] compiler.version=16
[15:26:42] os=Macos
[15:26:42] curaengine*:compiler.cppstd=20
[15:26:42] curaengine_plugin_infill_generate*:compiler.cppstd=20
[15:26:42] curaengine_plugin_gradual_flow*:compiler.cppstd=20
[15:26:42] curaengine_grpc_definitions*:compiler.cppstd=20
[15:26:42] scripta*:compiler.cppstd=20
[15:26:42] umspatial*:compiler.cppstd=20
[15:26:42] dulcificum*:compiler.cppstd=20
[15:26:42] curator/*:compiler.cppstd=20
[15:26:42] [options]
[15:26:42] asio-grpc/*:local_allocator=recycling_allocator
[15:26:42] boost/*:header_only=True
[15:26:42] clipper/*:shared=True
[15:26:42] cpython/*:shared=True
[15:26:42] cpython/*:with_curses=False
[15:26:42] cpython/*:with_tkinter=False
[15:26:42] dulcificum/*:shared=True
[15:26:42] grpc/*:csharp_plugin=False
[15:26:42] grpc/*:node_plugin=False
[15:26:42] grpc/*:objective_c_plugin=False
[15:26:42] grpc/*:php_plugin=False
[15:26:42] grpc/*:python_plugin=False
[15:26:42] grpc/*:ruby_plugin=False
[15:26:42] pyarcus/*:shared=True
[15:26:42] pynest2d/*:shared=True
[15:26:42] pysavitar/*:shared=True
[15:26:42] [conf]
[15:26:42] tools.build:skip_test=True
[15:26:42] tools.cmake.cmaketoolchain:generator=Ninja
[15:26:42] tools.gnu:define_libcxx11_abi=True
[15:26:42] 
[15:26:42] Profile build:
[15:26:42] [settings]
[15:26:42] arch=armv8
[15:26:42] build_type=Release
[15:26:42] compiler=apple-clang
[15:26:42] compiler.cppstd=17
[15:26:42] compiler.libcxx=libc++
[15:26:42] compiler.version=16
[15:26:42] os=Macos
[15:26:42] curator/*:compiler.cppstd=20
[15:26:42] [conf]
[15:26:42] tools.build:skip_test=True
[15:26:42] tools.cmake.cmaketoolchain:generator=Ninja
[15:26:42] tools.gnu:define_libcxx11_abi=True
[15:26:42] 
[15:26:42] 
[15:26:42] ======== Computing dependency graph ========
[15:26:42] Graph root
[15:26:42] conanfile.py (cura/5.11.0-alpha.0): /Users/<USER>/PycharmProjects/CuraProject/Cura/conanfile.py
[15:26:42] Requirements
[15:26:42] abseil/20230802.1#f0f91485b111dc9837a68972cb19ca7b - Cache
[15:26:42] arcus/5.10.0#2eaae9c834c26c7010c5f8183c1f4b8b - Cache
[15:26:42] asio-grpc/2.9.2#a44093413738811a2159b761c1bf1039 - Cache
[15:26:42] boost/1.86.0#b389df94f8fa0e15145bc66322ca0e82 - Cache
[15:26:42] bzip2/1.0.8#00b4a4658791c1f06914e087f0e792f5 - Cache
[15:26:42] c-ares/1.34.5#b78b91e7cfb1f11ce777a285bbf169c6 - Cache
[15:26:42] clipper/6.4.2@ultimaker/stable#95d9dd0b845ba2b4337f9a3fd331fa47 - Cache
[15:26:42] cpython/3.12.2#68cb44d6d7eeb24578b1c942ce16a8cd - Cache
[15:26:42] ctre/3.7.2#86bd3592feebcdafd2ab7b8a1aad0c80 - Cache
[15:26:42] cura_binary_data/5.11.0-alpha.0@ultimaker/testing#760976726c555ed281aa0b64cdf893db - Cache
[15:26:42] cura_resources/5.11.0-alpha.0@ultimaker/testing#204961382ff539e78d9c1635e4460a40 - Cache
[15:26:42] curaengine/5.11.0-alpha.0@ultimaker/testing#d0baec6b2c74bc442cc56805aad23d0f - Cache
[15:26:42] curaengine_grpc_definitions/0.3.1#b4a8063639b85fa65e8132cc29872383 - Cache
[15:26:42] dulcificum/5.10.0#d7796f570134346c8cf8a45b2b677d63 - Cache
[15:26:42] expat/2.7.1#b0b67ba910c5147271b444139ca06953 - Cache
[15:26:42] fdm_materials/5.11.0-alpha.0@ultimaker/testing#b12456ad8f9d9464e1d5db569cacef3c - Cache
[15:26:42] fmt/11.1.3#8364f0feb23ee32e4b870455edb552ae - Cache
[15:26:42] gdbm/1.23#e33e95cba20c006ae47466e2770cd984 - Cache
[15:26:42] grpc/1.54.3#8d094817df26303aa6cb74eaaea622d2 - Cache
[15:26:42] libffi/3.4.4#a1442e924f14664d9545dfcfe66d751f - Cache
[15:26:42] libxcrypt/4.4.36#4b4e8f20794f1997dd59eeed0b7cdcfb - Cache
[15:26:42] mapbox-geometry/2.0.3#******************************** - Cache
[15:26:42] mapbox-variant/1.2.0#******************************** - Cache
[15:26:42] mapbox-wagyu/0.5.0@ultimaker/stable#******************************** - Cache
[15:26:42] mpdecimal/2.5.0#4e67f881cf85c2df1095f7abd756b12f - Cache
[15:26:42] neargye-semver/0.3.0#799b2e371d5be985a073214e029ea6cd - Cache
[15:26:42] nest2d/5.10.0#6f430e9fa21c308e0e6234649a6116ed - Cache
[15:26:42] nlohmann_json/3.11.2#1ded6ae5d200a68ac17c51d528b945e2 - Cache
[15:26:42] nlopt/2.7.1#61296485856e44d13b39346ef54325f1 - Cache
[15:26:42] openssl/3.5.0#62d21a127982d5ddc42a83cffc147a4b - Cache
[15:26:42] protobuf/3.21.12#d927114e28de9f4691a6bbcdd9a529d1 - Cache
[15:26:42] pugixml/1.14#c6afdcf73d71858303d8260b0d76ff91 - Cache
[15:26:42] pyarcus/5.10.0#45dd0835e9e65d080faa27e5669a957f - Cache
[15:26:42] pybind11/2.11.1#9ac24fa2b6323656659eaf4e44fb7e0b - Cache
[15:26:42] pynest2d/5.10.0#ff5d39339d68c1cb296f83563e3eeacd - Cache
[15:26:42] pysavitar/5.11.0-alpha.0#683aa6a69435b316536c272b7807b883 - Cache
[15:26:42] range-v3/0.12.0#4c05d91d7b40e6b91b44b5345ac64408 - Cache
[15:26:42] rapidjson/cci.20230929#8dc0392af2b3aaea7312095f0ba53467 - Cache
[15:26:42] re2/20230301#dfd6e2bf050eb90ddd8729cfb4c844a4 - Cache
[15:26:42] savitar/5.11.0-alpha.0#3a382967d301e3aae78b23a7f9080012 - Cache
[15:26:42] scripta/1.1.0-alpha.0+b96045@ultimaker/testing#3786da67f9aa951719291aaab18d0785 - Cache
[15:26:42] spdlog/1.15.1#92e99f07f134481bce4b70c1a41060e7 - Cache
[15:26:42] sqlite3/3.45.2#60f2d3278e7bc12c8ef02ac75119c137 - Cache
[15:26:42] stb/cci.20230920#ed79bd361e974a99137f214efb117eef - Cache
[15:26:42] uranium/5.11.0-alpha.0@ultimaker/testing#68489362930d2886b7965de68739f7e8 - Cache
[15:26:42] xz_utils/5.4.5#b885d1d79c9d30cff3803f7f551dbe66 - Cache
[15:26:42] zlib/1.3.1#b8bc2603263cf7eccbd6e17e66b0ed76 - Cache
[15:26:42] Test requirements
[15:26:42] sipbuildtool/0.3.0#262477bdb72ff6a09737695673de2b62 - Cache
[15:26:42] standardprojectsettings/0.2.0#af70631ee980187032a76e136b293365 - Cache
[15:26:42] standardprojectsettings/0.2.0@ultimaker/stable#af70631ee980187032a76e136b293365 - Cache
[15:26:42] Build requirements
[15:26:42] autoconf/2.71#51077f068e61700d65bb05541ea1e4b0 - Cache
[15:26:42] automake/1.16.5#058bda3e21c36c9aa8425daf3c1faf50 - Cache
[15:26:42] bison/3.8.2#ed1ba0c42d2ab7ab64fc3a62e9ecc673 - Cache
[15:26:42] cmake/3.31.7#57c3e118bcf267552c0ea3f8bee1e7d5 - Cache
[15:26:42] flex/2.6.4#e35bc44b3fcbcd661e0af0dc5b5b1ad4 - Cache
[15:26:42] gettext/0.22.5#909a6ca9b6d4062e9b6ccf25c8461cda - Cache
[15:26:42] gnu-config/cci.20210814#69fde734e1a46fd1655b4af46ab40945 - Cache
[15:26:42] libiconv/1.17#1ae2f60ab5d08de1643a22a81b360c59 - Cache
[15:26:42] libtool/2.4.7#a182d7ce8d4c346a19dbd4a5d532ef68 - Cache
[15:26:42] m4/1.4.19#b38ced39a01e31fef5435bc634461fd2 - Cache
[15:26:42] meson/1.2.2#21b73818ba96d9eea465b310b5bbc993 - Cache
[15:26:42] ninja/1.12.1#fd583651bf0c6a901943495d49878803 - Cache
[15:26:42] pkgconf/2.1.0#27f44583701117b571307cf5b5fe5605 - Cache
[15:26:42] protobuf/3.21.12#d927114e28de9f4691a6bbcdd9a529d1 - Cache
[15:26:42] zlib/1.3.1#b8bc2603263cf7eccbd6e17e66b0ed76 - Cache
[15:26:42] Python requires
[15:26:42] npmpackage/1.1.0#4ee756f0e6532594bc38577aab07344a - Cache
[15:26:42] pyprojecttoolchain/0.2.0#d0d0d74876a9061a767bb2153c7952e3 - Cache
[15:26:42] sentrylibrary/1.0.0#004cb2aaa533fb28697dd9a302d652e8 - Cache
[15:26:42] sipbuildtool/0.3.0#262477bdb72ff6a09737695673de2b62 - Cache
[15:26:42] translationextractor/2.3.0#d504876a4742c1b92bcd6e1d5ba7509a - Cache
[15:26:42] Resolved version ranges
[15:26:42] abseil/[>=20230125.3 <=20230802.1]: abseil/20230802.1
[15:26:42] c-ares/[>=1.19.1 <2]: c-ares/1.34.5
[15:26:42] cmake/[>=3.25 <4]: cmake/3.31.7
[15:26:42] expat/[>=2.6.2 <3]: expat/2.7.1
[15:26:42] ninja/[>=1.10.2 <2]: ninja/1.12.1
[15:26:42] npmpackage/[>=1.0.0]: npmpackage/1.1.0
[15:26:42] openssl/[>=1.1 <4]: openssl/3.5.0
[15:26:42] pyprojecttoolchain/[>=0.2.0]: pyprojecttoolchain/0.2.0
[15:26:42] scripta/[>=1.1.0]@ultimaker/testing: scripta/1.1.0-alpha.0+b96045@ultimaker/testing
[15:26:42] sipbuildtool/[>=0.3.0]: sipbuildtool/0.3.0
[15:26:42] standardprojectsettings/[>=0.1.0]: standardprojectsettings/0.2.0
[15:26:42] standardprojectsettings/[>=0.2.0]: standardprojectsettings/0.2.0
[15:26:42] standardprojectsettings/[>=0.2.0]@ultimaker/stable: standardprojectsettings/0.2.0@ultimaker/stable
[15:26:42] translationextractor/[>=2.2.0]: translationextractor/2.3.0
[15:26:42] zlib/[>=1.2.11 <2]: zlib/1.3.1
[15:26:42] Overrides
[15:26:42] grpc/1.67.1: ['grpc/1.54.3']
[15:26:42] 
[15:26:42] ======== Computing necessary packages ========
[15:26:42] range-v3/0.12.0: WARN: apple-clang 16 support for range-v3 is unknown, assuming it is supported.
[15:26:47] Requirements
[15:26:47] abseil/20230802.1#f0f91485b111dc9837a68972cb19ca7b:499251790bf857231d9df29608a5e574ebb58b74#e58f5a676f0c04a4d74396609d67179e - Skip
[15:26:47] arcus/5.10.0#2eaae9c834c26c7010c5f8183c1f4b8b:2061f14db170435d1f10813a542bcb5697d31296#d13244c77d92548766d5abd6aff63183 - Cache
[15:26:47] asio-grpc/2.9.2#a44093413738811a2159b761c1bf1039:961db76d88f17792fac56ff31488b67885f21476#7d9f114a4b45b107848f90a976f5c538 - Skip
[15:26:47] boost/1.86.0#b389df94f8fa0e15145bc66322ca0e82:da39a3ee5e6b4b0d3255bfef95601890afd80709#8932d10a88896f0bc49cde93c0045d3e - Skip
[15:26:47] bzip2/1.0.8#00b4a4658791c1f06914e087f0e792f5:1b365626531995eab090d0d1b1d474dd9a356923#d702a76aec80422d630980e15565c133 - Skip
[15:26:47] c-ares/1.34.5#b78b91e7cfb1f11ce777a285bbf169c6:af04bc7c6bf6b6cfa4e3817c71889be42c6bfd01#bac61df70de00b34968e6e253548bb39 - Skip
[15:26:47] clipper/6.4.2@ultimaker/stable#95d9dd0b845ba2b4337f9a3fd331fa47:8e1203cdae4e8d016d34eaa424b8fba9a539ef0f#fc4593737fda698716f0239c17a2bdc0 - Cache
[15:26:47] cpython/3.12.2#68cb44d6d7eeb24578b1c942ce16a8cd:02a982a86b368bcb4f3ae495cfd34d438a73222e#7488e1ab0bbfdcd22fc3fe9deb7e3cc8 - Cache
[15:26:47] ctre/3.7.2#86bd3592feebcdafd2ab7b8a1aad0c80:da39a3ee5e6b4b0d3255bfef95601890afd80709#a42728bed0e6742bba8e9f978ebaca8a - Cache
[15:26:47] cura_binary_data/5.11.0-alpha.0@ultimaker/testing#760976726c555ed281aa0b64cdf893db:22cfd8ebee61e57e7cd803dea6de4b73b2266e64#c9984939b175868194a0cff3e3a8debb - Cache
[15:26:47] cura_resources/5.11.0-alpha.0@ultimaker/testing#204961382ff539e78d9c1635e4460a40:da39a3ee5e6b4b0d3255bfef95601890afd80709#bd255a35c18985545665e9b1d0acd217 - Cache
[15:26:47] curaengine/5.11.0-alpha.0@ultimaker/testing#d0baec6b2c74bc442cc56805aad23d0f:ad80d51452ce3048bd17dc8a04044e0c6867bb8c#683fd59af145d5a0f7acf4867f1cec0d - Cache
[15:26:47] curaengine_grpc_definitions/0.3.1#b4a8063639b85fa65e8132cc29872383:92de44899d23f49aa3c0025b60d08f0af4c36871#d872b4c26ca909fe89a9df2f16cf59a0 - Skip
[15:26:47] dulcificum/5.10.0#d7796f570134346c8cf8a45b2b677d63:90eacffdccf3e2fd9a2174dd5e1e3ec05a08a354#5274ccf001135b966000033f1d04f6bb - Cache
[15:26:47] expat/2.7.1#b0b67ba910c5147271b444139ca06953:a9cbbd17d34397fcf6331c0fe8783b1dc331a29a#e96c5ba3518e972a3d86bc969984078a - Skip
[15:26:47] fdm_materials/5.11.0-alpha.0@ultimaker/testing#b12456ad8f9d9464e1d5db569cacef3c:da39a3ee5e6b4b0d3255bfef95601890afd80709#a804b57beecad14bcd83016beb2d1f66 - Cache
[15:26:47] fmt/11.1.3#8364f0feb23ee32e4b870455edb552ae:8376d1402632cae5a7ce2d0de208560458160b2e#ac68bcc07476858dfdcea83ae69e707a - Cache
[15:26:47] gdbm/1.23#e33e95cba20c006ae47466e2770cd984:6d78f72486dd133e77d57b2d0886f3ad60c0bd17#2655174980cd580200699f5bc2984232 - Skip
[15:26:47] grpc/1.54.3#8d094817df26303aa6cb74eaaea622d2:02f362a28787370c7bd78f7b43d6e1711c744dab#7cb59e71aef865c7c6e6af556e9012d2 - Skip
[15:26:47] libffi/3.4.4#a1442e924f14664d9545dfcfe66d751f:405e382fa7fb6368c140a7f5c5cd71c32b009653#5a9e6b0fbede820350b0b359b13da23d - Skip
[15:26:47] libxcrypt/4.4.36#4b4e8f20794f1997dd59eeed0b7cdcfb:405e382fa7fb6368c140a7f5c5cd71c32b009653#868fb927f0e4bcfddcb17c07490143a5 - Skip
[15:26:47] mapbox-geometry/2.0.3#********************************:da39a3ee5e6b4b0d3255bfef95601890afd80709#b2b6166a216462f17c02e1c4bebbd942 - Skip
[15:26:47] mapbox-variant/1.2.0#********************************:da39a3ee5e6b4b0d3255bfef95601890afd80709#3526db39b7b7a80e939cc264b9df29c6 - Skip
[15:26:47] mapbox-wagyu/0.5.0@ultimaker/stable#********************************:da39a3ee5e6b4b0d3255bfef95601890afd80709#9c99eaae4d7ee99d627e4533142e2336 - Skip
[15:26:47] mpdecimal/2.5.0#4e67f881cf85c2df1095f7abd756b12f:f087e25c79ba161b4c16b765b97ef3fc5831a934#40384ebabe8364ab79f916c246e2e5e7 - Skip
[15:26:47] neargye-semver/0.3.0#799b2e371d5be985a073214e029ea6cd:da39a3ee5e6b4b0d3255bfef95601890afd80709#09b44697fa4ef5f27606e5eb1da16bcf - Skip
[15:26:47] nest2d/5.10.0#6f430e9fa21c308e0e6234649a6116ed:793c3017cb1ddc8f7421dac8fb54248c4df274d2#4c2a1276212c9f35e63ee5e902e7b2e5 - Cache
[15:26:47] nlohmann_json/3.11.2#1ded6ae5d200a68ac17c51d528b945e2:da39a3ee5e6b4b0d3255bfef95601890afd80709#2d1a5b1f5d673e1dab536bed20ce000b - Cache
[15:26:47] nlopt/2.7.1#61296485856e44d13b39346ef54325f1:1b9d2e9bd89cb9de97b6976e644e49076a838fe7#9a82b8fa1155db1ab4dc52a19f569ab8 - Cache
[15:26:47] openssl/3.5.0#62d21a127982d5ddc42a83cffc147a4b:7ae3c973da08c7a4091a723ab10651cc4c5d5a8b#ccefde40421646ddcdd075e369965fae - Skip
[15:26:47] protobuf/3.21.12#d927114e28de9f4691a6bbcdd9a529d1:a8a4bf2f8ee5d8730973141eef8e7d1e0cce7cdb#af0ab4b702b050a6c6c48f6eac6ff033 - Skip
[15:26:47] pugixml/1.14#c6afdcf73d71858303d8260b0d76ff91:e3dfce0fadec9f45391f86bf227cced4cae5628c#0711562c59863264f6ecba6efdc519a4 - Skip
[15:26:47] pyarcus/5.10.0#45dd0835e9e65d080faa27e5669a957f:dd8258d4ebdf6f045e1bfa2fa06a61361b90ff71#93b7bb1e675fde36d29815dd4c006a49 - Cache
[15:26:47] pybind11/2.11.1#9ac24fa2b6323656659eaf4e44fb7e0b:da39a3ee5e6b4b0d3255bfef95601890afd80709#6e4692644a05d1d1622e4fec74091232 - Skip
[15:26:47] pynest2d/5.10.0#ff5d39339d68c1cb296f83563e3eeacd:df171adae765fe7f94576e32a258393efb4ce1c4#49a99c1faffac3703553bee464974413 - Cache
[15:26:47] pysavitar/5.11.0-alpha.0#683aa6a69435b316536c272b7807b883:e33b2cb6a61cff82be5ac1ebca6a548e0aded3cc#13071d9485b8fdb2d98dc1cfe5136e01 - Cache
[15:26:47] range-v3/0.12.0#4c05d91d7b40e6b91b44b5345ac64408:da39a3ee5e6b4b0d3255bfef95601890afd80709#ecc6172c3cd6694c36d1cd98a702deb0 - Cache
[15:26:47] rapidjson/cci.20230929#8dc0392af2b3aaea7312095f0ba53467:da39a3ee5e6b4b0d3255bfef95601890afd80709#bc6124f3dda366933f5ac97f53b76b7b - Skip
[15:26:47] re2/20230301#dfd6e2bf050eb90ddd8729cfb4c844a4:99ecdde09c8c1fd4ed315a6227a2b3a12938bc0c#1d320a4d272e356396920ecfe195d0d0 - Skip
[15:26:47] savitar/5.11.0-alpha.0#3a382967d301e3aae78b23a7f9080012:5f60e39224b2b628a5e9f7acc6af2b6e701a9c11#e78eb86a1ffb35f788718676b35c1397 - Cache
[15:26:47] scripta/1.1.0-alpha.0+b96045@ultimaker/testing#3786da67f9aa951719291aaab18d0785:da39a3ee5e6b4b0d3255bfef95601890afd80709#d2d84c4ec705cbfa88d3313ea69b4b2e - Skip
[15:26:47] spdlog/1.15.1#92e99f07f134481bce4b70c1a41060e7:32385fd54998d7c22c8b2147037b7446fd04237b#bffcfd808b1660341cf4a1eccd16066d - Cache
[15:26:47] sqlite3/3.45.2#60f2d3278e7bc12c8ef02ac75119c137:8f92fe369fe33b57a9b2cd78c69ed7c84b04c7a0#60e8bc428b17ac585570619a57760893 - Skip
[15:26:47] stb/cci.20230920#ed79bd361e974a99137f214efb117eef:24b381c7532f70c284a2a67cc83f779b3c0042fb#72af46794853f74751a3ba685e1c7ef4 - Skip
[15:26:47] uranium/5.11.0-alpha.0@ultimaker/testing#68489362930d2886b7965de68739f7e8:da39a3ee5e6b4b0d3255bfef95601890afd80709#4421cc583c48e20925217cd40d0bfc89 - Cache
[15:26:47] xz_utils/5.4.5#b885d1d79c9d30cff3803f7f551dbe66:405e382fa7fb6368c140a7f5c5cd71c32b009653#40665f632c8cf4b8df801aef70edb555 - Skip
[15:26:47] zlib/1.3.1#b8bc2603263cf7eccbd6e17e66b0ed76:405e382fa7fb6368c140a7f5c5cd71c32b009653#695eacd1030489a6a96b412dd00919d9 - Skip
[15:26:47] Test requirements
[15:26:47] sipbuildtool/0.3.0#262477bdb72ff6a09737695673de2b62:da39a3ee5e6b4b0d3255bfef95601890afd80709#3e8dce2119ddc1074e3863c15e8d63ff - Skip
[15:26:47] standardprojectsettings/0.2.0#af70631ee980187032a76e136b293365:da39a3ee5e6b4b0d3255bfef95601890afd80709#11a0ba14a3eaa94059396f4af59df2c5 - Skip
[15:26:47] standardprojectsettings/0.2.0@ultimaker/stable#af70631ee980187032a76e136b293365:da39a3ee5e6b4b0d3255bfef95601890afd80709#11a0ba14a3eaa94059396f4af59df2c5 - Skip
[15:26:47] Build requirements
[15:26:47] autoconf/2.71#51077f068e61700d65bb05541ea1e4b0:9e5323c65b94ae38c3c733fe12637776db0119a5#c693b1ceb6f260246f50440ff7544168 - Skip
[15:26:47] automake/1.16.5#058bda3e21c36c9aa8425daf3c1faf50:9e5323c65b94ae38c3c733fe12637776db0119a5#53e724e4596a55dd2b2a3931d36256dc - Skip
[15:26:47] bison/3.8.2#ed1ba0c42d2ab7ab64fc3a62e9ecc673:5c848829c28c0080c8fdf627371bd7038aabd597#84df50cc9e58932022e833f5a9542abb - Skip
[15:26:47] cmake/3.31.7#57c3e118bcf267552c0ea3f8bee1e7d5:9e5323c65b94ae38c3c733fe12637776db0119a5#6e371ac3a9122681a3a516e4b35ddec5 - Skip
[15:26:47] flex/2.6.4#e35bc44b3fcbcd661e0af0dc5b5b1ad4:405e382fa7fb6368c140a7f5c5cd71c32b009653#490cb57ea6100afc3bc4437f6ef981e2 - Skip
[15:26:47] gettext/0.22.5#909a6ca9b6d4062e9b6ccf25c8461cda:42cdd0ebc69c5b84e47f600e0096e0353f036885#ef88b0277576949bd10af0467ff1c9d4 - Cache
[15:26:47] gnu-config/cci.20210814#69fde734e1a46fd1655b4af46ab40945:da39a3ee5e6b4b0d3255bfef95601890afd80709#22618e30bd9e326eb95e824dc90cc860 - Skip
[15:26:47] libiconv/1.17#1ae2f60ab5d08de1643a22a81b360c59:405e382fa7fb6368c140a7f5c5cd71c32b009653#9a42f5a432bf2128469c4fbec768d9bc - Skip
[15:26:47] libtool/2.4.7#a182d7ce8d4c346a19dbd4a5d532ef68:405e382fa7fb6368c140a7f5c5cd71c32b009653#2d183ddca682d6abdc80dbd4ae0f5c8b - Skip
[15:26:47] m4/1.4.19#b38ced39a01e31fef5435bc634461fd2:617cae191537b47386c088e07b1822d8606b7e67#af3bb664b82c4f616d3146625c5b4bd5 - Skip
[15:26:47] meson/1.2.2#21b73818ba96d9eea465b310b5bbc993:da39a3ee5e6b4b0d3255bfef95601890afd80709#97f4a23dd2d942f83e5344b1ca496ce7 - Skip
[15:26:47] ninja/1.12.1#fd583651bf0c6a901943495d49878803:617cae191537b47386c088e07b1822d8606b7e67#64e4f9c8e6ed8c3448c93db771b94ecc - Skip
[15:26:47] pkgconf/2.1.0#27f44583701117b571307cf5b5fe5605:df7e47c8f0b96c79c977dd45ec51a050d8380273#0e4a349206e0319ddfe0a13932c26b03 - Skip
[15:26:47] protobuf/3.21.12#d927114e28de9f4691a6bbcdd9a529d1:a8a4bf2f8ee5d8730973141eef8e7d1e0cce7cdb#af0ab4b702b050a6c6c48f6eac6ff033 - Skip
[15:26:47] zlib/1.3.1#b8bc2603263cf7eccbd6e17e66b0ed76:405e382fa7fb6368c140a7f5c5cd71c32b009653#695eacd1030489a6a96b412dd00919d9 - Skip
[15:26:47] 
[15:26:47] ======== Installing packages ========
[15:26:47] clipper/6.4.2@ultimaker/stable: Already installed! (1 of 21)
[15:26:47] ctre/3.7.2: Already installed! (2 of 21)
[15:26:47] cura_binary_data/5.11.0-alpha.0@ultimaker/testing: Already installed! (3 of 21)
[15:26:47] cura_resources/5.11.0-alpha.0@ultimaker/testing: Already installed! (4 of 21)
[15:26:47] fdm_materials/5.11.0-alpha.0@ultimaker/testing: Already installed! (5 of 21)
[15:26:47] fmt/11.1.3: Already installed! (6 of 21)
[15:26:47] nlohmann_json/3.11.2: Already installed! (7 of 21)
[15:26:47] nlopt/2.7.1: Already installed! (8 of 21)
[15:26:47] range-v3/0.12.0: Already installed! (9 of 21)
[15:26:47] savitar/5.11.0-alpha.0: Already installed! (10 of 21)
[15:26:47] arcus/5.10.0: Already installed! (11 of 21)
[15:26:47] gettext/0.22.5: Already installed! (12 of 21)
[15:26:47] gettext/0.22.5: WARN: The use of 'unix_path_legacy_compat' is deprecated in Conan 2.0 and does not perform path conversions. This is retained for compatibility with Conan 1.x and will be removed in a future version.
[15:26:47] gettext/0.22.5: WARN: The use of 'unix_path_legacy_compat' is deprecated in Conan 2.0 and does not perform path conversions. This is retained for compatibility with Conan 1.x and will be removed in a future version.
[15:26:47] cpython/3.12.2: Already installed! (13 of 21)
[15:26:47] cpython/3.12.2: Appending PATH environment variable: /Users/<USER>/.conan2/p/b/cpyth58b7a2dcfc351/p/bin
[15:26:47] cpython/3.12.2: Appending PYTHON environment variable: /Users/<USER>/.conan2/p/b/cpyth58b7a2dcfc351/p/bin/python3.12
[15:26:47] cpython/3.12.2: Setting PYTHON_ROOT environment variable: /Users/<USER>/.conan2/p/b/cpyth58b7a2dcfc351/p
[15:26:47] nest2d/5.10.0: Already installed! (14 of 21)
[15:26:47] spdlog/1.15.1: Already installed! (15 of 21)
[15:26:47] pyarcus/5.10.0: Already installed! (16 of 21)
[15:26:47] pysavitar/5.11.0-alpha.0: Already installed! (17 of 21)
[15:26:47] curaengine/5.11.0-alpha.0@ultimaker/testing: Already installed! (18 of 21)
[15:26:47] dulcificum/5.10.0: Already installed! (19 of 21)
[15:26:47] pynest2d/5.10.0: Already installed! (20 of 21)
[15:26:47] uranium/5.11.0-alpha.0@ultimaker/testing: Already installed! (21 of 21)
[15:26:47] WARN: deprecated: Usage of deprecated Conan 1.X features that will be removed in Conan 2.X:
[15:26:47] WARN: deprecated:     'cpp_info.names' used in: range-v3/0.12.0, nlopt/2.7.1
[15:26:47] WARN: deprecated:     'env_info' used in: cpython/3.12.2, gettext/0.22.5
[15:26:47] WARN: deprecated:     'user_info' used in: cpython/3.12.2
[15:26:47] 
[15:26:47] ======== Finalizing install (deploy, generators) ========
[15:26:47] conanfile.py (cura/5.11.0-alpha.0): Writing generators to /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators
[15:26:47] conanfile.py (cura/5.11.0-alpha.0): Generator 'VirtualPythonEnv' calling 'generate()'
[15:26:47] conanfile.py (cura/5.11.0-alpha.0): Using Python interpreter '/Users/<USER>/.conan2/p/b/cpyth58b7a2dcfc351/p/bin/python3.12' to create Virtual Environment in '/Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/cura_venv'
[15:26:48] Requirement already satisfied: pip in ./cura_venv/lib/python3.12/site-packages (25.1.1)
[15:26:51] Requirement already satisfied: wheel in ./cura_venv/lib/python3.12/site-packages (0.37.1)
[15:26:51] Requirement already satisfied: setuptools in ./cura_venv/lib/python3.12/site-packages (75.6.0)
[15:26:52] conanfile.py (cura/5.11.0-alpha.0): Generating pip requirements file at '/Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_basic.txt'
[15:26:52] conanfile.py (cura/5.11.0-alpha.0): Generating pip requirements file at '/Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt'
[15:26:52] conanfile.py (cura/5.11.0-alpha.0): Generating pip requirements file at '/Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_dev_basic.txt'
[15:26:52] conanfile.py (cura/5.11.0-alpha.0): Generating pip requirements file at '/Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_installer_basic.txt'
[15:26:52] conanfile.py (cura/5.11.0-alpha.0): Generating pip requirements summary at '/Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_summary.yml'
[15:26:52] conanfile.py (cura/5.11.0-alpha.0): Installing pip requirements from /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_basic.txt
[15:26:52] Collecting charon@ git+https://github.com/ultimaker/libcharon@master/s-line#egg=charon (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_basic.txt (line 1))
[15:26:52] Cloning https://github.com/ultimaker/libcharon (to revision master/s-line) to /private/var/folders/qc/jnt15c412ls9zryfnnd7bc_r0000gn/T/pip-install-axmqvc88/charon_1876a261473f485286cabfdead772280
[15:26:52] Running command git clone --filter=blob:none --quiet https://github.com/ultimaker/libcharon /private/var/folders/qc/jnt15c412ls9zryfnnd7bc_r0000gn/T/pip-install-axmqvc88/charon_1876a261473f485286cabfdead772280
[15:26:57] Running command git checkout -q b275d32cd2be7261194b6770cbd524717332e674
[15:26:59] Resolved https://github.com/ultimaker/libcharon to commit b275d32cd2be7261194b6770cbd524717332e674
[15:26:59] Preparing metadata (setup.py): started
[15:26:59] Preparing metadata (setup.py): finished with status 'done'
[15:26:59] conanfile.py (cura/5.11.0-alpha.0): Installing pip requirements from /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt
[15:27:00] Requirement already satisfied: certifi==2023.5.7 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 1)) (2023.5.7)
[15:27:00] Requirement already satisfied: zeroconf==0.31.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 2)) (0.31.0)
[15:27:00] Requirement already satisfied: importlib-metadata==4.10.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 3)) (4.10.0)
[15:27:00] Requirement already satisfied: trimesh==3.9.36 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 4)) (3.9.36)
[15:27:00] Requirement already satisfied: setuptools==75.6.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 5)) (75.6.0)
[15:27:00] Requirement already satisfied: sentry-sdk==0.13.5 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 6)) (0.13.5)
[15:27:00] Requirement already satisfied: pyserial==3.4 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 7)) (3.4)
[15:27:00] Requirement already satisfied: chardet==3.0.4 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 8)) (3.0.4)
[15:27:00] Requirement already satisfied: idna==2.8 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 9)) (2.8)
[15:27:00] Requirement already satisfied: attrs==21.3.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 10)) (21.3.0)
[15:27:00] Requirement already satisfied: requests==2.32.3 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 11)) (2.32.3)
[15:27:00] Requirement already satisfied: twisted==21.2.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 12)) (21.2.0)
[15:27:00] Requirement already satisfied: constantly==15.1.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 13)) (15.1.0)
[15:27:00] Requirement already satisfied: hyperlink==21.0.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 14)) (21.0.0)
[15:27:00] Requirement already satisfied: incremental==22.10.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 15)) (22.10.0)
[15:27:00] Requirement already satisfied: zope.interface==5.4.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 16)) (5.4.0)
[15:27:00] Requirement already satisfied: automat==20.2.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 17)) (20.2.0)
[15:27:00] Requirement already satisfied: shapely==2.0.6 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 18)) (2.0.6)
[15:27:00] Requirement already satisfied: cython==0.29.26 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 19)) (0.29.26)
[15:27:00] Requirement already satisfied: pybind11==2.6.2 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 20)) (2.6.2)
[15:27:00] Requirement already satisfied: wheel==0.37.1 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 21)) (0.37.1)
[15:27:00] Requirement already satisfied: ifaddr==0.1.7 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 22)) (0.1.7)
[15:27:00] Requirement already satisfied: pycparser==2.22 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 23)) (2.22)
[15:27:00] Requirement already satisfied: zipp==3.5.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 24)) (3.5.0)
[15:27:00] Requirement already satisfied: urllib3==2.2.3 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 25)) (2.2.3)
[15:27:00] Requirement already satisfied: jeepney==0.8.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 26)) (0.8.0)
[15:27:00] Requirement already satisfied: SecretStorage==3.3.3 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 27)) (3.3.3)
[15:27:00] Requirement already satisfied: keyring==25.5.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 28)) (25.5.0)
[15:27:00] Requirement already satisfied: jaraco.classes==3.4.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 29)) (3.4.0)
[15:27:00] Requirement already satisfied: jaraco.functools==4.1.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 30)) (4.1.0)
[15:27:00] Requirement already satisfied: jaraco.context==6.0.1 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 31)) (6.0.1)
[15:27:00] Requirement already satisfied: more_itertools==10.5.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 32)) (10.5.0)
[15:27:00] Requirement already satisfied: charset-normalizer==2.1.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 33)) (2.1.0)
[15:27:00] Requirement already satisfied: pynavlib==0.9.4 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 34)) (0.9.4)
[15:27:00] Requirement already satisfied: numpy==1.26.4 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 35)) (1.26.4)
[15:27:00] Requirement already satisfied: PyQt6==6.6.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 36)) (6.6.0)
[15:27:00] Requirement already satisfied: PyQt6-Qt6==6.6.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 37)) (6.6.0)
[15:27:00] Requirement already satisfied: PyQt6-sip==13.6.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 38)) (13.6.0)
[15:27:00] Requirement already satisfied: cffi==1.17.1 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 39)) (1.17.1)
[15:27:00] Requirement already satisfied: colorlog==6.6.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 40)) (6.6.0)
[15:27:00] Requirement already satisfied: cryptography==44.0.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 41)) (44.0.0)
[15:27:00] Requirement already satisfied: mypy==0.931 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 42)) (0.931)
[15:27:00] Requirement already satisfied: mypy-extensions==0.4.3 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 43)) (0.4.3)
[15:27:00] Requirement already satisfied: networkx==2.6.2 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 44)) (2.6.2)
[15:27:00] Requirement already satisfied: numpy-stl==2.10.1 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 45)) (2.10.1)
[15:27:00] Requirement already satisfied: pyclipper==1.3.0.post5 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 46)) (1.3.0.post5)
[15:27:00] Requirement already satisfied: python-utils==2.3.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 47)) (2.3.0)
[15:27:00] Requirement already satisfied: scipy==1.11.3 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 48)) (1.11.3)
[15:27:00] Requirement already satisfied: six==1.16.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 49)) (1.16.0)
[15:27:00] Requirement already satisfied: tomli==2.0.1 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 50)) (2.0.1)
[15:27:00] Requirement already satisfied: typing-extensions==4.3.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 51)) (4.3.0)
[15:27:00] conanfile.py (cura/5.11.0-alpha.0): Calling generate()
[15:27:00] conanfile.py (cura/5.11.0-alpha.0): Generators folder: /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators
[15:27:00] conanfile.py (cura/5.11.0-alpha.0): Write CuraVersion.py to /Users/<USER>/PycharmProjects/CuraProject/Cura
[15:27:00] conanfile.py (cura/5.11.0-alpha.0): Collecting conan installs
[15:27:00] conanfile.py (cura/5.11.0-alpha.0): Collecting python installs
[15:27:00] conanfile.py (cura/5.11.0-alpha.0): RUN: python collect_python_installs.py
[15:27:00] 
[15:27:00] conanfile.py (cura/5.11.0-alpha.0): Retrieving license for cura
[15:27:05] ERROR: conanfile.py (cura/5.11.0-alpha.0): Error in generate() method, line 594
[15:27:05] self._generate_cura_version(str(Path(self.source_folder, "cura")))
[15:27:05] while calling '_generate_cura_version', line 355
[15:27:05] dependencies_description=self._dependencies_description(),
[15:27:05] while calling '_dependencies_description', line 312
[15:27:05] self._make_conan_dependency_description(dependency, dependencies)
[15:27:05] while calling '_make_conan_dependency_description', line 283
[15:27:05] dependency_description["license_full"] = self._get_license_from_repository(source_url, str(dependency.ref.version))
[15:27:05] while calling '_get_license_from_repository', line 238
[15:27:05] repo = Repo.clone_from(git_url, clone_dir, depth=1, no_checkout=True)
[15:27:05] GitCommandError: Cmd('git') failed due to: exit code(128)
[15:27:05] cmdline: git clone -v --depth=1 --no-checkout -- https://github.com/Ultimaker/cura.git /var/folders/qc/jnt15c412ls9zryfnnd7bc_r0000gn/T/tmplhj0i9ji
[15:27:05] stderr: 'Cloning into '/var/folders/qc/jnt15c412ls9zryfnnd7bc_r0000gn/T/tmplhj0i9ji'...
[15:27:05] POST git-upload-pack (182 bytes)
[15:27:05] POST git-upload-pack (251 bytes)
[15:27:05] error: RPC failed; curl 18 Transferred a partial file
[15:27:05] fatal: early EOF
[15:27:05] fatal: fetch-pack: invalid index-pack output
[15:27:05] '
[2025-06-20 15:27:05] [ERROR] Command failed with return code 1
