# Cura MacOS Development Scripts

这是一套完整的MacOS开发脚本，用于构建包含自定义CuraEngine的Cura MacOS安装包。

## 🎯 特性

- **一键构建**: 运行一个脚本即可完成从源代码到安装包的全过程
- **自定义CuraEngine**: 自动使用您编译的CuraEngine
- **详细日志**: 1小时超时限制，完整的构建日志输出
- **跨平台兼容**: 支持MacOS和Windows开发环境
- **标准化流程**: 可重复的构建过程

## 📁 文件结构

```
MacOS_Development_Scripts/
├── README.md                    # 本文档
├── build_cura_complete.py      # 主构建脚本 (推荐使用)
├── cura_engine_config.py       # 跨平台CuraEngine配置
├── compile_translations.sh     # 翻译编译脚本
├── quick_test.py               # 环境快速测试
└── build_log_YYYYMMDD_HHMMSS.txt  # 构建日志 (自动生成)
```

## 🚀 快速开始

### 1. 环境检查

首先运行快速测试确保环境准备就绪：

```bash
cd MacOS_Development_Scripts
python3 quick_test.py
```

### 2. 完整构建

运行主构建脚本创建完整的MacOS安装包：

```bash
python3 build_cura_complete.py
```

这将执行以下步骤：
1. ✅ 检查先决条件 (1分钟)
2. ✅ 设置环境 (1分钟)  
3. ✅ 编译翻译文件 (2分钟)
4. ✅ 安装依赖 (30-60分钟) - **Conan构建，支持1小时超时**
5. ✅ 构建Cura (10-30分钟)
6. ✅ 创建安装包 (10-20分钟)
7. ✅ 验证安装包 (1分钟)
8. ✅ 创建DMG (5分钟)
9. ✅ 清理构建文件 (1分钟)

### 3. 构建选项

```bash
# 指定版本
python3 build_cura_complete.py --version 5.11.0-custom

# 只构建应用程序，不创建DMG
python3 build_cura_complete.py --no-dmg

# 保留构建文件，不清理
python3 build_cura_complete.py --no-cleanup

# 查看所有选项
python3 build_cura_complete.py --help
```

## 📋 先决条件

### 必需工具

确保以下工具已安装：

```bash
# 使用Homebrew安装
brew install conan cmake gettext git

# 验证Python 3
python3 --version
```

### CuraEngine编译

确保您的自定义CuraEngine已编译：

```bash
cd /Users/<USER>/PycharmProjects/CuraProject/CuraEngine
cmake -B build -DCMAKE_BUILD_TYPE=Release
cmake --build build --config Release
```

编译完成后，CuraEngine应该位于：
`/Users/<USER>/PycharmProjects/CuraProject/CuraEngine/build/Release/CuraEngine`

## 📦 输出文件

构建完成后，您将在项目根目录的 `dist_macos_complete/` 中找到：

- `UltiMaker-Cura-{version}-MacOS-{arch}-{timestamp}.dmg` - MacOS安装包
- `UltiMaker-Cura.app` - 应用程序包 (在dist子目录中)

## 🔧 配置

### CuraEngine路径配置

如果您的CuraEngine在不同位置，编辑 `cura_engine_config.py`：

```python
# 修改MacOS项目根路径
MACOS_PROJECT_ROOT = Path("您的项目路径")
```

### 构建脚本配置

编辑 `build_cura_complete.py` 中的路径：

```python
# 修改CuraEngine路径
self.custom_curaengine = Path("您的CuraEngine路径")
```

## 🐛 故障排除

### 常见问题

1. **CuraEngine未找到**
   ```
   ❌ Custom CuraEngine not found
   ```
   - 确保CuraEngine已编译
   - 检查路径配置是否正确

2. **Conan超时**
   ```
   ❌ Command timed out after 3600 seconds
   ```
   - 网络问题，重新运行构建
   - 清理Conan缓存：`conan remove "*" --confirm`

3. **工具缺失**
   ```
   ❌ Required tool not found: msgfmt
   ```
   - 安装缺失工具：`brew install gettext`

### 调试步骤

1. **运行环境测试**
   ```bash
   python3 quick_test.py
   ```

2. **查看详细日志**
   ```bash
   # 日志文件会自动生成
   tail -f build_log_YYYYMMDD_HHMMSS.txt
   ```

3. **分步构建**
   ```bash
   # 只构建应用程序
   python3 build_cura_complete.py --no-dmg --no-cleanup
   ```

## 🔄 开发工作流

### 日常开发

1. 修改Cura源代码
2. 如果修改了CuraEngine，重新编译：
   ```bash
   cd /Users/<USER>/PycharmProjects/CuraProject/CuraEngine
   cmake --build build --config Release
   ```
3. 运行构建脚本：
   ```bash
   cd MacOS_Development_Scripts
   python3 build_cura_complete.py
   ```

### 发布准备

1. 确保所有翻译都是最新的
2. 运行完整构建测试
3. 创建带版本号的安装包：
   ```bash
   python3 build_cura_complete.py --version 5.11.0-release
   ```

## 🌐 跨平台开发

### 文件命名兼容性

- ✅ 使用小写文件名
- ✅ 避免特殊字符
- ✅ 使用正斜杠(/)作为路径分隔符

### Windows开发

如果在Windows上开发，请：

1. 调整 `cura_engine_config.py` 中的Windows路径
2. 确保CuraEngine编译为 `.exe` 文件
3. 使用相同的构建脚本

## 📝 日志和监控

### 构建日志

每次构建都会生成详细日志：
- 文件名：`build_log_YYYYMMDD_HHMMSS.txt`
- 包含：所有命令输出、错误信息、时间戳
- 位置：`MacOS_Development_Scripts/` 目录

### 实时监控

构建过程中可以实时查看日志：

```bash
# 在另一个终端窗口中
tail -f MacOS_Development_Scripts/build_log_*.txt
```

## 🎯 高级用法

### 自定义构建配置

您可以修改 `build_cura_complete.py` 来：
- 调整超时时间
- 添加自定义构建步骤
- 修改输出目录
- 添加额外的验证步骤

### 集成到CI/CD

这些脚本可以集成到自动化构建系统中：

```bash
#!/bin/bash
# CI构建脚本示例
cd MacOS_Development_Scripts
python3 quick_test.py && python3 build_cura_complete.py --version $BUILD_VERSION
```

## 📞 支持

如果遇到问题：

1. 首先运行 `python3 quick_test.py` 诊断环境
2. 检查构建日志中的详细错误信息
3. 确保所有先决条件都已满足
4. 验证CuraEngine编译是否成功

---

**注意**: 这套脚本专为您的开发环境定制，包含了自定义CuraEngine路径和跨平台兼容性考虑。
