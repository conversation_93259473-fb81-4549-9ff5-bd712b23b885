[2025-06-20 14:57:47] [INFO] 🚀 Starting Complete Cura MacOS Package Build
[2025-06-20 14:57:47] [INFO] 📍 Project root: /Users/<USER>/PycharmProjects/CuraProject/Cura
[2025-06-20 14:57:47] [INFO] 🔧 Custom CuraEngine: /Users/<USER>/PycharmProjects/CuraProject/CuraEngine/build/Release/CuraEngine
[2025-06-20 14:57:47] [INFO] 📝 Build log: /Users/<USER>/PycharmProjects/CuraProject/Cura/MacOS_Development_Scripts/build_log_20250620_145747.txt
[2025-06-20 14:57:47] [INFO] ⏰ Started at: 2025-06-20 14:57:47
[2025-06-20 14:57:47] [INFO] 📋 Step 1/9: Prerequisites Check (estimated: 1 minute)
[2025-06-20 14:57:47] [INFO] 🔍 Checking prerequisites...
[2025-06-20 14:57:47] [SUCCESS] ✅ Custom CuraEngine found: /Users/<USER>/PycharmProjects/CuraProject/CuraEngine/build/Release/CuraEngine
[2025-06-20 14:57:47] [WARNING] ⚠️  Could not get CuraEngine version
[2025-06-20 14:57:47] [SUCCESS] ✅ conan: Conan version 2.7.0
[2025-06-20 14:57:47] [SUCCESS] ✅ python3: Python 3.13.5
[2025-06-20 14:57:47] [SUCCESS] ✅ msgfmt: msgfmt (GNU gettext-tools) 0.25
[2025-06-20 14:57:47] [SUCCESS] ✅ cmake: cmake version 3.27.9
[2025-06-20 14:57:47] [SUCCESS] ✅ git: git version 2.49.0
[2025-06-20 14:57:47] [SUCCESS] ✅ hdiutil: 
[2025-06-20 14:57:47] [INFO] 🏗️  Building for architecture: arm64
[2025-06-20 14:57:47] [SUCCESS] ✅ Prerequisites Check completed in 0.0 minutes
[2025-06-20 14:57:47] [INFO] 📋 Step 2/9: Environment Setup (estimated: 1 minute)
[2025-06-20 14:57:47] [INFO] 🏗️  Setting up build environment...
[2025-06-20 14:57:47] [SUCCESS] ✅ Build directory: /Users/<USER>/PycharmProjects/CuraProject/Cura/build_macos_complete
[2025-06-20 14:57:47] [SUCCESS] ✅ Distribution directory: /Users/<USER>/PycharmProjects/CuraProject/Cura/dist_macos_complete
[2025-06-20 14:57:47] [SUCCESS] ✅ Copied CuraEngine to: /Users/<USER>/PycharmProjects/CuraProject/Cura/CuraEngine
[2025-06-20 14:57:47] [SUCCESS] ✅ Environment Setup completed in 0.0 minutes
[2025-06-20 14:57:47] [INFO] 📋 Step 3/9: Translation Compilation (estimated: 2 minutes)
[2025-06-20 14:57:47] [INFO] 🌐 Compiling translations...
[2025-06-20 14:57:47] [INFO] Running command: /Users/<USER>/PycharmProjects/CuraProject/Cura/MacOS_Development_Scripts/compile_translations.sh
[2025-06-20 14:57:47] [INFO] Working directory: /Users/<USER>/PycharmProjects/CuraProject/Cura
[2025-06-20 14:57:47] [INFO] Timeout: 300 seconds
[14:57:48] Compiling translation files...
[14:57:48] Translation compilation completed!
[14:57:48] 
[14:57:48] Available languages with compiled translations:
[14:57:48] cs_CZ (       3 translation files)
[14:57:48] de_DE (       3 translation files)
[14:57:48] es_ES (       3 translation files)
[14:57:48] fi_FI (       3 translation files)
[14:57:48] fr_FR (       3 translation files)
[14:57:48] hu_HU (       3 translation files)
[14:57:48] it_IT (       3 translation files)
[14:57:48] ja_JP (       3 translation files)
[14:57:48] ko_KR (       3 translation files)
[14:57:48] nl_NL (       3 translation files)
[14:57:48] pl_PL (       3 translation files)
[14:57:48] pt_BR (       3 translation files)
[14:57:48] pt_PT (       3 translation files)
[14:57:48] ru_RU (       3 translation files)
[14:57:48] tr_TR (       3 translation files)
[14:57:48] zh_CN (       3 translation files)
[14:57:48] zh_TW (       3 translation files)
[2025-06-20 14:57:48] [SUCCESS] Command completed successfully
[2025-06-20 14:57:48] [SUCCESS] ✅ Translation Compilation completed in 0.0 minutes
[2025-06-20 14:57:48] [INFO] 📋 Step 4/9: Dependency Installation (estimated: 30-60 minutes)
[2025-06-20 14:57:48] [INFO] 📦 Installing Conan dependencies (this may take up to 1 hour)...
[2025-06-20 14:57:48] [INFO] 🏗️  Building for architecture: armv8
[2025-06-20 14:57:48] [INFO] ⏰ Starting Conan dependency installation...
[2025-06-20 14:57:48] [INFO] 📝 This process will show detailed logs and may take 30-60 minutes
[2025-06-20 14:57:48] [INFO] Running command: conan install /Users/<USER>/PycharmProjects/CuraProject/Cura --build=missing -s build_type=Release -s arch=armv8 -s os=Macos -v
[2025-06-20 14:57:48] [INFO] Working directory: /Users/<USER>/PycharmProjects/CuraProject/Cura/build_macos_complete
[2025-06-20 14:57:48] [INFO] Timeout: 3600 seconds
[14:57:48] 
[14:57:48] ======== Input profiles ========
[14:57:48] Profile host:
[14:57:48] [settings]
[14:57:48] arch=armv8
[14:57:48] build_type=Release
[14:57:48] compiler=apple-clang
[14:57:48] compiler.cppstd=17
[14:57:48] compiler.libcxx=libc++
[14:57:48] compiler.version=16
[14:57:48] os=Macos
[14:57:48] curaengine*:compiler.cppstd=20
[14:57:48] curaengine_plugin_infill_generate*:compiler.cppstd=20
[14:57:48] curaengine_plugin_gradual_flow*:compiler.cppstd=20
[14:57:48] curaengine_grpc_definitions*:compiler.cppstd=20
[14:57:48] scripta*:compiler.cppstd=20
[14:57:48] umspatial*:compiler.cppstd=20
[14:57:48] dulcificum*:compiler.cppstd=20
[14:57:48] curator/*:compiler.cppstd=20
[14:57:48] [options]
[14:57:48] asio-grpc/*:local_allocator=recycling_allocator
[14:57:48] boost/*:header_only=True
[14:57:48] clipper/*:shared=True
[14:57:48] cpython/*:shared=True
[14:57:48] cpython/*:with_curses=False
[14:57:48] cpython/*:with_tkinter=False
[14:57:48] dulcificum/*:shared=True
[14:57:48] grpc/*:csharp_plugin=False
[14:57:48] grpc/*:node_plugin=False
[14:57:48] grpc/*:objective_c_plugin=False
[14:57:48] grpc/*:php_plugin=False
[14:57:48] grpc/*:python_plugin=False
[14:57:48] grpc/*:ruby_plugin=False
[14:57:48] pyarcus/*:shared=True
[14:57:48] pynest2d/*:shared=True
[14:57:48] pysavitar/*:shared=True
[14:57:48] [conf]
[14:57:48] tools.build:skip_test=True
[14:57:48] tools.cmake.cmaketoolchain:generator=Ninja
[14:57:48] tools.gnu:define_libcxx11_abi=True
[14:57:48] 
[14:57:48] Profile build:
[14:57:48] [settings]
[14:57:48] arch=armv8
[14:57:48] build_type=Release
[14:57:48] compiler=apple-clang
[14:57:48] compiler.cppstd=17
[14:57:48] compiler.libcxx=libc++
[14:57:48] compiler.version=16
[14:57:48] os=Macos
[14:57:48] curator/*:compiler.cppstd=20
[14:57:48] [conf]
[14:57:48] tools.build:skip_test=True
[14:57:48] tools.cmake.cmaketoolchain:generator=Ninja
[14:57:48] tools.gnu:define_libcxx11_abi=True
[14:57:48] 
[14:57:48] 
[14:57:48] ======== Computing dependency graph ========
[14:57:49] Graph root
[14:57:49] conanfile.py (cura/5.11.0-alpha.0): /Users/<USER>/PycharmProjects/CuraProject/Cura/conanfile.py
[14:57:49] Requirements
[14:57:49] abseil/20230802.1#f0f91485b111dc9837a68972cb19ca7b - Cache
[14:57:49] arcus/5.10.0#2eaae9c834c26c7010c5f8183c1f4b8b - Cache
[14:57:49] asio-grpc/2.9.2#a44093413738811a2159b761c1bf1039 - Cache
[14:57:49] boost/1.86.0#b389df94f8fa0e15145bc66322ca0e82 - Cache
[14:57:49] bzip2/1.0.8#00b4a4658791c1f06914e087f0e792f5 - Cache
[14:57:49] c-ares/1.34.5#b78b91e7cfb1f11ce777a285bbf169c6 - Cache
[14:57:49] clipper/6.4.2@ultimaker/stable#95d9dd0b845ba2b4337f9a3fd331fa47 - Cache
[14:57:49] cpython/3.12.2#68cb44d6d7eeb24578b1c942ce16a8cd - Cache
[14:57:49] ctre/3.7.2#86bd3592feebcdafd2ab7b8a1aad0c80 - Cache
[14:57:49] cura_binary_data/5.11.0-alpha.0@ultimaker/testing#760976726c555ed281aa0b64cdf893db - Cache
[14:57:49] cura_resources/5.11.0-alpha.0@ultimaker/testing#204961382ff539e78d9c1635e4460a40 - Cache
[14:57:49] curaengine/5.11.0-alpha.0@ultimaker/testing#d0baec6b2c74bc442cc56805aad23d0f - Cache
[14:57:49] curaengine_grpc_definitions/0.3.1#b4a8063639b85fa65e8132cc29872383 - Cache
[14:57:49] dulcificum/5.10.0#d7796f570134346c8cf8a45b2b677d63 - Cache
[14:57:49] expat/2.7.1#b0b67ba910c5147271b444139ca06953 - Cache
[14:57:49] fdm_materials/5.11.0-alpha.0@ultimaker/testing#b12456ad8f9d9464e1d5db569cacef3c - Cache
[14:57:49] fmt/11.1.3#8364f0feb23ee32e4b870455edb552ae - Cache
[14:57:49] gdbm/1.23#e33e95cba20c006ae47466e2770cd984 - Cache
[14:57:49] grpc/1.54.3#8d094817df26303aa6cb74eaaea622d2 - Cache
[14:57:49] libffi/3.4.4#a1442e924f14664d9545dfcfe66d751f - Cache
[14:57:49] libxcrypt/4.4.36#4b4e8f20794f1997dd59eeed0b7cdcfb - Cache
[14:57:49] mapbox-geometry/2.0.3#******************************** - Cache
[14:57:49] mapbox-variant/1.2.0#******************************** - Cache
[14:57:49] mapbox-wagyu/0.5.0@ultimaker/stable#******************************** - Cache
[14:57:49] mpdecimal/2.5.0#4e67f881cf85c2df1095f7abd756b12f - Cache
[14:57:49] neargye-semver/0.3.0#799b2e371d5be985a073214e029ea6cd - Cache
[14:57:49] nest2d/5.10.0#6f430e9fa21c308e0e6234649a6116ed - Cache
[14:57:49] nlohmann_json/3.11.2#1ded6ae5d200a68ac17c51d528b945e2 - Cache
[14:57:49] nlopt/2.7.1#61296485856e44d13b39346ef54325f1 - Cache
[14:57:49] openssl/3.5.0#62d21a127982d5ddc42a83cffc147a4b - Cache
[14:57:49] protobuf/3.21.12#d927114e28de9f4691a6bbcdd9a529d1 - Cache
[14:57:49] pugixml/1.14#c6afdcf73d71858303d8260b0d76ff91 - Cache
[14:57:49] pyarcus/5.10.0#45dd0835e9e65d080faa27e5669a957f - Cache
[14:57:49] pybind11/2.11.1#9ac24fa2b6323656659eaf4e44fb7e0b - Cache
[14:57:49] pynest2d/5.10.0#ff5d39339d68c1cb296f83563e3eeacd - Cache
[14:57:49] pysavitar/5.11.0-alpha.0#683aa6a69435b316536c272b7807b883 - Cache
[14:57:49] range-v3/0.12.0#4c05d91d7b40e6b91b44b5345ac64408 - Cache
[14:57:49] rapidjson/cci.20230929#8dc0392af2b3aaea7312095f0ba53467 - Cache
[14:57:49] re2/20230301#dfd6e2bf050eb90ddd8729cfb4c844a4 - Cache
[14:57:49] savitar/5.11.0-alpha.0#3a382967d301e3aae78b23a7f9080012 - Cache
[14:57:49] scripta/1.1.0-alpha.0+b96045@ultimaker/testing#3786da67f9aa951719291aaab18d0785 - Cache
[14:57:49] spdlog/1.15.1#92e99f07f134481bce4b70c1a41060e7 - Cache
[14:57:49] sqlite3/3.45.2#60f2d3278e7bc12c8ef02ac75119c137 - Cache
[14:57:49] stb/cci.20230920#ed79bd361e974a99137f214efb117eef - Cache
[14:57:49] uranium/5.11.0-alpha.0@ultimaker/testing#68489362930d2886b7965de68739f7e8 - Cache
[14:57:49] xz_utils/5.4.5#b885d1d79c9d30cff3803f7f551dbe66 - Cache
[14:57:49] zlib/1.3.1#b8bc2603263cf7eccbd6e17e66b0ed76 - Cache
[14:57:49] Test requirements
[14:57:49] sipbuildtool/0.3.0#262477bdb72ff6a09737695673de2b62 - Cache
[14:57:49] standardprojectsettings/0.2.0#af70631ee980187032a76e136b293365 - Cache
[14:57:49] standardprojectsettings/0.2.0@ultimaker/stable#af70631ee980187032a76e136b293365 - Cache
[14:57:49] Build requirements
[14:57:49] autoconf/2.71#51077f068e61700d65bb05541ea1e4b0 - Cache
[14:57:49] automake/1.16.5#058bda3e21c36c9aa8425daf3c1faf50 - Cache
[14:57:49] bison/3.8.2#ed1ba0c42d2ab7ab64fc3a62e9ecc673 - Cache
[14:57:49] cmake/3.31.7#57c3e118bcf267552c0ea3f8bee1e7d5 - Cache
[14:57:49] flex/2.6.4#e35bc44b3fcbcd661e0af0dc5b5b1ad4 - Cache
[14:57:49] gettext/0.22.5#909a6ca9b6d4062e9b6ccf25c8461cda - Cache
[14:57:49] gnu-config/cci.20210814#69fde734e1a46fd1655b4af46ab40945 - Cache
[14:57:49] libiconv/1.17#1ae2f60ab5d08de1643a22a81b360c59 - Cache
[14:57:49] libtool/2.4.7#a182d7ce8d4c346a19dbd4a5d532ef68 - Cache
[14:57:49] m4/1.4.19#b38ced39a01e31fef5435bc634461fd2 - Cache
[14:57:49] meson/1.2.2#21b73818ba96d9eea465b310b5bbc993 - Cache
[14:57:49] ninja/1.12.1#fd583651bf0c6a901943495d49878803 - Cache
[14:57:49] pkgconf/2.1.0#27f44583701117b571307cf5b5fe5605 - Cache
[14:57:49] protobuf/3.21.12#d927114e28de9f4691a6bbcdd9a529d1 - Cache
[14:57:49] zlib/1.3.1#b8bc2603263cf7eccbd6e17e66b0ed76 - Cache
[14:57:49] Python requires
[14:57:49] npmpackage/1.1.0#4ee756f0e6532594bc38577aab07344a - Cache
[14:57:49] pyprojecttoolchain/0.2.0#d0d0d74876a9061a767bb2153c7952e3 - Cache
[14:57:49] sentrylibrary/1.0.0#004cb2aaa533fb28697dd9a302d652e8 - Cache
[14:57:49] sipbuildtool/0.3.0#262477bdb72ff6a09737695673de2b62 - Cache
[14:57:49] translationextractor/2.3.0#d504876a4742c1b92bcd6e1d5ba7509a - Cache
[14:57:49] Resolved version ranges
[14:57:49] abseil/[>=20230125.3 <=20230802.1]: abseil/20230802.1
[14:57:49] c-ares/[>=1.19.1 <2]: c-ares/1.34.5
[14:57:49] cmake/[>=3.25 <4]: cmake/3.31.7
[14:57:49] expat/[>=2.6.2 <3]: expat/2.7.1
[14:57:49] ninja/[>=1.10.2 <2]: ninja/1.12.1
[14:57:49] npmpackage/[>=1.0.0]: npmpackage/1.1.0
[14:57:49] openssl/[>=1.1 <4]: openssl/3.5.0
[14:57:49] pyprojecttoolchain/[>=0.2.0]: pyprojecttoolchain/0.2.0
[14:57:49] scripta/[>=1.1.0]@ultimaker/testing: scripta/1.1.0-alpha.0+b96045@ultimaker/testing
[14:57:49] sipbuildtool/[>=0.3.0]: sipbuildtool/0.3.0
[14:57:49] standardprojectsettings/[>=0.1.0]: standardprojectsettings/0.2.0
[14:57:49] standardprojectsettings/[>=0.2.0]: standardprojectsettings/0.2.0
[14:57:49] standardprojectsettings/[>=0.2.0]@ultimaker/stable: standardprojectsettings/0.2.0@ultimaker/stable
[14:57:49] translationextractor/[>=2.2.0]: translationextractor/2.3.0
[14:57:49] zlib/[>=1.2.11 <2]: zlib/1.3.1
[14:57:49] Overrides
[14:57:49] grpc/1.67.1: ['grpc/1.54.3']
[14:57:49] 
[14:57:49] ======== Computing necessary packages ========
[14:57:49] range-v3/0.12.0: WARN: apple-clang 16 support for range-v3 is unknown, assuming it is supported.
[14:57:52] Requirements
[14:57:52] abseil/20230802.1#f0f91485b111dc9837a68972cb19ca7b:499251790bf857231d9df29608a5e574ebb58b74#e58f5a676f0c04a4d74396609d67179e - Skip
[14:57:52] arcus/5.10.0#2eaae9c834c26c7010c5f8183c1f4b8b:2061f14db170435d1f10813a542bcb5697d31296#d13244c77d92548766d5abd6aff63183 - Cache
[14:57:52] asio-grpc/2.9.2#a44093413738811a2159b761c1bf1039:961db76d88f17792fac56ff31488b67885f21476#7d9f114a4b45b107848f90a976f5c538 - Skip
[14:57:52] boost/1.86.0#b389df94f8fa0e15145bc66322ca0e82:da39a3ee5e6b4b0d3255bfef95601890afd80709#8932d10a88896f0bc49cde93c0045d3e - Skip
[14:57:52] bzip2/1.0.8#00b4a4658791c1f06914e087f0e792f5:1b365626531995eab090d0d1b1d474dd9a356923#d702a76aec80422d630980e15565c133 - Skip
[14:57:52] c-ares/1.34.5#b78b91e7cfb1f11ce777a285bbf169c6:af04bc7c6bf6b6cfa4e3817c71889be42c6bfd01#bac61df70de00b34968e6e253548bb39 - Skip
[14:57:52] clipper/6.4.2@ultimaker/stable#95d9dd0b845ba2b4337f9a3fd331fa47:8e1203cdae4e8d016d34eaa424b8fba9a539ef0f#fc4593737fda698716f0239c17a2bdc0 - Cache
[14:57:52] cpython/3.12.2#68cb44d6d7eeb24578b1c942ce16a8cd:02a982a86b368bcb4f3ae495cfd34d438a73222e#7488e1ab0bbfdcd22fc3fe9deb7e3cc8 - Cache
[14:57:52] ctre/3.7.2#86bd3592feebcdafd2ab7b8a1aad0c80:da39a3ee5e6b4b0d3255bfef95601890afd80709#a42728bed0e6742bba8e9f978ebaca8a - Cache
[14:57:52] cura_binary_data/5.11.0-alpha.0@ultimaker/testing#760976726c555ed281aa0b64cdf893db:22cfd8ebee61e57e7cd803dea6de4b73b2266e64#c9984939b175868194a0cff3e3a8debb - Cache
[14:57:52] cura_resources/5.11.0-alpha.0@ultimaker/testing#204961382ff539e78d9c1635e4460a40:da39a3ee5e6b4b0d3255bfef95601890afd80709#bd255a35c18985545665e9b1d0acd217 - Cache
[14:57:52] curaengine/5.11.0-alpha.0@ultimaker/testing#d0baec6b2c74bc442cc56805aad23d0f:ad80d51452ce3048bd17dc8a04044e0c6867bb8c#683fd59af145d5a0f7acf4867f1cec0d - Cache
[14:57:52] curaengine_grpc_definitions/0.3.1#b4a8063639b85fa65e8132cc29872383:92de44899d23f49aa3c0025b60d08f0af4c36871#d872b4c26ca909fe89a9df2f16cf59a0 - Skip
[14:57:52] dulcificum/5.10.0#d7796f570134346c8cf8a45b2b677d63:90eacffdccf3e2fd9a2174dd5e1e3ec05a08a354#5274ccf001135b966000033f1d04f6bb - Cache
[14:57:52] expat/2.7.1#b0b67ba910c5147271b444139ca06953:a9cbbd17d34397fcf6331c0fe8783b1dc331a29a#e96c5ba3518e972a3d86bc969984078a - Skip
[14:57:52] fdm_materials/5.11.0-alpha.0@ultimaker/testing#b12456ad8f9d9464e1d5db569cacef3c:da39a3ee5e6b4b0d3255bfef95601890afd80709#a804b57beecad14bcd83016beb2d1f66 - Cache
[14:57:52] fmt/11.1.3#8364f0feb23ee32e4b870455edb552ae:8376d1402632cae5a7ce2d0de208560458160b2e#ac68bcc07476858dfdcea83ae69e707a - Cache
[14:57:52] gdbm/1.23#e33e95cba20c006ae47466e2770cd984:6d78f72486dd133e77d57b2d0886f3ad60c0bd17#2655174980cd580200699f5bc2984232 - Skip
[14:57:52] grpc/1.54.3#8d094817df26303aa6cb74eaaea622d2:02f362a28787370c7bd78f7b43d6e1711c744dab#7cb59e71aef865c7c6e6af556e9012d2 - Skip
[14:57:52] libffi/3.4.4#a1442e924f14664d9545dfcfe66d751f:405e382fa7fb6368c140a7f5c5cd71c32b009653#5a9e6b0fbede820350b0b359b13da23d - Skip
[14:57:52] libxcrypt/4.4.36#4b4e8f20794f1997dd59eeed0b7cdcfb:405e382fa7fb6368c140a7f5c5cd71c32b009653#868fb927f0e4bcfddcb17c07490143a5 - Skip
[14:57:52] mapbox-geometry/2.0.3#********************************:da39a3ee5e6b4b0d3255bfef95601890afd80709#b2b6166a216462f17c02e1c4bebbd942 - Skip
[14:57:52] mapbox-variant/1.2.0#********************************:da39a3ee5e6b4b0d3255bfef95601890afd80709#3526db39b7b7a80e939cc264b9df29c6 - Skip
[14:57:52] mapbox-wagyu/0.5.0@ultimaker/stable#********************************:da39a3ee5e6b4b0d3255bfef95601890afd80709#9c99eaae4d7ee99d627e4533142e2336 - Skip
[14:57:52] mpdecimal/2.5.0#4e67f881cf85c2df1095f7abd756b12f:f087e25c79ba161b4c16b765b97ef3fc5831a934#40384ebabe8364ab79f916c246e2e5e7 - Skip
[14:57:52] neargye-semver/0.3.0#799b2e371d5be985a073214e029ea6cd:da39a3ee5e6b4b0d3255bfef95601890afd80709#09b44697fa4ef5f27606e5eb1da16bcf - Skip
[14:57:52] nest2d/5.10.0#6f430e9fa21c308e0e6234649a6116ed:793c3017cb1ddc8f7421dac8fb54248c4df274d2#4c2a1276212c9f35e63ee5e902e7b2e5 - Cache
[14:57:52] nlohmann_json/3.11.2#1ded6ae5d200a68ac17c51d528b945e2:da39a3ee5e6b4b0d3255bfef95601890afd80709#2d1a5b1f5d673e1dab536bed20ce000b - Cache
[14:57:52] nlopt/2.7.1#61296485856e44d13b39346ef54325f1:1b9d2e9bd89cb9de97b6976e644e49076a838fe7#9a82b8fa1155db1ab4dc52a19f569ab8 - Cache
[14:57:52] openssl/3.5.0#62d21a127982d5ddc42a83cffc147a4b:7ae3c973da08c7a4091a723ab10651cc4c5d5a8b#ccefde40421646ddcdd075e369965fae - Skip
[14:57:52] protobuf/3.21.12#d927114e28de9f4691a6bbcdd9a529d1:a8a4bf2f8ee5d8730973141eef8e7d1e0cce7cdb#af0ab4b702b050a6c6c48f6eac6ff033 - Skip
[14:57:52] pugixml/1.14#c6afdcf73d71858303d8260b0d76ff91:e3dfce0fadec9f45391f86bf227cced4cae5628c#0711562c59863264f6ecba6efdc519a4 - Skip
[14:57:52] pyarcus/5.10.0#45dd0835e9e65d080faa27e5669a957f:dd8258d4ebdf6f045e1bfa2fa06a61361b90ff71#93b7bb1e675fde36d29815dd4c006a49 - Cache
[14:57:52] pybind11/2.11.1#9ac24fa2b6323656659eaf4e44fb7e0b:da39a3ee5e6b4b0d3255bfef95601890afd80709#6e4692644a05d1d1622e4fec74091232 - Skip
[14:57:52] pynest2d/5.10.0#ff5d39339d68c1cb296f83563e3eeacd:df171adae765fe7f94576e32a258393efb4ce1c4#49a99c1faffac3703553bee464974413 - Cache
[14:57:52] pysavitar/5.11.0-alpha.0#683aa6a69435b316536c272b7807b883:e33b2cb6a61cff82be5ac1ebca6a548e0aded3cc#13071d9485b8fdb2d98dc1cfe5136e01 - Cache
[14:57:52] range-v3/0.12.0#4c05d91d7b40e6b91b44b5345ac64408:da39a3ee5e6b4b0d3255bfef95601890afd80709#ecc6172c3cd6694c36d1cd98a702deb0 - Cache
[14:57:52] rapidjson/cci.20230929#8dc0392af2b3aaea7312095f0ba53467:da39a3ee5e6b4b0d3255bfef95601890afd80709#bc6124f3dda366933f5ac97f53b76b7b - Skip
[14:57:52] re2/20230301#dfd6e2bf050eb90ddd8729cfb4c844a4:99ecdde09c8c1fd4ed315a6227a2b3a12938bc0c#1d320a4d272e356396920ecfe195d0d0 - Skip
[14:57:52] savitar/5.11.0-alpha.0#3a382967d301e3aae78b23a7f9080012:5f60e39224b2b628a5e9f7acc6af2b6e701a9c11#e78eb86a1ffb35f788718676b35c1397 - Cache
[14:57:52] scripta/1.1.0-alpha.0+b96045@ultimaker/testing#3786da67f9aa951719291aaab18d0785:da39a3ee5e6b4b0d3255bfef95601890afd80709#d2d84c4ec705cbfa88d3313ea69b4b2e - Skip
[14:57:52] spdlog/1.15.1#92e99f07f134481bce4b70c1a41060e7:32385fd54998d7c22c8b2147037b7446fd04237b#bffcfd808b1660341cf4a1eccd16066d - Cache
[14:57:52] sqlite3/3.45.2#60f2d3278e7bc12c8ef02ac75119c137:8f92fe369fe33b57a9b2cd78c69ed7c84b04c7a0#60e8bc428b17ac585570619a57760893 - Skip
[14:57:52] stb/cci.20230920#ed79bd361e974a99137f214efb117eef:24b381c7532f70c284a2a67cc83f779b3c0042fb#72af46794853f74751a3ba685e1c7ef4 - Skip
[14:57:52] uranium/5.11.0-alpha.0@ultimaker/testing#68489362930d2886b7965de68739f7e8:da39a3ee5e6b4b0d3255bfef95601890afd80709#4421cc583c48e20925217cd40d0bfc89 - Cache
[14:57:52] xz_utils/5.4.5#b885d1d79c9d30cff3803f7f551dbe66:405e382fa7fb6368c140a7f5c5cd71c32b009653#40665f632c8cf4b8df801aef70edb555 - Skip
[14:57:52] zlib/1.3.1#b8bc2603263cf7eccbd6e17e66b0ed76:405e382fa7fb6368c140a7f5c5cd71c32b009653#695eacd1030489a6a96b412dd00919d9 - Skip
[14:57:52] Test requirements
[14:57:52] sipbuildtool/0.3.0#262477bdb72ff6a09737695673de2b62:da39a3ee5e6b4b0d3255bfef95601890afd80709#3e8dce2119ddc1074e3863c15e8d63ff - Skip
[14:57:52] standardprojectsettings/0.2.0#af70631ee980187032a76e136b293365:da39a3ee5e6b4b0d3255bfef95601890afd80709#11a0ba14a3eaa94059396f4af59df2c5 - Skip
[14:57:52] standardprojectsettings/0.2.0@ultimaker/stable#af70631ee980187032a76e136b293365:da39a3ee5e6b4b0d3255bfef95601890afd80709#11a0ba14a3eaa94059396f4af59df2c5 - Skip
[14:57:52] Build requirements
[14:57:52] autoconf/2.71#51077f068e61700d65bb05541ea1e4b0:9e5323c65b94ae38c3c733fe12637776db0119a5#c693b1ceb6f260246f50440ff7544168 - Skip
[14:57:52] automake/1.16.5#058bda3e21c36c9aa8425daf3c1faf50:9e5323c65b94ae38c3c733fe12637776db0119a5#53e724e4596a55dd2b2a3931d36256dc - Skip
[14:57:52] bison/3.8.2#ed1ba0c42d2ab7ab64fc3a62e9ecc673:5c848829c28c0080c8fdf627371bd7038aabd597#84df50cc9e58932022e833f5a9542abb - Skip
[14:57:52] cmake/3.31.7#57c3e118bcf267552c0ea3f8bee1e7d5:9e5323c65b94ae38c3c733fe12637776db0119a5#6e371ac3a9122681a3a516e4b35ddec5 - Skip
[14:57:52] flex/2.6.4#e35bc44b3fcbcd661e0af0dc5b5b1ad4:405e382fa7fb6368c140a7f5c5cd71c32b009653#490cb57ea6100afc3bc4437f6ef981e2 - Skip
[14:57:52] gettext/0.22.5#909a6ca9b6d4062e9b6ccf25c8461cda:42cdd0ebc69c5b84e47f600e0096e0353f036885#ef88b0277576949bd10af0467ff1c9d4 - Cache
[14:57:52] gnu-config/cci.20210814#69fde734e1a46fd1655b4af46ab40945:da39a3ee5e6b4b0d3255bfef95601890afd80709#22618e30bd9e326eb95e824dc90cc860 - Skip
[14:57:52] libiconv/1.17#1ae2f60ab5d08de1643a22a81b360c59:405e382fa7fb6368c140a7f5c5cd71c32b009653#9a42f5a432bf2128469c4fbec768d9bc - Skip
[14:57:52] libtool/2.4.7#a182d7ce8d4c346a19dbd4a5d532ef68:405e382fa7fb6368c140a7f5c5cd71c32b009653#2d183ddca682d6abdc80dbd4ae0f5c8b - Skip
[14:57:52] m4/1.4.19#b38ced39a01e31fef5435bc634461fd2:617cae191537b47386c088e07b1822d8606b7e67#af3bb664b82c4f616d3146625c5b4bd5 - Skip
[14:57:52] meson/1.2.2#21b73818ba96d9eea465b310b5bbc993:da39a3ee5e6b4b0d3255bfef95601890afd80709#97f4a23dd2d942f83e5344b1ca496ce7 - Skip
[14:57:52] ninja/1.12.1#fd583651bf0c6a901943495d49878803:617cae191537b47386c088e07b1822d8606b7e67#64e4f9c8e6ed8c3448c93db771b94ecc - Skip
[14:57:52] pkgconf/2.1.0#27f44583701117b571307cf5b5fe5605:df7e47c8f0b96c79c977dd45ec51a050d8380273#0e4a349206e0319ddfe0a13932c26b03 - Skip
[14:57:52] protobuf/3.21.12#d927114e28de9f4691a6bbcdd9a529d1:a8a4bf2f8ee5d8730973141eef8e7d1e0cce7cdb#af0ab4b702b050a6c6c48f6eac6ff033 - Skip
[14:57:52] zlib/1.3.1#b8bc2603263cf7eccbd6e17e66b0ed76:405e382fa7fb6368c140a7f5c5cd71c32b009653#695eacd1030489a6a96b412dd00919d9 - Skip
[14:57:52] 
[14:57:52] ======== Installing packages ========
[14:57:52] clipper/6.4.2@ultimaker/stable: Already installed! (1 of 21)
[14:57:52] ctre/3.7.2: Already installed! (2 of 21)
[14:57:52] cura_binary_data/5.11.0-alpha.0@ultimaker/testing: Already installed! (3 of 21)
[14:57:52] cura_resources/5.11.0-alpha.0@ultimaker/testing: Already installed! (4 of 21)
[14:57:52] fdm_materials/5.11.0-alpha.0@ultimaker/testing: Already installed! (5 of 21)
[14:57:52] fmt/11.1.3: Already installed! (6 of 21)
[14:57:52] nlohmann_json/3.11.2: Already installed! (7 of 21)
[14:57:52] nlopt/2.7.1: Already installed! (8 of 21)
[14:57:52] range-v3/0.12.0: Already installed! (9 of 21)
[14:57:52] savitar/5.11.0-alpha.0: Already installed! (10 of 21)
[14:57:52] arcus/5.10.0: Already installed! (11 of 21)
[14:57:52] gettext/0.22.5: Already installed! (12 of 21)
[14:57:52] gettext/0.22.5: WARN: The use of 'unix_path_legacy_compat' is deprecated in Conan 2.0 and does not perform path conversions. This is retained for compatibility with Conan 1.x and will be removed in a future version.
[14:57:52] gettext/0.22.5: WARN: The use of 'unix_path_legacy_compat' is deprecated in Conan 2.0 and does not perform path conversions. This is retained for compatibility with Conan 1.x and will be removed in a future version.
[14:57:52] cpython/3.12.2: Already installed! (13 of 21)
[14:57:52] cpython/3.12.2: Appending PATH environment variable: /Users/<USER>/.conan2/p/b/cpyth58b7a2dcfc351/p/bin
[14:57:52] cpython/3.12.2: Appending PYTHON environment variable: /Users/<USER>/.conan2/p/b/cpyth58b7a2dcfc351/p/bin/python3.12
[14:57:52] cpython/3.12.2: Setting PYTHON_ROOT environment variable: /Users/<USER>/.conan2/p/b/cpyth58b7a2dcfc351/p
[14:57:52] nest2d/5.10.0: Already installed! (14 of 21)
[14:57:52] spdlog/1.15.1: Already installed! (15 of 21)
[14:57:52] pyarcus/5.10.0: Already installed! (16 of 21)
[14:57:52] pysavitar/5.11.0-alpha.0: Already installed! (17 of 21)
[14:57:52] curaengine/5.11.0-alpha.0@ultimaker/testing: Already installed! (18 of 21)
[14:57:52] dulcificum/5.10.0: Already installed! (19 of 21)
[14:57:52] pynest2d/5.10.0: Already installed! (20 of 21)
[14:57:52] uranium/5.11.0-alpha.0@ultimaker/testing: Already installed! (21 of 21)
[14:57:52] WARN: deprecated: Usage of deprecated Conan 1.X features that will be removed in Conan 2.X:
[14:57:52] WARN: deprecated:     'cpp_info.names' used in: nlopt/2.7.1, range-v3/0.12.0
[14:57:52] WARN: deprecated:     'env_info' used in: cpython/3.12.2, gettext/0.22.5
[14:57:52] WARN: deprecated:     'user_info' used in: cpython/3.12.2
[14:57:52] 
[14:57:52] ======== Finalizing install (deploy, generators) ========
[14:57:52] conanfile.py (cura/5.11.0-alpha.0): Writing generators to /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators
[14:57:52] conanfile.py (cura/5.11.0-alpha.0): Generator 'VirtualPythonEnv' calling 'generate()'
[14:57:52] conanfile.py (cura/5.11.0-alpha.0): Using Python interpreter '/Users/<USER>/.conan2/p/b/cpyth58b7a2dcfc351/p/bin/python3.12' to create Virtual Environment in '/Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/cura_venv'
[14:57:54] Requirement already satisfied: pip in ./cura_venv/lib/python3.12/site-packages (25.1.1)
[14:57:56] Requirement already satisfied: wheel in ./cura_venv/lib/python3.12/site-packages (0.37.1)
[14:57:56] Requirement already satisfied: setuptools in ./cura_venv/lib/python3.12/site-packages (75.6.0)
[14:57:56] conanfile.py (cura/5.11.0-alpha.0): Generating pip requirements file at '/Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_basic.txt'
[14:57:56] conanfile.py (cura/5.11.0-alpha.0): Generating pip requirements file at '/Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt'
[14:57:56] conanfile.py (cura/5.11.0-alpha.0): Generating pip requirements file at '/Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_dev_basic.txt'
[14:57:56] conanfile.py (cura/5.11.0-alpha.0): Generating pip requirements file at '/Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_installer_basic.txt'
[14:57:56] conanfile.py (cura/5.11.0-alpha.0): Generating pip requirements summary at '/Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_summary.yml'
[14:57:56] conanfile.py (cura/5.11.0-alpha.0): Installing pip requirements from /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_basic.txt
[14:57:56] Collecting charon@ git+https://github.com/ultimaker/libcharon@master/s-line#egg=charon (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_basic.txt (line 1))
[14:57:56] Cloning https://github.com/ultimaker/libcharon (to revision master/s-line) to /private/var/folders/qc/jnt15c412ls9zryfnnd7bc_r0000gn/T/pip-install-nfhep9n8/charon_4f93c4384fc040edb8b8b0809477f671
[14:57:56] Running command git clone --filter=blob:none --quiet https://github.com/ultimaker/libcharon /private/var/folders/qc/jnt15c412ls9zryfnnd7bc_r0000gn/T/pip-install-nfhep9n8/charon_4f93c4384fc040edb8b8b0809477f671
[14:58:02] Running command git checkout -q b275d32cd2be7261194b6770cbd524717332e674
[14:58:04] Resolved https://github.com/ultimaker/libcharon to commit b275d32cd2be7261194b6770cbd524717332e674
[14:58:04] Preparing metadata (setup.py): started
[14:58:04] Preparing metadata (setup.py): finished with status 'done'
[14:58:04] conanfile.py (cura/5.11.0-alpha.0): Installing pip requirements from /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt
[14:58:04] Requirement already satisfied: certifi==2023.5.7 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 1)) (2023.5.7)
[14:58:04] Requirement already satisfied: zeroconf==0.31.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 2)) (0.31.0)
[14:58:04] Requirement already satisfied: importlib-metadata==4.10.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 3)) (4.10.0)
[14:58:04] Requirement already satisfied: trimesh==3.9.36 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 4)) (3.9.36)
[14:58:04] Requirement already satisfied: setuptools==75.6.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 5)) (75.6.0)
[14:58:04] Requirement already satisfied: sentry-sdk==0.13.5 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 6)) (0.13.5)
[14:58:04] Requirement already satisfied: pyserial==3.4 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 7)) (3.4)
[14:58:04] Requirement already satisfied: chardet==3.0.4 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 8)) (3.0.4)
[14:58:04] Requirement already satisfied: idna==2.8 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 9)) (2.8)
[14:58:04] Requirement already satisfied: attrs==21.3.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 10)) (21.3.0)
[14:58:04] Requirement already satisfied: requests==2.32.3 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 11)) (2.32.3)
[14:58:04] Requirement already satisfied: twisted==21.2.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 12)) (21.2.0)
[14:58:04] Requirement already satisfied: constantly==15.1.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 13)) (15.1.0)
[14:58:04] Requirement already satisfied: hyperlink==21.0.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 14)) (21.0.0)
[14:58:04] Requirement already satisfied: incremental==22.10.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 15)) (22.10.0)
[14:58:04] Requirement already satisfied: zope.interface==5.4.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 16)) (5.4.0)
[14:58:04] Requirement already satisfied: automat==20.2.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 17)) (20.2.0)
[14:58:04] Requirement already satisfied: shapely==2.0.6 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 18)) (2.0.6)
[14:58:04] Requirement already satisfied: cython==0.29.26 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 19)) (0.29.26)
[14:58:04] Requirement already satisfied: pybind11==2.6.2 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 20)) (2.6.2)
[14:58:04] Requirement already satisfied: wheel==0.37.1 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 21)) (0.37.1)
[14:58:04] Requirement already satisfied: ifaddr==0.1.7 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 22)) (0.1.7)
[14:58:04] Requirement already satisfied: pycparser==2.22 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 23)) (2.22)
[14:58:04] Requirement already satisfied: zipp==3.5.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 24)) (3.5.0)
[14:58:04] Requirement already satisfied: urllib3==2.2.3 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 25)) (2.2.3)
[14:58:04] Requirement already satisfied: jeepney==0.8.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 26)) (0.8.0)
[14:58:04] Requirement already satisfied: SecretStorage==3.3.3 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 27)) (3.3.3)
[14:58:04] Requirement already satisfied: keyring==25.5.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 28)) (25.5.0)
[14:58:04] Requirement already satisfied: jaraco.classes==3.4.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 29)) (3.4.0)
[14:58:04] Requirement already satisfied: jaraco.functools==4.1.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 30)) (4.1.0)
[14:58:04] Requirement already satisfied: jaraco.context==6.0.1 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 31)) (6.0.1)
[14:58:04] Requirement already satisfied: more_itertools==10.5.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 32)) (10.5.0)
[14:58:04] Requirement already satisfied: charset-normalizer==2.1.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 33)) (2.1.0)
[14:58:04] Requirement already satisfied: pynavlib==0.9.4 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 34)) (0.9.4)
[14:58:04] Requirement already satisfied: numpy==1.26.4 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 35)) (1.26.4)
[14:58:04] Requirement already satisfied: PyQt6==6.6.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 36)) (6.6.0)
[14:58:04] Requirement already satisfied: PyQt6-Qt6==6.6.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 37)) (6.6.0)
[14:58:04] Requirement already satisfied: PyQt6-sip==13.6.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 38)) (13.6.0)
[14:58:04] Requirement already satisfied: cffi==1.17.1 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 39)) (1.17.1)
[14:58:04] Requirement already satisfied: colorlog==6.6.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 40)) (6.6.0)
[14:58:04] Requirement already satisfied: cryptography==44.0.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 41)) (44.0.0)
[14:58:04] Requirement already satisfied: mypy==0.931 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 42)) (0.931)
[14:58:04] Requirement already satisfied: mypy-extensions==0.4.3 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 43)) (0.4.3)
[14:58:04] Requirement already satisfied: networkx==2.6.2 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 44)) (2.6.2)
[14:58:04] Requirement already satisfied: numpy-stl==2.10.1 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 45)) (2.10.1)
[14:58:04] Requirement already satisfied: pyclipper==1.3.0.post5 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 46)) (1.3.0.post5)
[14:58:04] Requirement already satisfied: python-utils==2.3.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 47)) (2.3.0)
[14:58:04] Requirement already satisfied: scipy==1.11.3 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 48)) (1.11.3)
[14:58:04] Requirement already satisfied: six==1.16.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 49)) (1.16.0)
[14:58:04] Requirement already satisfied: tomli==2.0.1 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 50)) (2.0.1)
[14:58:04] Requirement already satisfied: typing-extensions==4.3.0 in ./cura_venv/lib/python3.12/site-packages (from -r /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/pip_requirements_core_hashes.txt (line 51)) (4.3.0)
[14:58:04] conanfile.py (cura/5.11.0-alpha.0): Calling generate()
[14:58:04] conanfile.py (cura/5.11.0-alpha.0): Generators folder: /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators
[14:58:05] conanfile.py (cura/5.11.0-alpha.0): Write CuraVersion.py to /Users/<USER>/PycharmProjects/CuraProject/Cura
[14:58:05] conanfile.py (cura/5.11.0-alpha.0): Collecting conan installs
[14:58:05] conanfile.py (cura/5.11.0-alpha.0): Collecting python installs
[14:58:05] conanfile.py (cura/5.11.0-alpha.0): RUN: python collect_python_installs.py
[14:58:05] 
[14:58:05] conanfile.py (cura/5.11.0-alpha.0): Retrieving license for cura
[14:58:42] conanfile.py (cura/5.11.0-alpha.0): Retrieving license for conan
[14:58:55] conanfile.py (cura/5.11.0-alpha.0): Retrieving license for cura_resources
[14:59:29] conanfile.py (cura/5.11.0-alpha.0): Retrieving license for uranium
[14:59:37] conanfile.py (cura/5.11.0-alpha.0): Retrieving license for Qt
