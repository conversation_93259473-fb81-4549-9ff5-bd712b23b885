#!/usr/bin/env python3
"""
Quick Test Script for Cura MacOS Development Environment

This script performs a quick test to verify that all components are ready
for building the MacOS package with custom CuraEngine.
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Quick test of the development environment."""
    script_dir = Path(__file__).parent.absolute()
    project_root = script_dir.parent
    
    print("🧪 Quick Test for Cura MacOS Development Environment")
    print("=" * 60)
    print(f"📍 Script directory: {script_dir}")
    print(f"📍 Project root: {project_root}")
    
    # Test CuraEngine configuration
    print("\n🔍 Testing CuraEngine configuration...")
    try:
        sys.path.insert(0, str(script_dir))
        from cura_engine_config import CuraEngineConfig
        
        platform_info = CuraEngineConfig.get_platform_info()
        print(f"  Platform: {platform_info['platform']}")
        print(f"  Architecture: {platform_info['architecture']}")
        
        found_engine = CuraEngineConfig.find_curaengine()
        if found_engine:
            print(f"  ✅ Found CuraEngine: {found_engine}")
            
            # Test if it's executable
            if os.access(found_engine, os.X_OK):
                print("  ✅ CuraEngine is executable")
                
                # Try to get version
                try:
                    result = subprocess.run([found_engine, "--version"], 
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        version = result.stdout.strip() or "Unknown"
                        print(f"  ✅ CuraEngine version: {version}")
                    else:
                        print("  ⚠️  Could not get CuraEngine version")
                except Exception as e:
                    print(f"  ⚠️  Version check failed: {e}")
            else:
                print("  ❌ CuraEngine is not executable")
                return False
        else:
            print("  ❌ CuraEngine not found")
            return False
            
    except ImportError as e:
        print(f"  ❌ Failed to import configuration: {e}")
        return False
    
    # Test required tools
    print("\n🛠️  Testing required tools...")
    tools = {
        "conan": ["conan", "--version"],
        "python3": ["python3", "--version"],
        "msgfmt": ["msgfmt", "--version"],
        "cmake": ["cmake", "--version"],
        "git": ["git", "--version"],
        "hdiutil": ["hdiutil", "help"]  # hdiutil doesn't have --version
    }
    
    missing_tools = []
    for tool_name, cmd in tools.items():
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                version_line = result.stdout.split('\n')[0]
                print(f"  ✅ {tool_name}: {version_line}")
            else:
                print(f"  ❌ {tool_name} not working")
                missing_tools.append(tool_name)
        except FileNotFoundError:
            print(f"  ❌ {tool_name} not found")
            missing_tools.append(tool_name)
        except Exception as e:
            print(f"  ❌ {tool_name} check failed: {e}")
            missing_tools.append(tool_name)
    
    # Test translation system
    print("\n🌐 Testing translation system...")
    i18n_dir = project_root / "resources" / "i18n"
    if i18n_dir.exists():
        lang_dirs = [d for d in i18n_dir.iterdir() if d.is_dir()]
        mo_files = list(i18n_dir.glob("**/*.mo"))
        print(f"  ✅ Found {len(lang_dirs)} language directories")
        print(f"  ✅ Found {len(mo_files)} compiled translation files")
    else:
        print("  ❌ Translation directory not found")
        return False
    
    # Test project structure
    print("\n📁 Testing project structure...")
    required_files = [
        "conanfile.py",
        "resources/i18n",
        "plugins/CuraEngineBackend",
    ]
    
    for file_path in required_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path}")
            return False
    
    # Summary
    print("\n📊 Test Summary:")
    if missing_tools:
        print(f"  ⚠️  Missing tools: {', '.join(missing_tools)}")
        print("  💡 Install missing tools with: brew install <tool_name>")
    else:
        print("  ✅ All required tools are available")
    
    print(f"  ✅ CuraEngine: Ready")
    print(f"  ✅ Translation system: Ready")
    print(f"  ✅ Project structure: Ready")
    
    if not missing_tools:
        print("\n🎉 Environment is ready for building!")
        print("💡 Run the complete build with:")
        print(f"   python3 {script_dir}/build_cura_complete.py")
        return True
    else:
        print("\n⚠️  Please install missing tools before building")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
