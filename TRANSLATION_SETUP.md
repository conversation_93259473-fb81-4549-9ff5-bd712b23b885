# Cura 翻译设置指南

## 问题描述

如果您在Cura中设置了简体中文或其他语言后，界面语言没有切换，这通常是因为缺少编译后的翻译文件（.mo文件）。

## 解决方案

### 1. 编译翻译文件

运行以下命令来编译所有翻译文件：

```bash
./compile_translations.sh
```

这个脚本会：
- 扫描 `resources/i18n/` 目录下的所有 `.po` 文件
- 为每种语言创建 `LC_MESSAGES` 目录
- 将 `.po` 文件编译成 `.mo` 文件
- 显示编译结果和可用语言列表

### 2. 验证翻译文件

编译完成后，您可以检查简体中文翻译文件是否存在：

```bash
ls -la resources/i18n/zh_CN/LC_MESSAGES/
```

应该看到以下文件：
- `cura.mo` - 主要界面翻译
- `fdmprinter.def.json.mo` - 打印机定义翻译
- `fdmextruder.def.json.mo` - 挤出机定义翻译

### 3. 重启Cura

编译完翻译文件后，重启Cura应用程序。现在您应该能够：
1. 打开 Preferences（偏好设置）
2. 选择 General（常规）标签
3. 在 Language 下拉菜单中选择"简体中文"
4. 重启应用程序后界面将显示为中文

## 自动化选项

### 监视模式

如果您正在开发或更新翻译，可以使用监视模式：

```bash
./compile_translations.sh --watch
```

这将监视翻译文件的变化并自动重新编译。

### 集成到构建过程

您也可以将翻译编译集成到您的构建过程中。在构建脚本中添加：

```bash
# 编译翻译文件
./compile_translations.sh
```

## 支持的语言

当前支持的语言包括：
- 简体中文 (zh_CN)
- 繁体中文 (zh_TW)
- 英语 (en_US)
- 德语 (de_DE)
- 法语 (fr_FR)
- 西班牙语 (es_ES)
- 意大利语 (it_IT)
- 日语 (ja_JP)
- 韩语 (ko_KR)
- 荷兰语 (nl_NL)
- 葡萄牙语 (pt_PT, pt_BR)
- 俄语 (ru_RU)
- 土耳其语 (tr_TR)
- 捷克语 (cs_CZ)
- 芬兰语 (fi_FI)
- 匈牙利语 (hu_HU)
- 波兰语 (pl_PL)

## 故障排除

### 1. msgfmt 命令未找到

如果遇到 "msgfmt: command not found" 错误，请安装 gettext：

**macOS:**
```bash
brew install gettext
```

**Ubuntu/Debian:**
```bash
sudo apt-get install gettext
```

**CentOS/RHEL:**
```bash
sudo yum install gettext
```

### 2. 权限问题

确保脚本有执行权限：
```bash
chmod +x compile_translations.sh
```

### 3. 翻译仍然不显示

1. 确认 .mo 文件已正确生成
2. 重启Cura应用程序
3. 检查Cura的日志文件是否有相关错误信息

## 开发者注意事项

- 翻译文件位于 `resources/i18n/` 目录
- 每种语言都有自己的子目录（如 `zh_CN/`）
- `.po` 文件是源翻译文件
- `.mo` 文件是编译后的二进制翻译文件
- gettext 系统需要 `LC_MESSAGES` 目录结构

## 贡献翻译

如果您想改进翻译或添加新语言：
1. 编辑相应的 `.po` 文件
2. 运行 `./compile_translations.sh` 重新编译
3. 测试翻译效果
4. 提交您的更改
