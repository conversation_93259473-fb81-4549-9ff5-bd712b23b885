#!/usr/bin/env python3
"""
Quick Build Test for Cura MacOS Package

This script performs a quick test to verify that all components are ready
for building the MacOS package with custom CuraEngine.
"""

import os
import sys
import subprocess
from pathlib import Path

def test_curaengine_config():
    """Test the CuraEngine configuration."""
    print("🔍 Testing CuraEngine configuration...")
    
    try:
        from cura_engine_config import CuraEngineConfig
        
        # Test platform detection
        platform_info = CuraEngineConfig.get_platform_info()
        print(f"  Platform: {platform_info['platform']}")
        print(f"  Architecture: {platform_info['architecture']}")
        
        # Test CuraEngine detection
        found_engine = CuraEngineConfig.find_curaengine()
        if found_engine:
            print(f"  ✅ Found CuraEngine: {found_engine}")
            
            # Test if it's executable
            if os.access(found_engine, os.X_OK):
                print("  ✅ CuraEngine is executable")
                
                # Try to run it to get version
                try:
                    result = subprocess.run([found_engine, "--version"], 
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        print(f"  ✅ CuraEngine version: {result.stdout.strip()}")
                    else:
                        print(f"  ⚠️  CuraEngine version check failed: {result.stderr}")
                except subprocess.TimeoutExpired:
                    print("  ⚠️  CuraEngine version check timed out")
                except Exception as e:
                    print(f"  ⚠️  CuraEngine version check error: {e}")
            else:
                print("  ❌ CuraEngine is not executable")
                return False
        else:
            print("  ❌ CuraEngine not found")
            return False
            
        return True
        
    except ImportError as e:
        print(f"  ❌ Failed to import configuration: {e}")
        return False

def test_translations():
    """Test translation compilation."""
    print("🌐 Testing translation compilation...")
    
    # Check if translation script exists
    script_path = Path("compile_translations.sh")
    if not script_path.exists():
        print("  ❌ Translation script not found")
        return False
    
    # Check if it's executable
    if not os.access(script_path, os.X_OK):
        print("  ⚠️  Making translation script executable...")
        os.chmod(script_path, 0o755)
    
    # Check for some translation files
    i18n_dir = Path("resources/i18n")
    if not i18n_dir.exists():
        print("  ❌ Translation directory not found")
        return False
    
    # Count available languages
    lang_dirs = [d for d in i18n_dir.iterdir() if d.is_dir()]
    print(f"  ✅ Found {len(lang_dirs)} language directories")
    
    # Check for compiled translations
    mo_files = list(i18n_dir.glob("**/*.mo"))
    print(f"  ✅ Found {len(mo_files)} compiled translation files")
    
    return True

def test_conan_setup():
    """Test Conan setup."""
    print("🔧 Testing Conan setup...")
    
    # Check if conan is available
    try:
        result = subprocess.run(["conan", "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"  ✅ Conan version: {result.stdout.strip()}")
        else:
            print("  ❌ Conan not working properly")
            return False
    except FileNotFoundError:
        print("  ❌ Conan not found")
        return False
    except subprocess.TimeoutExpired:
        print("  ❌ Conan command timed out")
        return False
    
    # Check conanfile.py
    conanfile = Path("conanfile.py")
    if not conanfile.exists():
        print("  ❌ conanfile.py not found")
        return False
    
    print("  ✅ conanfile.py found")
    return True

def test_python_environment():
    """Test Python environment."""
    print("🐍 Testing Python environment...")
    
    print(f"  Python version: {sys.version}")
    print(f"  Python executable: {sys.executable}")
    
    # Check for required modules
    required_modules = ["PyQt6", "sip"]
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"  ✅ {module} available")
        except ImportError:
            print(f"  ❌ {module} missing")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"  ⚠️  Missing modules: {', '.join(missing_modules)}")
        print("  💡 You may need to install them or set up a proper environment")
    
    return len(missing_modules) == 0

def test_build_tools():
    """Test build tools."""
    print("🛠️  Testing build tools...")
    
    tools = {
        "cmake": ["cmake", "--version"],
        "make": ["make", "--version"],
        "msgfmt": ["msgfmt", "--version"],
        "git": ["git", "--version"]
    }
    
    all_available = True
    
    for tool_name, command in tools.items():
        try:
            result = subprocess.run(command, capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                version_line = result.stdout.split('\n')[0]
                print(f"  ✅ {tool_name}: {version_line}")
            else:
                print(f"  ❌ {tool_name} not working")
                all_available = False
        except FileNotFoundError:
            print(f"  ❌ {tool_name} not found")
            all_available = False
        except subprocess.TimeoutExpired:
            print(f"  ❌ {tool_name} command timed out")
            all_available = False
    
    return all_available

def create_simple_build_script():
    """Create a simple build script for testing."""
    print("📝 Creating simple build script...")
    
    script_content = '''#!/bin/bash
# Simple Cura Build Script for Testing

set -e

echo "🚀 Starting simple Cura build..."

# Copy custom CuraEngine
echo "📦 Copying custom CuraEngine..."
cp "/Users/<USER>/PycharmProjects/CuraProject/CuraEngine/build/Release/CuraEngine" ./CuraEngine
chmod +x ./CuraEngine

# Compile translations
echo "🌐 Compiling translations..."
./compile_translations.sh

# Create a simple test to verify CuraEngine integration
echo "🔍 Testing CuraEngine integration..."
python3 -c "
import sys
sys.path.insert(0, '.')
from cura_engine_config import CuraEngineConfig
engine = CuraEngineConfig.find_curaengine()
print(f'Found CuraEngine: {engine}')
if engine:
    print('✅ CuraEngine integration test passed')
else:
    print('❌ CuraEngine integration test failed')
    sys.exit(1)
"

echo "✅ Simple build test completed successfully!"
'''
    
    script_path = Path("simple_build_test.sh")
    script_path.write_text(script_content)
    os.chmod(script_path, 0o755)
    
    print(f"  ✅ Created: {script_path}")
    return script_path

def main():
    """Main test function."""
    print("🧪 Cura MacOS Build Readiness Test")
    print("=" * 50)
    
    tests = [
        ("CuraEngine Configuration", test_curaengine_config),
        ("Translation System", test_translations),
        ("Conan Setup", test_conan_setup),
        ("Python Environment", test_python_environment),
        ("Build Tools", test_build_tools),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"  ❌ Test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Ready to build Cura MacOS package.")
        
        # Create simple build script
        simple_script = create_simple_build_script()
        print(f"\n💡 You can run a simple build test with: ./{simple_script}")
        
    else:
        print("⚠️  Some tests failed. Please fix the issues before building.")
        
        # Provide suggestions
        print("\n💡 Suggestions:")
        if not results.get("CuraEngine Configuration", True):
            print("  - Ensure CuraEngine is compiled in Release mode")
            print("  - Check the path in cura_engine_config.py")
        
        if not results.get("Conan Setup", True):
            print("  - Install Conan: pip install conan")
        
        if not results.get("Build Tools", True):
            print("  - Install missing tools with Homebrew")
            print("  - brew install cmake gettext")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
