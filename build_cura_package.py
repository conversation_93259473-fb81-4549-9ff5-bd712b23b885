#!/usr/bin/env python3
"""
Final Cura MacOS Package Builder

This script builds a complete MacOS installation package for Cura with your custom CuraEngine.
It handles all the necessary steps including dependency management, compilation, and packaging.
"""

import os
import sys
import subprocess
import shutil
import tempfile
from pathlib import Path
import argparse

class CuraPackageBuilder:
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.custom_curaengine = Path("/Users/<USER>/PycharmProjects/CuraProject/CuraEngine/build/Release/CuraEngine")
        self.build_dir = self.project_root / "build_package"
        self.dist_dir = self.project_root / "dist_package"
        
    def log(self, level, message):
        """Log a message with color coding."""
        colors = {
            'INFO': '\033[0;34m',
            'SUCCESS': '\033[0;32m',
            'WARNING': '\033[1;33m',
            'ERROR': '\033[0;31m',
            'NC': '\033[0m'
        }
        print(f"{colors.get(level, '')}{message}{colors['NC']}")
    
    def run_command(self, cmd, cwd=None, timeout=300):
        """Run a command and return the result."""
        try:
            if cwd is None:
                cwd = self.project_root
            
            self.log('INFO', f"Running: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
            
            result = subprocess.run(
                cmd, 
                cwd=cwd, 
                capture_output=True, 
                text=True, 
                timeout=timeout,
                shell=isinstance(cmd, str)
            )
            
            if result.returncode != 0:
                self.log('ERROR', f"Command failed with return code {result.returncode}")
                self.log('ERROR', f"stdout: {result.stdout}")
                self.log('ERROR', f"stderr: {result.stderr}")
                return False
            
            return True
            
        except subprocess.TimeoutExpired:
            self.log('ERROR', f"Command timed out after {timeout} seconds")
            return False
        except Exception as e:
            self.log('ERROR', f"Command failed with exception: {e}")
            return False
    
    def check_prerequisites(self):
        """Check if all prerequisites are met."""
        self.log('INFO', "Checking prerequisites...")
        
        # Check custom CuraEngine
        if not self.custom_curaengine.exists():
            self.log('ERROR', f"Custom CuraEngine not found: {self.custom_curaengine}")
            return False
        
        if not os.access(self.custom_curaengine, os.X_OK):
            self.log('ERROR', f"Custom CuraEngine is not executable: {self.custom_curaengine}")
            return False
        
        self.log('SUCCESS', f"Custom CuraEngine found: {self.custom_curaengine}")
        
        # Check required tools
        required_tools = ['conan', 'python3', 'msgfmt', 'cmake']
        for tool in required_tools:
            if not shutil.which(tool):
                self.log('ERROR', f"Required tool not found: {tool}")
                return False
            self.log('SUCCESS', f"Found {tool}")
        
        return True
    
    def setup_environment(self):
        """Set up the build environment."""
        self.log('INFO', "Setting up build environment...")
        
        # Create directories
        self.build_dir.mkdir(exist_ok=True)
        self.dist_dir.mkdir(exist_ok=True)
        
        # Copy custom CuraEngine to project root
        dest_curaengine = self.project_root / "CuraEngine"
        if dest_curaengine.exists():
            dest_curaengine.unlink()
        
        shutil.copy2(self.custom_curaengine, dest_curaengine)
        os.chmod(dest_curaengine, 0o755)
        
        self.log('SUCCESS', f"Copied CuraEngine to: {dest_curaengine}")
        return True
    
    def compile_translations(self):
        """Compile translation files."""
        self.log('INFO', "Compiling translations...")
        
        script_path = self.project_root / "compile_translations.sh"
        if not script_path.exists():
            self.log('ERROR', "Translation script not found")
            return False
        
        # Make sure it's executable
        os.chmod(script_path, 0o755)
        
        return self.run_command([str(script_path)])
    
    def install_dependencies(self):
        """Install Conan dependencies."""
        self.log('INFO', "Installing Conan dependencies...")
        
        # Detect architecture
        arch = "armv8" if "arm64" in os.uname().machine.lower() else "x86_64"
        self.log('INFO', f"Building for architecture: {arch}")
        
        cmd = [
            "conan", "install", str(self.project_root),
            "--build=missing",
            "-s", "build_type=Release",
            "-s", f"arch={arch}",
            "-s", "os=Macos"
        ]
        
        return self.run_command(cmd, cwd=self.build_dir)
    
    def build_cura(self):
        """Build Cura."""
        self.log('INFO', "Building Cura...")
        
        cmd = ["conan", "build", str(self.project_root)]
        return self.run_command(cmd, cwd=self.build_dir)
    
    def create_package(self):
        """Create the distribution package."""
        self.log('INFO', "Creating distribution package...")
        
        # Deploy with Conan
        cmd = ["conan", "deploy", str(self.project_root), "--deployer-folder", str(self.dist_dir)]
        if not self.run_command(cmd, cwd=self.build_dir):
            return False
        
        # Find PyInstaller spec file
        spec_files = list(self.dist_dir.glob("*.spec"))
        if not spec_files:
            self.log('ERROR', "No PyInstaller spec file found")
            return False
        
        spec_file = spec_files[0]
        self.log('INFO', f"Using spec file: {spec_file}")
        
        # Run PyInstaller
        cmd = ["pyinstaller", "--clean", "--noconfirm", str(spec_file)]
        return self.run_command(cmd, cwd=self.dist_dir)
    
    def verify_package(self):
        """Verify the created package."""
        self.log('INFO', "Verifying package...")
        
        # Find .app bundle
        app_bundles = list(self.dist_dir.glob("**/UltiMaker-Cura.app"))
        if not app_bundles:
            app_bundles = list(self.dist_dir.glob("**/*.app"))
        
        if not app_bundles:
            self.log('ERROR', "No .app bundle found")
            return False
        
        app_bundle = app_bundles[0]
        self.log('SUCCESS', f"Found app bundle: {app_bundle}")
        
        # Check for CuraEngine in bundle
        possible_paths = [
            app_bundle / "Contents" / "MacOS" / "CuraEngine",
            app_bundle / "Contents" / "Resources" / "CuraEngine",
            app_bundle / "Contents" / "Frameworks" / "CuraEngine"
        ]
        
        curaengine_found = None
        for path in possible_paths:
            if path.exists():
                curaengine_found = path
                break
        
        if curaengine_found:
            self.log('SUCCESS', f"CuraEngine found in bundle: {curaengine_found}")
            
            # Verify it's our custom version
            original_size = self.custom_curaengine.stat().st_size
            bundle_size = curaengine_found.stat().st_size
            
            if original_size == bundle_size:
                self.log('SUCCESS', "CuraEngine size matches custom version")
            else:
                self.log('WARNING', f"CuraEngine size differs: original={original_size}, bundle={bundle_size}")
        else:
            self.log('ERROR', "CuraEngine not found in bundle")
            return False
        
        return True
    
    def create_dmg(self, version="5.11.0-alpha.0"):
        """Create DMG installer."""
        self.log('INFO', "Creating DMG installer...")
        
        # Find app bundle
        app_bundles = list(self.dist_dir.glob("**/UltiMaker-Cura.app"))
        if not app_bundles:
            app_bundles = list(self.dist_dir.glob("**/*.app"))
        
        if not app_bundles:
            self.log('ERROR', "No .app bundle found for DMG creation")
            return False
        
        app_bundle = app_bundles[0]
        
        # Use existing MacOS build script if available
        build_script = self.project_root / "packaging" / "MacOS" / "build_macos.py"
        
        if build_script.exists():
            arch = "ARM64" if "arm64" in os.uname().machine.lower() else "X64"
            filename = f"UltiMaker-Cura-{version}-MacOS-{arch}"
            
            cmd = [
                sys.executable, str(build_script),
                "--source_path", str(self.project_root),
                "--dist_path", str(self.dist_dir),
                "--cura_conan_version", version,
                "--filename", filename,
                "--build_dmg",
                "--app_name", app_bundle.stem
            ]
            
            if self.run_command(cmd):
                dmg_file = self.dist_dir / f"{filename}.dmg"
                if dmg_file.exists():
                    self.log('SUCCESS', f"DMG created: {dmg_file}")
                    return True
        
        # Fallback: create simple DMG
        self.log('INFO', "Creating simple DMG...")
        
        arch = "ARM64" if "arm64" in os.uname().machine.lower() else "X64"
        dmg_name = f"UltiMaker-Cura-{version}-MacOS-{arch}.dmg"
        dmg_path = self.dist_dir / dmg_name
        
        # Create temporary directory for DMG contents
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Copy app bundle to temp directory
            shutil.copytree(app_bundle, temp_path / app_bundle.name)
            
            # Create DMG
            cmd = [
                "hdiutil", "create",
                "-volname", f"UltiMaker Cura {version}",
                "-srcfolder", str(temp_path),
                "-ov", "-format", "UDZO",
                str(dmg_path)
            ]
            
            if self.run_command(cmd):
                self.log('SUCCESS', f"Simple DMG created: {dmg_path}")
                return True
        
        return False
    
    def build(self, create_dmg=True, version="5.11.0-alpha.0"):
        """Main build process."""
        self.log('INFO', "🚀 Starting Cura MacOS package build")
        self.log('INFO', f"Project root: {self.project_root}")
        self.log('INFO', f"Custom CuraEngine: {self.custom_curaengine}")
        
        steps = [
            ("Prerequisites", self.check_prerequisites),
            ("Environment Setup", self.setup_environment),
            ("Translation Compilation", self.compile_translations),
            ("Dependency Installation", self.install_dependencies),
            ("Cura Build", self.build_cura),
            ("Package Creation", self.create_package),
            ("Package Verification", self.verify_package),
        ]
        
        if create_dmg:
            steps.append(("DMG Creation", lambda: self.create_dmg(version)))
        
        for step_name, step_func in steps:
            self.log('INFO', f"📋 {step_name}...")
            if not step_func():
                self.log('ERROR', f"❌ {step_name} failed")
                return False
            self.log('SUCCESS', f"✅ {step_name} completed")
        
        self.log('SUCCESS', "🎉 Cura MacOS package build completed successfully!")
        self.log('INFO', f"📁 Output directory: {self.dist_dir}")
        
        # List created files
        self.log('INFO', "📦 Created files:")
        for pattern in ["*.dmg", "*.app"]:
            for file in self.dist_dir.glob(f"**/{pattern}"):
                self.log('INFO', f"  - {file}")
        
        return True

def main():
    parser = argparse.ArgumentParser(description="Build Cura MacOS package with custom CuraEngine")
    parser.add_argument("--no-dmg", action="store_true", help="Skip DMG creation")
    parser.add_argument("--version", default="5.11.0-alpha.0", help="Cura version")
    
    args = parser.parse_args()
    
    builder = CuraPackageBuilder()
    success = builder.build(create_dmg=not args.no_dmg, version=args.version)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
