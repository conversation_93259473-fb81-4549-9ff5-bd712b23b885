#!/usr/bin/env python3
"""
Cross-platform CuraEngine Configuration

This module provides platform-specific configuration for CuraEngine paths
to ensure compatibility between MacOS and Windows development environments.

The configuration handles:
- Different file naming conventions (CuraEngine vs CuraEngine.exe)
- Different path separators (/ vs \\)
- Case sensitivity differences
- Development vs production environments
"""

import os
import sys
from pathlib import Path
from typing import List, Optional

# Try to import Platform from UM, fallback to manual detection
try:
    from UM.Platform import Platform
except ImportError:
    # Fallback platform detection
    class Platform:
        @staticmethod
        def isOSX():
            return sys.platform == "darwin"

        @staticmethod
        def isWindows():
            return sys.platform.startswith("win")

        @staticmethod
        def isLinux():
            return sys.platform.startswith("linux")

class CuraEngineConfig:
    """Configuration manager for CuraEngine paths across platforms."""
    
    # Base project paths - adjust these for your development setup
    MACOS_PROJECT_ROOT = Path("/Users/<USER>/PycharmProjects/CuraProject")
    WINDOWS_PROJECT_ROOT = Path("C:/Users/<USER>/PycharmProjects/CuraProject")  # Adjust as needed
    
    @classmethod
    def get_custom_curaengine_paths(cls) -> List[str]:
        """
        Get platform-specific custom CuraEngine paths.
        
        Returns:
            List of potential CuraEngine paths in order of priority
        """
        paths = []
        
        if Platform.isOSX():
            # MacOS paths
            base_path = cls.MACOS_PROJECT_ROOT
            paths.extend([
                str(base_path / "CuraEngine" / "build" / "Release" / "CuraEngine"),  # CMake Release build
                str(base_path / "CuraEngine" / "build" / "Debug" / "CuraEngine"),    # CMake Debug build
                str(base_path / "CuraEngine" / "build" / "CuraEngine"),              # CMake default build
                str(base_path / "CuraEngine" / "CuraEngine"),                        # Direct binary
                str(base_path / "CuraEngine"),                                       # Alternative location
                str(base_path / "curaengine" / "CuraEngine"),                        # Alternative naming
            ])
        elif Platform.isWindows():
            # Windows paths
            base_path = cls.WINDOWS_PROJECT_ROOT
            paths.extend([
                str(base_path / "CuraEngine" / "build" / "Release" / "CuraEngine.exe"),  # CMake Release build
                str(base_path / "CuraEngine" / "build" / "Debug" / "CuraEngine.exe"),    # CMake Debug build
                str(base_path / "CuraEngine" / "build" / "CuraEngine.exe"),              # CMake default build
                str(base_path / "CuraEngine" / "CuraEngine.exe"),                        # Direct binary
                str(base_path / "CuraEngine.exe"),                                       # Alternative location
                str(base_path / "curaengine" / "CuraEngine.exe"),                        # Alternative naming
                # Also check without .exe extension for compatibility
                str(base_path / "CuraEngine" / "build" / "Release" / "CuraEngine"),
                str(base_path / "CuraEngine" / "build" / "Debug" / "CuraEngine"),
                str(base_path / "CuraEngine" / "build" / "CuraEngine"),
                str(base_path / "CuraEngine" / "CuraEngine"),
                str(base_path / "CuraEngine"),
            ])
        elif Platform.isLinux():
            # Linux paths (similar to MacOS)
            base_path = Path.home() / "PycharmProjects" / "CuraProject"
            paths.extend([
                str(base_path / "CuraEngine" / "build" / "Release" / "CuraEngine"),  # CMake Release build
                str(base_path / "CuraEngine" / "build" / "Debug" / "CuraEngine"),    # CMake Debug build
                str(base_path / "CuraEngine" / "build" / "CuraEngine"),              # CMake default build
                str(base_path / "CuraEngine" / "CuraEngine"),                        # Direct binary
                str(base_path / "CuraEngine"),                                       # Alternative location
                str(base_path / "curaengine" / "CuraEngine"),                        # Alternative naming
            ])
        
        return paths
    
    @classmethod
    def get_standard_search_paths(cls) -> List[str]:
        """
        Get standard CuraEngine search paths for packaged installations.
        
        Returns:
            List of standard search paths
        """
        executable_name = "CuraEngine"
        if Platform.isWindows():
            executable_name += ".exe"
        
        # Import here to avoid circular imports
        try:
            from cura.CuraApplication import CuraApplication
            install_prefix = CuraApplication.getInstallPrefix()
        except ImportError:
            install_prefix = ""
        
        search_paths = [
            # Packaged application paths
            os.path.abspath(os.path.join(os.path.dirname(sys.executable), "..", "Resources")),
            os.path.abspath(os.path.dirname(sys.executable)),
            os.path.abspath(os.path.join(os.path.dirname(sys.executable), "bin")),
            os.path.abspath(os.path.join(os.path.dirname(sys.executable), "..")),
        ]
        
        if install_prefix:
            search_paths.append(os.path.join(install_prefix, "bin"))
        
        search_paths.append(os.path.dirname(os.path.abspath(sys.executable)))
        
        return search_paths
    
    @classmethod
    def find_curaengine(cls) -> Optional[str]:
        """
        Find CuraEngine executable using priority search.
        
        Returns:
            Path to CuraEngine executable or None if not found
        """
        # First, try custom development paths
        custom_paths = cls.get_custom_curaengine_paths()
        for path in custom_paths:
            if os.path.isfile(path) and os.access(path, os.X_OK):
                return path
        
        # Then try standard paths
        executable_name = "CuraEngine"
        if Platform.isWindows():
            executable_name += ".exe"
        
        standard_paths = cls.get_standard_search_paths()
        for path in standard_paths:
            engine_path = os.path.join(path, executable_name)
            if os.path.isfile(engine_path):
                return engine_path
        
        # Finally, check PATH environment variable (Linux/Unix style)
        if not Platform.isWindows():
            path_env = os.getenv("PATH", "")
            for pathdir in path_env.split(os.pathsep):
                execpath = os.path.join(pathdir, executable_name)
                if os.path.exists(execpath):
                    return execpath
        
        return None
    
    @classmethod
    def validate_curaengine(cls, path: str) -> bool:
        """
        Validate that a CuraEngine path is usable.
        
        Args:
            path: Path to CuraEngine executable
            
        Returns:
            True if valid and executable, False otherwise
        """
        if not path or not os.path.isfile(path):
            return False
        
        if not os.access(path, os.X_OK):
            return False
        
        # Additional validation could be added here
        # e.g., checking file signature, version, etc.
        
        return True
    
    @classmethod
    def get_platform_info(cls) -> dict:
        """
        Get platform information for debugging.
        
        Returns:
            Dictionary with platform information
        """
        return {
            "platform": sys.platform,
            "is_macos": Platform.isOSX(),
            "is_windows": Platform.isWindows(),
            "is_linux": Platform.isLinux(),
            "architecture": os.uname().machine if hasattr(os, 'uname') else 'unknown',
            "python_executable": sys.executable,
            "working_directory": os.getcwd(),
        }

# Convenience functions for backward compatibility
def get_custom_curaengine_path() -> Optional[str]:
    """Get the first available custom CuraEngine path."""
    paths = CuraEngineConfig.get_custom_curaengine_paths()
    for path in paths:
        if CuraEngineConfig.validate_curaengine(path):
            return path
    return None

def find_curaengine() -> Optional[str]:
    """Find CuraEngine executable."""
    return CuraEngineConfig.find_curaengine()

# Platform-specific path utilities
class PathUtils:
    """Utilities for handling cross-platform paths."""
    
    @staticmethod
    def normalize_path(path: str) -> str:
        """
        Normalize path for current platform.
        
        Args:
            path: Input path
            
        Returns:
            Normalized path
        """
        return os.path.normpath(os.path.expanduser(path))
    
    @staticmethod
    def ensure_executable(path: str) -> bool:
        """
        Ensure a file is executable.
        
        Args:
            path: Path to file
            
        Returns:
            True if successfully made executable, False otherwise
        """
        try:
            if os.path.isfile(path):
                os.chmod(path, 0o755)
                return True
        except (OSError, PermissionError):
            pass
        return False
    
    @staticmethod
    def safe_path_join(*args) -> str:
        """
        Safely join path components across platforms.
        
        Args:
            *args: Path components
            
        Returns:
            Joined path
        """
        return os.path.normpath(os.path.join(*args))

if __name__ == "__main__":
    # Test the configuration
    print("CuraEngine Configuration Test")
    print("=" * 40)
    
    # Platform info
    platform_info = CuraEngineConfig.get_platform_info()
    for key, value in platform_info.items():
        print(f"{key}: {value}")
    
    print("\nCustom CuraEngine paths:")
    custom_paths = CuraEngineConfig.get_custom_curaengine_paths()
    for i, path in enumerate(custom_paths, 1):
        exists = "✓" if os.path.exists(path) else "✗"
        executable = "✓" if os.path.isfile(path) and os.access(path, os.X_OK) else "✗"
        print(f"  {i}. {path} [exists: {exists}, executable: {executable}]")
    
    print("\nStandard search paths:")
    standard_paths = CuraEngineConfig.get_standard_search_paths()
    for i, path in enumerate(standard_paths, 1):
        exists = "✓" if os.path.exists(path) else "✗"
        print(f"  {i}. {path} [exists: {exists}]")
    
    print("\nFound CuraEngine:")
    found_engine = CuraEngineConfig.find_curaengine()
    if found_engine:
        print(f"  ✓ {found_engine}")
    else:
        print("  ✗ Not found")
