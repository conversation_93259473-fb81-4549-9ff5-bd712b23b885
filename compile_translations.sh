#!/bin/bash

# Script to compile all translation files (.po) to binary format (.mo)
# This ensures that <PERSON><PERSON> can properly load translations
#
# Usage: ./compile_translations.sh [--watch]
#   --watch: Monitor .po files for changes and auto-recompile

WATCH_MODE=false
if [ "$1" = "--watch" ]; then
    WATCH_MODE=true
fi

compile_translations() {
    echo "Compiling translation files..."

    # Find all .po files in the i18n directory
    for po_file in resources/i18n/*/*.po; do
        if [ -f "$po_file" ]; then
            # Extract language code and filename
            lang_dir=$(dirname "$po_file")
            lang_code=$(basename "$lang_dir")
            filename=$(basename "$po_file" .po)

            # Create LC_MESSAGES directory if it doesn't exist
            lc_messages_dir="$lang_dir/LC_MESSAGES"
            mkdir -p "$lc_messages_dir"

            # Compile .po to .mo
            mo_file="$lc_messages_dir/$filename.mo"

            # Check if .mo file is older than .po file or doesn't exist
            if [ ! -f "$mo_file" ] || [ "$po_file" -nt "$mo_file" ]; then
                echo "Compiling $po_file -> $mo_file"
                msgfmt "$po_file" -o "$mo_file"

                if [ $? -eq 0 ]; then
                    echo "  ✓ Successfully compiled $filename for $lang_code"
                else
                    echo "  ✗ Failed to compile $filename for $lang_code"
                fi
            fi
        fi
    done

    echo "Translation compilation completed!"
    echo ""
    echo "Available languages with compiled translations:"
    for lang_dir in resources/i18n/*/; do
        if [ -d "$lang_dir/LC_MESSAGES" ]; then
            lang_code=$(basename "$lang_dir")
            mo_count=$(find "$lang_dir/LC_MESSAGES" -name "*.mo" | wc -l)
            echo "  $lang_code ($mo_count translation files)"
        fi
    done
}

if [ "$WATCH_MODE" = true ]; then
    echo "Starting translation file watcher..."
    echo "Press Ctrl+C to stop"

    # Initial compilation
    compile_translations

    # Watch for changes (requires fswatch on macOS or inotify-tools on Linux)
    if command -v fswatch >/dev/null 2>&1; then
        fswatch -o resources/i18n/ | while read f; do
            echo "Translation files changed, recompiling..."
            compile_translations
        done
    else
        echo "fswatch not found. Install it with: brew install fswatch"
        echo "Falling back to manual compilation mode."
        compile_translations
    fi
else
    compile_translations
fi
