# Cura MacOS Package Build Guide

本指南将帮助您构建包含自定义CuraEngine的Cura MacOS安装包。

## 概述

我们已经为您创建了一套完整的构建工具，确保：
- 使用您的自定义CuraEngine (`/Users/<USER>/PycharmProjects/CuraProject/CuraEngine/build/Release/CuraEngine`)
- 跨平台兼容性（MacOS/Windows开发环境）
- 自动化的构建和打包过程
- 完整的翻译支持

## 文件说明

### 核心文件
- `build_cura_package.py` - 主要构建脚本（推荐使用）
- `cura_engine_config.py` - 跨平台CuraEngine配置管理
- `compile_translations.sh` - 翻译文件编译脚本
- `quick_build_test.py` - 构建环境测试脚本

### 辅助文件
- `build_cura_macos.sh` - Bash版本的构建脚本
- `build_macos_package.py` - 详细的Python构建脚本
- `TRANSLATION_SETUP.md` - 翻译设置指南

## 快速开始

### 1. 环境检查

首先运行环境测试：

```bash
python3 quick_build_test.py
```

这将检查：
- ✅ CuraEngine配置和可执行性
- ✅ 翻译系统
- ✅ Conan设置
- ✅ 构建工具
- ⚠️ Python环境（PyQt6等会通过Conan管理，可以忽略）

### 2. 构建安装包

运行主构建脚本：

```bash
python3 build_cura_package.py
```

或者指定版本：

```bash
python3 build_cura_package.py --version 5.11.0-custom
```

如果只想构建应用程序而不创建DMG：

```bash
python3 build_cura_package.py --no-dmg
```

### 3. 构建过程

构建脚本将执行以下步骤：

1. **Prerequisites** - 检查自定义CuraEngine和必要工具
2. **Environment Setup** - 设置构建环境，复制CuraEngine
3. **Translation Compilation** - 编译所有语言的翻译文件
4. **Dependency Installation** - 通过Conan安装依赖
5. **Cura Build** - 编译Cura应用程序
6. **Package Creation** - 创建PyInstaller包
7. **Package Verification** - 验证CuraEngine正确集成
8. **DMG Creation** - 创建MacOS安装包

## 输出文件

构建完成后，您将在 `dist_package/` 目录中找到：

- `UltiMaker-Cura-{version}-MacOS-{arch}.dmg` - MacOS安装包
- `UltiMaker-Cura.app` - 应用程序包（在dist子目录中）

## 验证安装包

### 检查CuraEngine集成

安装包创建后，脚本会自动验证：
- CuraEngine是否正确包含在应用程序包中
- 文件大小是否与您的自定义版本匹配
- 可执行权限是否正确设置

### 手动验证

您也可以手动检查：

```bash
# 查看应用程序包内容
ls -la dist_package/dist/UltiMaker-Cura.app/Contents/MacOS/

# 检查CuraEngine
file dist_package/dist/UltiMaker-Cura.app/Contents/MacOS/CuraEngine
```

## 跨平台开发注意事项

### 文件命名兼容性
- 避免使用大小写敏感的文件名
- 使用正斜杠(/)作为路径分隔符，脚本会自动转换
- 新代码应同时兼容MacOS和Windows

### CuraEngine路径配置
`cura_engine_config.py` 文件处理不同平台的路径：

```python
# MacOS路径
MACOS_PROJECT_ROOT = Path("/Users/<USER>/PycharmProjects/CuraProject")

# Windows路径（如需要，请调整）
WINDOWS_PROJECT_ROOT = Path("C:/Users/<USER>/PycharmProjects/CuraProject")
```

## 故障排除

### 常见问题

1. **CuraEngine未找到**
   ```
   ❌ Custom CuraEngine not found
   ```
   - 确保CuraEngine已编译：`cd /Users/<USER>/PycharmProjects/CuraProject/CuraEngine && cmake --build build --config Release`
   - 检查路径是否正确

2. **Conan依赖问题**
   ```
   ❌ Conan install failed
   ```
   - 更新Conan：`pip install --upgrade conan`
   - 清理缓存：`conan remove "*" --confirm`

3. **翻译编译失败**
   ```
   ❌ Translation compilation failed
   ```
   - 安装gettext：`brew install gettext`
   - 检查msgfmt是否在PATH中

4. **PyInstaller失败**
   ```
   ❌ PyInstaller failed
   ```
   - 检查.spec文件是否正确生成
   - 查看详细错误日志

### 调试模式

如果遇到问题，可以分步执行：

```bash
# 1. 只测试环境
python3 quick_build_test.py

# 2. 只编译翻译
./compile_translations.sh

# 3. 测试CuraEngine配置
python3 cura_engine_config.py

# 4. 分步构建（不创建DMG）
python3 build_cura_package.py --no-dmg
```

## 自定义配置

### 修改CuraEngine路径

如果您的CuraEngine在不同位置，编辑 `cura_engine_config.py`：

```python
# 在CuraEngineConfig类中修改路径
MACOS_PROJECT_ROOT = Path("您的项目路径")
```

### 修改构建配置

编辑 `build_cura_package.py` 中的配置：

```python
# 修改CuraEngine路径
self.custom_curaengine = Path("您的CuraEngine路径")

# 修改构建目录
self.build_dir = self.project_root / "您的构建目录"
```

## 开发工作流

### 日常开发
1. 修改Cura源代码
2. 如果修改了CuraEngine，重新编译它
3. 运行 `python3 build_cura_package.py` 创建新的安装包
4. 测试安装包

### 发布准备
1. 确保所有翻译都是最新的
2. 运行完整的构建测试
3. 创建带版本号的安装包
4. 在干净的系统上测试安装包

## 技术细节

### 构建架构
- 自动检测ARM64/x86_64架构
- 使用Conan 2.x进行依赖管理
- PyInstaller用于应用程序打包
- hdiutil用于DMG创建

### 依赖管理
- 所有Python依赖通过Conan管理
- 自定义CuraEngine优先级最高
- 自动处理Qt/PyQt6依赖

### 安全考虑
- 验证CuraEngine可执行权限
- 检查文件完整性
- 确保代码签名兼容性（如需要）

## 支持

如果遇到问题：
1. 首先运行 `python3 quick_build_test.py` 诊断环境
2. 检查构建日志中的详细错误信息
3. 确保所有依赖工具都已正确安装
4. 验证CuraEngine编译是否成功

---

**注意**: 此构建系统设计为同时支持MacOS和Windows开发环境。在Windows上开发时，请相应调整路径配置。
